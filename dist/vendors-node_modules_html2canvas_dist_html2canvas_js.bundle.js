/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk"] = self["webpackChunk"] || []).push([["vendors-node_modules_html2canvas_dist_html2canvas_js"],{

/***/ "./node_modules/html2canvas/dist/html2canvas.js":
/*!******************************************************!*\
  !*** ./node_modules/html2canvas/dist/html2canvas.js ***!
  \******************************************************/
/***/ (function(module) {

eval("/*!\n * html2canvas 1.4.1 <https://html2canvas.hertzen.com>\n * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n * Released under MIT License\n */\n(function (global, factory) {\n     true ? module.exports = factory() :\n    0;\n}(this, (function () { 'use strict';\n\n    /*! *****************************************************************************\r\n    Copyright (c) Microsoft Corporation.\r\n\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n    purpose with or without fee is hereby granted.\r\n\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n    PERFORMANCE OF THIS SOFTWARE.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise */\r\n\r\n    var extendStatics = function(d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n\r\n    function __extends(d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    }\r\n\r\n    var __assign = function() {\r\n        __assign = Object.assign || function __assign(t) {\r\n            for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n                s = arguments[i];\r\n                for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n            }\r\n            return t;\r\n        };\r\n        return __assign.apply(this, arguments);\r\n    };\r\n\r\n    function __awaiter(thisArg, _arguments, P, generator) {\r\n        function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n        return new (P || (P = Promise))(function (resolve, reject) {\r\n            function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n            function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n            function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n        });\r\n    }\r\n\r\n    function __generator(thisArg, body) {\r\n        var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n        return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n        function verb(n) { return function (v) { return step([n, v]); }; }\r\n        function step(op) {\r\n            if (f) throw new TypeError(\"Generator is already executing.\");\r\n            while (_) try {\r\n                if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n                if (y = 0, t) op = [op[0] & 2, t.value];\r\n                switch (op[0]) {\r\n                    case 0: case 1: t = op; break;\r\n                    case 4: _.label++; return { value: op[1], done: false };\r\n                    case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                    case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                    default:\r\n                        if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                        if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                        if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                        if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                        if (t[2]) _.ops.pop();\r\n                        _.trys.pop(); continue;\r\n                }\r\n                op = body.call(thisArg, _);\r\n            } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n            if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n        }\r\n    }\r\n\r\n    function __spreadArray(to, from, pack) {\r\n        if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n            if (ar || !(i in from)) {\r\n                if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n                ar[i] = from[i];\r\n            }\r\n        }\r\n        return to.concat(ar || from);\r\n    }\n\n    var Bounds = /** @class */ (function () {\n        function Bounds(left, top, width, height) {\n            this.left = left;\n            this.top = top;\n            this.width = width;\n            this.height = height;\n        }\n        Bounds.prototype.add = function (x, y, w, h) {\n            return new Bounds(this.left + x, this.top + y, this.width + w, this.height + h);\n        };\n        Bounds.fromClientRect = function (context, clientRect) {\n            return new Bounds(clientRect.left + context.windowBounds.left, clientRect.top + context.windowBounds.top, clientRect.width, clientRect.height);\n        };\n        Bounds.fromDOMRectList = function (context, domRectList) {\n            var domRect = Array.from(domRectList).find(function (rect) { return rect.width !== 0; });\n            return domRect\n                ? new Bounds(domRect.left + context.windowBounds.left, domRect.top + context.windowBounds.top, domRect.width, domRect.height)\n                : Bounds.EMPTY;\n        };\n        Bounds.EMPTY = new Bounds(0, 0, 0, 0);\n        return Bounds;\n    }());\n    var parseBounds = function (context, node) {\n        return Bounds.fromClientRect(context, node.getBoundingClientRect());\n    };\n    var parseDocumentSize = function (document) {\n        var body = document.body;\n        var documentElement = document.documentElement;\n        if (!body || !documentElement) {\n            throw new Error(\"Unable to get document size\");\n        }\n        var width = Math.max(Math.max(body.scrollWidth, documentElement.scrollWidth), Math.max(body.offsetWidth, documentElement.offsetWidth), Math.max(body.clientWidth, documentElement.clientWidth));\n        var height = Math.max(Math.max(body.scrollHeight, documentElement.scrollHeight), Math.max(body.offsetHeight, documentElement.offsetHeight), Math.max(body.clientHeight, documentElement.clientHeight));\n        return new Bounds(0, 0, width, height);\n    };\n\n    /*\n     * css-line-break 2.1.0 <https://github.com/niklasvh/css-line-break#readme>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var toCodePoints$1 = function (str) {\n        var codePoints = [];\n        var i = 0;\n        var length = str.length;\n        while (i < length) {\n            var value = str.charCodeAt(i++);\n            if (value >= 0xd800 && value <= 0xdbff && i < length) {\n                var extra = str.charCodeAt(i++);\n                if ((extra & 0xfc00) === 0xdc00) {\n                    codePoints.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n                }\n                else {\n                    codePoints.push(value);\n                    i--;\n                }\n            }\n            else {\n                codePoints.push(value);\n            }\n        }\n        return codePoints;\n    };\n    var fromCodePoint$1 = function () {\n        var codePoints = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            codePoints[_i] = arguments[_i];\n        }\n        if (String.fromCodePoint) {\n            return String.fromCodePoint.apply(String, codePoints);\n        }\n        var length = codePoints.length;\n        if (!length) {\n            return '';\n        }\n        var codeUnits = [];\n        var index = -1;\n        var result = '';\n        while (++index < length) {\n            var codePoint = codePoints[index];\n            if (codePoint <= 0xffff) {\n                codeUnits.push(codePoint);\n            }\n            else {\n                codePoint -= 0x10000;\n                codeUnits.push((codePoint >> 10) + 0xd800, (codePoint % 0x400) + 0xdc00);\n            }\n            if (index + 1 === length || codeUnits.length > 0x4000) {\n                result += String.fromCharCode.apply(String, codeUnits);\n                codeUnits.length = 0;\n            }\n        }\n        return result;\n    };\n    var chars$2 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    // Use a lookup table to find the index.\n    var lookup$2 = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n    for (var i$2 = 0; i$2 < chars$2.length; i$2++) {\n        lookup$2[chars$2.charCodeAt(i$2)] = i$2;\n    }\n\n    /*\n     * utrie 1.0.2 <https://github.com/niklasvh/utrie>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var chars$1$1 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    // Use a lookup table to find the index.\n    var lookup$1$1 = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n    for (var i$1$1 = 0; i$1$1 < chars$1$1.length; i$1$1++) {\n        lookup$1$1[chars$1$1.charCodeAt(i$1$1)] = i$1$1;\n    }\n    var decode$1 = function (base64) {\n        var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n        if (base64[base64.length - 1] === '=') {\n            bufferLength--;\n            if (base64[base64.length - 2] === '=') {\n                bufferLength--;\n            }\n        }\n        var buffer = typeof ArrayBuffer !== 'undefined' &&\n            typeof Uint8Array !== 'undefined' &&\n            typeof Uint8Array.prototype.slice !== 'undefined'\n            ? new ArrayBuffer(bufferLength)\n            : new Array(bufferLength);\n        var bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n        for (i = 0; i < len; i += 4) {\n            encoded1 = lookup$1$1[base64.charCodeAt(i)];\n            encoded2 = lookup$1$1[base64.charCodeAt(i + 1)];\n            encoded3 = lookup$1$1[base64.charCodeAt(i + 2)];\n            encoded4 = lookup$1$1[base64.charCodeAt(i + 3)];\n            bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n            bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n            bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n        }\n        return buffer;\n    };\n    var polyUint16Array$1 = function (buffer) {\n        var length = buffer.length;\n        var bytes = [];\n        for (var i = 0; i < length; i += 2) {\n            bytes.push((buffer[i + 1] << 8) | buffer[i]);\n        }\n        return bytes;\n    };\n    var polyUint32Array$1 = function (buffer) {\n        var length = buffer.length;\n        var bytes = [];\n        for (var i = 0; i < length; i += 4) {\n            bytes.push((buffer[i + 3] << 24) | (buffer[i + 2] << 16) | (buffer[i + 1] << 8) | buffer[i]);\n        }\n        return bytes;\n    };\n\n    /** Shift size for getting the index-2 table offset. */\n    var UTRIE2_SHIFT_2$1 = 5;\n    /** Shift size for getting the index-1 table offset. */\n    var UTRIE2_SHIFT_1$1 = 6 + 5;\n    /**\n     * Shift size for shifting left the index array values.\n     * Increases possible data size with 16-bit index values at the cost\n     * of compactability.\n     * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n     */\n    var UTRIE2_INDEX_SHIFT$1 = 2;\n    /**\n     * Difference between the two shift sizes,\n     * for getting an index-1 offset from an index-2 offset. 6=11-5\n     */\n    var UTRIE2_SHIFT_1_2$1 = UTRIE2_SHIFT_1$1 - UTRIE2_SHIFT_2$1;\n    /**\n     * The part of the index-2 table for U+D800..U+DBFF stores values for\n     * lead surrogate code _units_ not code _points_.\n     * Values for lead surrogate code _points_ are indexed with this portion of the table.\n     * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n     */\n    var UTRIE2_LSCP_INDEX_2_OFFSET$1 = 0x10000 >> UTRIE2_SHIFT_2$1;\n    /** Number of entries in a data block. 32=0x20 */\n    var UTRIE2_DATA_BLOCK_LENGTH$1 = 1 << UTRIE2_SHIFT_2$1;\n    /** Mask for getting the lower bits for the in-data-block offset. */\n    var UTRIE2_DATA_MASK$1 = UTRIE2_DATA_BLOCK_LENGTH$1 - 1;\n    var UTRIE2_LSCP_INDEX_2_LENGTH$1 = 0x400 >> UTRIE2_SHIFT_2$1;\n    /** Count the lengths of both BMP pieces. 2080=0x820 */\n    var UTRIE2_INDEX_2_BMP_LENGTH$1 = UTRIE2_LSCP_INDEX_2_OFFSET$1 + UTRIE2_LSCP_INDEX_2_LENGTH$1;\n    /**\n     * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n     * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n     */\n    var UTRIE2_UTF8_2B_INDEX_2_OFFSET$1 = UTRIE2_INDEX_2_BMP_LENGTH$1;\n    var UTRIE2_UTF8_2B_INDEX_2_LENGTH$1 = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n    /**\n     * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n     * Variable length, for code points up to highStart, where the last single-value range starts.\n     * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n     * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n     *\n     * The part of the index-2 table for supplementary code points starts\n     * after this index-1 table.\n     *\n     * Both the index-1 table and the following part of the index-2 table\n     * are omitted completely if there is only BMP data.\n     */\n    var UTRIE2_INDEX_1_OFFSET$1 = UTRIE2_UTF8_2B_INDEX_2_OFFSET$1 + UTRIE2_UTF8_2B_INDEX_2_LENGTH$1;\n    /**\n     * Number of index-1 entries for the BMP. 32=0x20\n     * This part of the index-1 table is omitted from the serialized form.\n     */\n    var UTRIE2_OMITTED_BMP_INDEX_1_LENGTH$1 = 0x10000 >> UTRIE2_SHIFT_1$1;\n    /** Number of entries in an index-2 block. 64=0x40 */\n    var UTRIE2_INDEX_2_BLOCK_LENGTH$1 = 1 << UTRIE2_SHIFT_1_2$1;\n    /** Mask for getting the lower bits for the in-index-2-block offset. */\n    var UTRIE2_INDEX_2_MASK$1 = UTRIE2_INDEX_2_BLOCK_LENGTH$1 - 1;\n    var slice16$1 = function (view, start, end) {\n        if (view.slice) {\n            return view.slice(start, end);\n        }\n        return new Uint16Array(Array.prototype.slice.call(view, start, end));\n    };\n    var slice32$1 = function (view, start, end) {\n        if (view.slice) {\n            return view.slice(start, end);\n        }\n        return new Uint32Array(Array.prototype.slice.call(view, start, end));\n    };\n    var createTrieFromBase64$1 = function (base64, _byteLength) {\n        var buffer = decode$1(base64);\n        var view32 = Array.isArray(buffer) ? polyUint32Array$1(buffer) : new Uint32Array(buffer);\n        var view16 = Array.isArray(buffer) ? polyUint16Array$1(buffer) : new Uint16Array(buffer);\n        var headerLength = 24;\n        var index = slice16$1(view16, headerLength / 2, view32[4] / 2);\n        var data = view32[5] === 2\n            ? slice16$1(view16, (headerLength + view32[4]) / 2)\n            : slice32$1(view32, Math.ceil((headerLength + view32[4]) / 4));\n        return new Trie$1(view32[0], view32[1], view32[2], view32[3], index, data);\n    };\n    var Trie$1 = /** @class */ (function () {\n        function Trie(initialValue, errorValue, highStart, highValueIndex, index, data) {\n            this.initialValue = initialValue;\n            this.errorValue = errorValue;\n            this.highStart = highStart;\n            this.highValueIndex = highValueIndex;\n            this.index = index;\n            this.data = data;\n        }\n        /**\n         * Get the value for a code point as stored in the Trie.\n         *\n         * @param codePoint the code point\n         * @return the value\n         */\n        Trie.prototype.get = function (codePoint) {\n            var ix;\n            if (codePoint >= 0) {\n                if (codePoint < 0x0d800 || (codePoint > 0x0dbff && codePoint <= 0x0ffff)) {\n                    // Ordinary BMP code point, excluding leading surrogates.\n                    // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                    // 16 bit data is stored in the index array itself.\n                    ix = this.index[codePoint >> UTRIE2_SHIFT_2$1];\n                    ix = (ix << UTRIE2_INDEX_SHIFT$1) + (codePoint & UTRIE2_DATA_MASK$1);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0xffff) {\n                    // Lead Surrogate Code Point.  A Separate index section is stored for\n                    // lead surrogate code units and code points.\n                    //   The main index has the code unit data.\n                    //   For this function, we need the code point data.\n                    // Note: this expression could be refactored for slightly improved efficiency, but\n                    //       surrogate code points will be so rare in practice that it's not worth it.\n                    ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET$1 + ((codePoint - 0xd800) >> UTRIE2_SHIFT_2$1)];\n                    ix = (ix << UTRIE2_INDEX_SHIFT$1) + (codePoint & UTRIE2_DATA_MASK$1);\n                    return this.data[ix];\n                }\n                if (codePoint < this.highStart) {\n                    // Supplemental code point, use two-level lookup.\n                    ix = UTRIE2_INDEX_1_OFFSET$1 - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH$1 + (codePoint >> UTRIE2_SHIFT_1$1);\n                    ix = this.index[ix];\n                    ix += (codePoint >> UTRIE2_SHIFT_2$1) & UTRIE2_INDEX_2_MASK$1;\n                    ix = this.index[ix];\n                    ix = (ix << UTRIE2_INDEX_SHIFT$1) + (codePoint & UTRIE2_DATA_MASK$1);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0x10ffff) {\n                    return this.data[this.highValueIndex];\n                }\n            }\n            // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n            return this.errorValue;\n        };\n        return Trie;\n    }());\n\n    /*\n     * base64-arraybuffer 1.0.2 <https://github.com/niklasvh/base64-arraybuffer>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var chars$3 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    // Use a lookup table to find the index.\n    var lookup$3 = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n    for (var i$3 = 0; i$3 < chars$3.length; i$3++) {\n        lookup$3[chars$3.charCodeAt(i$3)] = i$3;\n    }\n\n    var base64$1 = '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';\n\n    var LETTER_NUMBER_MODIFIER = 50;\n    // Non-tailorable Line Breaking Classes\n    var BK = 1; //  Cause a line break (after)\n    var CR$1 = 2; //  Cause a line break (after), except between CR and LF\n    var LF$1 = 3; //  Cause a line break (after)\n    var CM = 4; //  Prohibit a line break between the character and the preceding character\n    var NL = 5; //  Cause a line break (after)\n    var WJ = 7; //  Prohibit line breaks before and after\n    var ZW = 8; //  Provide a break opportunity\n    var GL = 9; //  Prohibit line breaks before and after\n    var SP = 10; // Enable indirect line breaks\n    var ZWJ$1 = 11; // Prohibit line breaks within joiner sequences\n    // Break Opportunities\n    var B2 = 12; //  Provide a line break opportunity before and after the character\n    var BA = 13; //  Generally provide a line break opportunity after the character\n    var BB = 14; //  Generally provide a line break opportunity before the character\n    var HY = 15; //  Provide a line break opportunity after the character, except in numeric context\n    var CB = 16; //   Provide a line break opportunity contingent on additional information\n    // Characters Prohibiting Certain Breaks\n    var CL = 17; //  Prohibit line breaks before\n    var CP = 18; //  Prohibit line breaks before\n    var EX = 19; //  Prohibit line breaks before\n    var IN = 20; //  Allow only indirect line breaks between pairs\n    var NS = 21; //  Allow only indirect line breaks before\n    var OP = 22; //  Prohibit line breaks after\n    var QU = 23; //  Act like they are both opening and closing\n    // Numeric Context\n    var IS = 24; //  Prevent breaks after any and before numeric\n    var NU = 25; //  Form numeric expressions for line breaking purposes\n    var PO = 26; //  Do not break following a numeric expression\n    var PR = 27; //  Do not break in front of a numeric expression\n    var SY = 28; //  Prevent a break before; and allow a break after\n    // Other Characters\n    var AI = 29; //  Act like AL when the resolvedEAW is N; otherwise; act as ID\n    var AL = 30; //  Are alphabetic characters or symbols that are used with alphabetic characters\n    var CJ = 31; //  Treat as NS or ID for strict or normal breaking.\n    var EB = 32; //  Do not break from following Emoji Modifier\n    var EM = 33; //  Do not break from preceding Emoji Base\n    var H2 = 34; //  Form Korean syllable blocks\n    var H3 = 35; //  Form Korean syllable blocks\n    var HL = 36; //  Do not break around a following hyphen; otherwise act as Alphabetic\n    var ID = 37; //  Break before or after; except in some numeric context\n    var JL = 38; //  Form Korean syllable blocks\n    var JV = 39; //  Form Korean syllable blocks\n    var JT = 40; //  Form Korean syllable blocks\n    var RI$1 = 41; //  Keep pairs together. For pairs; break before and after other classes\n    var SA = 42; //  Provide a line break opportunity contingent on additional, language-specific context analysis\n    var XX = 43; //  Have as yet unknown line breaking behavior or unassigned code positions\n    var ea_OP = [0x2329, 0xff08];\n    var BREAK_MANDATORY = '!';\n    var BREAK_NOT_ALLOWED$1 = '×';\n    var BREAK_ALLOWED$1 = '÷';\n    var UnicodeTrie$1 = createTrieFromBase64$1(base64$1);\n    var ALPHABETICS = [AL, HL];\n    var HARD_LINE_BREAKS = [BK, CR$1, LF$1, NL];\n    var SPACE$1 = [SP, ZW];\n    var PREFIX_POSTFIX = [PR, PO];\n    var LINE_BREAKS = HARD_LINE_BREAKS.concat(SPACE$1);\n    var KOREAN_SYLLABLE_BLOCK = [JL, JV, JT, H2, H3];\n    var HYPHEN = [HY, BA];\n    var codePointsToCharacterClasses = function (codePoints, lineBreak) {\n        if (lineBreak === void 0) { lineBreak = 'strict'; }\n        var types = [];\n        var indices = [];\n        var categories = [];\n        codePoints.forEach(function (codePoint, index) {\n            var classType = UnicodeTrie$1.get(codePoint);\n            if (classType > LETTER_NUMBER_MODIFIER) {\n                categories.push(true);\n                classType -= LETTER_NUMBER_MODIFIER;\n            }\n            else {\n                categories.push(false);\n            }\n            if (['normal', 'auto', 'loose'].indexOf(lineBreak) !== -1) {\n                // U+2010, – U+2013, 〜 U+301C, ゠ U+30A0\n                if ([0x2010, 0x2013, 0x301c, 0x30a0].indexOf(codePoint) !== -1) {\n                    indices.push(index);\n                    return types.push(CB);\n                }\n            }\n            if (classType === CM || classType === ZWJ$1) {\n                // LB10 Treat any remaining combining mark or ZWJ as AL.\n                if (index === 0) {\n                    indices.push(index);\n                    return types.push(AL);\n                }\n                // LB9 Do not break a combining character sequence; treat it as if it has the line breaking class of\n                // the base character in all of the following rules. Treat ZWJ as if it were CM.\n                var prev = types[index - 1];\n                if (LINE_BREAKS.indexOf(prev) === -1) {\n                    indices.push(indices[index - 1]);\n                    return types.push(prev);\n                }\n                indices.push(index);\n                return types.push(AL);\n            }\n            indices.push(index);\n            if (classType === CJ) {\n                return types.push(lineBreak === 'strict' ? NS : ID);\n            }\n            if (classType === SA) {\n                return types.push(AL);\n            }\n            if (classType === AI) {\n                return types.push(AL);\n            }\n            // For supplementary characters, a useful default is to treat characters in the range 10000..1FFFD as AL\n            // and characters in the ranges 20000..2FFFD and 30000..3FFFD as ID, until the implementation can be revised\n            // to take into account the actual line breaking properties for these characters.\n            if (classType === XX) {\n                if ((codePoint >= 0x20000 && codePoint <= 0x2fffd) || (codePoint >= 0x30000 && codePoint <= 0x3fffd)) {\n                    return types.push(ID);\n                }\n                else {\n                    return types.push(AL);\n                }\n            }\n            types.push(classType);\n        });\n        return [indices, types, categories];\n    };\n    var isAdjacentWithSpaceIgnored = function (a, b, currentIndex, classTypes) {\n        var current = classTypes[currentIndex];\n        if (Array.isArray(a) ? a.indexOf(current) !== -1 : a === current) {\n            var i = currentIndex;\n            while (i <= classTypes.length) {\n                i++;\n                var next = classTypes[i];\n                if (next === b) {\n                    return true;\n                }\n                if (next !== SP) {\n                    break;\n                }\n            }\n        }\n        if (current === SP) {\n            var i = currentIndex;\n            while (i > 0) {\n                i--;\n                var prev = classTypes[i];\n                if (Array.isArray(a) ? a.indexOf(prev) !== -1 : a === prev) {\n                    var n = currentIndex;\n                    while (n <= classTypes.length) {\n                        n++;\n                        var next = classTypes[n];\n                        if (next === b) {\n                            return true;\n                        }\n                        if (next !== SP) {\n                            break;\n                        }\n                    }\n                }\n                if (prev !== SP) {\n                    break;\n                }\n            }\n        }\n        return false;\n    };\n    var previousNonSpaceClassType = function (currentIndex, classTypes) {\n        var i = currentIndex;\n        while (i >= 0) {\n            var type = classTypes[i];\n            if (type === SP) {\n                i--;\n            }\n            else {\n                return type;\n            }\n        }\n        return 0;\n    };\n    var _lineBreakAtIndex = function (codePoints, classTypes, indicies, index, forbiddenBreaks) {\n        if (indicies[index] === 0) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        var currentIndex = index - 1;\n        if (Array.isArray(forbiddenBreaks) && forbiddenBreaks[currentIndex] === true) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        var beforeIndex = currentIndex - 1;\n        var afterIndex = currentIndex + 1;\n        var current = classTypes[currentIndex];\n        // LB4 Always break after hard line breaks.\n        // LB5 Treat CR followed by LF, as well as CR, LF, and NL as hard line breaks.\n        var before = beforeIndex >= 0 ? classTypes[beforeIndex] : 0;\n        var next = classTypes[afterIndex];\n        if (current === CR$1 && next === LF$1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        if (HARD_LINE_BREAKS.indexOf(current) !== -1) {\n            return BREAK_MANDATORY;\n        }\n        // LB6 Do not break before hard line breaks.\n        if (HARD_LINE_BREAKS.indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB7 Do not break before spaces or zero width space.\n        if (SPACE$1.indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB8 Break before any character following a zero-width space, even if one or more spaces intervene.\n        if (previousNonSpaceClassType(currentIndex, classTypes) === ZW) {\n            return BREAK_ALLOWED$1;\n        }\n        // LB8a Do not break after a zero width joiner.\n        if (UnicodeTrie$1.get(codePoints[currentIndex]) === ZWJ$1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // zwj emojis\n        if ((current === EB || current === EM) && UnicodeTrie$1.get(codePoints[afterIndex]) === ZWJ$1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB11 Do not break before or after Word joiner and related characters.\n        if (current === WJ || next === WJ) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB12 Do not break after NBSP and related characters.\n        if (current === GL) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB12a Do not break before NBSP and related characters, except after spaces and hyphens.\n        if ([SP, BA, HY].indexOf(current) === -1 && next === GL) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB13 Do not break before ‘]’ or ‘!’ or ‘;’ or ‘/’, even after spaces.\n        if ([CL, CP, EX, IS, SY].indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB14 Do not break after ‘[’, even after spaces.\n        if (previousNonSpaceClassType(currentIndex, classTypes) === OP) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB15 Do not break within ‘”[’, even with intervening spaces.\n        if (isAdjacentWithSpaceIgnored(QU, OP, currentIndex, classTypes)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB16 Do not break between closing punctuation and a nonstarter (lb=NS), even with intervening spaces.\n        if (isAdjacentWithSpaceIgnored([CL, CP], NS, currentIndex, classTypes)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB17 Do not break within ‘——’, even with intervening spaces.\n        if (isAdjacentWithSpaceIgnored(B2, B2, currentIndex, classTypes)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB18 Break after spaces.\n        if (current === SP) {\n            return BREAK_ALLOWED$1;\n        }\n        // LB19 Do not break before or after quotation marks, such as ‘ ” ’.\n        if (current === QU || next === QU) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB20 Break before and after unresolved CB.\n        if (next === CB || current === CB) {\n            return BREAK_ALLOWED$1;\n        }\n        // LB21 Do not break before hyphen-minus, other hyphens, fixed-width spaces, small kana, and other non-starters, or after acute accents.\n        if ([BA, HY, NS].indexOf(next) !== -1 || current === BB) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB21a Don't break after Hebrew + Hyphen.\n        if (before === HL && HYPHEN.indexOf(current) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB21b Don’t break between Solidus and Hebrew letters.\n        if (current === SY && next === HL) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB22 Do not break before ellipsis.\n        if (next === IN) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB23 Do not break between digits and letters.\n        if ((ALPHABETICS.indexOf(next) !== -1 && current === NU) || (ALPHABETICS.indexOf(current) !== -1 && next === NU)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB23a Do not break between numeric prefixes and ideographs, or between ideographs and numeric postfixes.\n        if ((current === PR && [ID, EB, EM].indexOf(next) !== -1) ||\n            ([ID, EB, EM].indexOf(current) !== -1 && next === PO)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB24 Do not break between numeric prefix/postfix and letters, or between letters and prefix/postfix.\n        if ((ALPHABETICS.indexOf(current) !== -1 && PREFIX_POSTFIX.indexOf(next) !== -1) ||\n            (PREFIX_POSTFIX.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB25 Do not break between the following pairs of classes relevant to numbers:\n        if (\n        // (PR | PO) × ( OP | HY )? NU\n        ([PR, PO].indexOf(current) !== -1 &&\n            (next === NU || ([OP, HY].indexOf(next) !== -1 && classTypes[afterIndex + 1] === NU))) ||\n            // ( OP | HY ) × NU\n            ([OP, HY].indexOf(current) !== -1 && next === NU) ||\n            // NU ×\t(NU | SY | IS)\n            (current === NU && [NU, SY, IS].indexOf(next) !== -1)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // NU (NU | SY | IS)* × (NU | SY | IS | CL | CP)\n        if ([NU, SY, IS, CL, CP].indexOf(next) !== -1) {\n            var prevIndex = currentIndex;\n            while (prevIndex >= 0) {\n                var type = classTypes[prevIndex];\n                if (type === NU) {\n                    return BREAK_NOT_ALLOWED$1;\n                }\n                else if ([SY, IS].indexOf(type) !== -1) {\n                    prevIndex--;\n                }\n                else {\n                    break;\n                }\n            }\n        }\n        // NU (NU | SY | IS)* (CL | CP)? × (PO | PR))\n        if ([PR, PO].indexOf(next) !== -1) {\n            var prevIndex = [CL, CP].indexOf(current) !== -1 ? beforeIndex : currentIndex;\n            while (prevIndex >= 0) {\n                var type = classTypes[prevIndex];\n                if (type === NU) {\n                    return BREAK_NOT_ALLOWED$1;\n                }\n                else if ([SY, IS].indexOf(type) !== -1) {\n                    prevIndex--;\n                }\n                else {\n                    break;\n                }\n            }\n        }\n        // LB26 Do not break a Korean syllable.\n        if ((JL === current && [JL, JV, H2, H3].indexOf(next) !== -1) ||\n            ([JV, H2].indexOf(current) !== -1 && [JV, JT].indexOf(next) !== -1) ||\n            ([JT, H3].indexOf(current) !== -1 && next === JT)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB27 Treat a Korean Syllable Block the same as ID.\n        if ((KOREAN_SYLLABLE_BLOCK.indexOf(current) !== -1 && [IN, PO].indexOf(next) !== -1) ||\n            (KOREAN_SYLLABLE_BLOCK.indexOf(next) !== -1 && current === PR)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB28 Do not break between alphabetics (“at”).\n        if (ALPHABETICS.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB29 Do not break between numeric punctuation and alphabetics (“e.g.”).\n        if (current === IS && ALPHABETICS.indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB30 Do not break between letters, numbers, or ordinary symbols and opening or closing parentheses.\n        if ((ALPHABETICS.concat(NU).indexOf(current) !== -1 &&\n            next === OP &&\n            ea_OP.indexOf(codePoints[afterIndex]) === -1) ||\n            (ALPHABETICS.concat(NU).indexOf(next) !== -1 && current === CP)) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        // LB30a Break between two regional indicator symbols if and only if there are an even number of regional\n        // indicators preceding the position of the break.\n        if (current === RI$1 && next === RI$1) {\n            var i = indicies[currentIndex];\n            var count = 1;\n            while (i > 0) {\n                i--;\n                if (classTypes[i] === RI$1) {\n                    count++;\n                }\n                else {\n                    break;\n                }\n            }\n            if (count % 2 !== 0) {\n                return BREAK_NOT_ALLOWED$1;\n            }\n        }\n        // LB30b Do not break between an emoji base and an emoji modifier.\n        if (current === EB && next === EM) {\n            return BREAK_NOT_ALLOWED$1;\n        }\n        return BREAK_ALLOWED$1;\n    };\n    var cssFormattedClasses = function (codePoints, options) {\n        if (!options) {\n            options = { lineBreak: 'normal', wordBreak: 'normal' };\n        }\n        var _a = codePointsToCharacterClasses(codePoints, options.lineBreak), indicies = _a[0], classTypes = _a[1], isLetterNumber = _a[2];\n        if (options.wordBreak === 'break-all' || options.wordBreak === 'break-word') {\n            classTypes = classTypes.map(function (type) { return ([NU, AL, SA].indexOf(type) !== -1 ? ID : type); });\n        }\n        var forbiddenBreakpoints = options.wordBreak === 'keep-all'\n            ? isLetterNumber.map(function (letterNumber, i) {\n                return letterNumber && codePoints[i] >= 0x4e00 && codePoints[i] <= 0x9fff;\n            })\n            : undefined;\n        return [indicies, classTypes, forbiddenBreakpoints];\n    };\n    var Break = /** @class */ (function () {\n        function Break(codePoints, lineBreak, start, end) {\n            this.codePoints = codePoints;\n            this.required = lineBreak === BREAK_MANDATORY;\n            this.start = start;\n            this.end = end;\n        }\n        Break.prototype.slice = function () {\n            return fromCodePoint$1.apply(void 0, this.codePoints.slice(this.start, this.end));\n        };\n        return Break;\n    }());\n    var LineBreaker = function (str, options) {\n        var codePoints = toCodePoints$1(str);\n        var _a = cssFormattedClasses(codePoints, options), indicies = _a[0], classTypes = _a[1], forbiddenBreakpoints = _a[2];\n        var length = codePoints.length;\n        var lastEnd = 0;\n        var nextIndex = 0;\n        return {\n            next: function () {\n                if (nextIndex >= length) {\n                    return { done: true, value: null };\n                }\n                var lineBreak = BREAK_NOT_ALLOWED$1;\n                while (nextIndex < length &&\n                    (lineBreak = _lineBreakAtIndex(codePoints, classTypes, indicies, ++nextIndex, forbiddenBreakpoints)) ===\n                        BREAK_NOT_ALLOWED$1) { }\n                if (lineBreak !== BREAK_NOT_ALLOWED$1 || nextIndex === length) {\n                    var value = new Break(codePoints, lineBreak, lastEnd, nextIndex);\n                    lastEnd = nextIndex;\n                    return { value: value, done: false };\n                }\n                return { done: true, value: null };\n            },\n        };\n    };\n\n    // https://www.w3.org/TR/css-syntax-3\n    var FLAG_UNRESTRICTED = 1 << 0;\n    var FLAG_ID = 1 << 1;\n    var FLAG_INTEGER = 1 << 2;\n    var FLAG_NUMBER = 1 << 3;\n    var LINE_FEED = 0x000a;\n    var SOLIDUS = 0x002f;\n    var REVERSE_SOLIDUS = 0x005c;\n    var CHARACTER_TABULATION = 0x0009;\n    var SPACE = 0x0020;\n    var QUOTATION_MARK = 0x0022;\n    var EQUALS_SIGN = 0x003d;\n    var NUMBER_SIGN = 0x0023;\n    var DOLLAR_SIGN = 0x0024;\n    var PERCENTAGE_SIGN = 0x0025;\n    var APOSTROPHE = 0x0027;\n    var LEFT_PARENTHESIS = 0x0028;\n    var RIGHT_PARENTHESIS = 0x0029;\n    var LOW_LINE = 0x005f;\n    var HYPHEN_MINUS = 0x002d;\n    var EXCLAMATION_MARK = 0x0021;\n    var LESS_THAN_SIGN = 0x003c;\n    var GREATER_THAN_SIGN = 0x003e;\n    var COMMERCIAL_AT = 0x0040;\n    var LEFT_SQUARE_BRACKET = 0x005b;\n    var RIGHT_SQUARE_BRACKET = 0x005d;\n    var CIRCUMFLEX_ACCENT = 0x003d;\n    var LEFT_CURLY_BRACKET = 0x007b;\n    var QUESTION_MARK = 0x003f;\n    var RIGHT_CURLY_BRACKET = 0x007d;\n    var VERTICAL_LINE = 0x007c;\n    var TILDE = 0x007e;\n    var CONTROL = 0x0080;\n    var REPLACEMENT_CHARACTER = 0xfffd;\n    var ASTERISK = 0x002a;\n    var PLUS_SIGN = 0x002b;\n    var COMMA = 0x002c;\n    var COLON = 0x003a;\n    var SEMICOLON = 0x003b;\n    var FULL_STOP = 0x002e;\n    var NULL = 0x0000;\n    var BACKSPACE = 0x0008;\n    var LINE_TABULATION = 0x000b;\n    var SHIFT_OUT = 0x000e;\n    var INFORMATION_SEPARATOR_ONE = 0x001f;\n    var DELETE = 0x007f;\n    var EOF = -1;\n    var ZERO = 0x0030;\n    var a = 0x0061;\n    var e = 0x0065;\n    var f = 0x0066;\n    var u = 0x0075;\n    var z = 0x007a;\n    var A = 0x0041;\n    var E = 0x0045;\n    var F = 0x0046;\n    var U = 0x0055;\n    var Z = 0x005a;\n    var isDigit = function (codePoint) { return codePoint >= ZERO && codePoint <= 0x0039; };\n    var isSurrogateCodePoint = function (codePoint) { return codePoint >= 0xd800 && codePoint <= 0xdfff; };\n    var isHex = function (codePoint) {\n        return isDigit(codePoint) || (codePoint >= A && codePoint <= F) || (codePoint >= a && codePoint <= f);\n    };\n    var isLowerCaseLetter = function (codePoint) { return codePoint >= a && codePoint <= z; };\n    var isUpperCaseLetter = function (codePoint) { return codePoint >= A && codePoint <= Z; };\n    var isLetter = function (codePoint) { return isLowerCaseLetter(codePoint) || isUpperCaseLetter(codePoint); };\n    var isNonASCIICodePoint = function (codePoint) { return codePoint >= CONTROL; };\n    var isWhiteSpace = function (codePoint) {\n        return codePoint === LINE_FEED || codePoint === CHARACTER_TABULATION || codePoint === SPACE;\n    };\n    var isNameStartCodePoint = function (codePoint) {\n        return isLetter(codePoint) || isNonASCIICodePoint(codePoint) || codePoint === LOW_LINE;\n    };\n    var isNameCodePoint = function (codePoint) {\n        return isNameStartCodePoint(codePoint) || isDigit(codePoint) || codePoint === HYPHEN_MINUS;\n    };\n    var isNonPrintableCodePoint = function (codePoint) {\n        return ((codePoint >= NULL && codePoint <= BACKSPACE) ||\n            codePoint === LINE_TABULATION ||\n            (codePoint >= SHIFT_OUT && codePoint <= INFORMATION_SEPARATOR_ONE) ||\n            codePoint === DELETE);\n    };\n    var isValidEscape = function (c1, c2) {\n        if (c1 !== REVERSE_SOLIDUS) {\n            return false;\n        }\n        return c2 !== LINE_FEED;\n    };\n    var isIdentifierStart = function (c1, c2, c3) {\n        if (c1 === HYPHEN_MINUS) {\n            return isNameStartCodePoint(c2) || isValidEscape(c2, c3);\n        }\n        else if (isNameStartCodePoint(c1)) {\n            return true;\n        }\n        else if (c1 === REVERSE_SOLIDUS && isValidEscape(c1, c2)) {\n            return true;\n        }\n        return false;\n    };\n    var isNumberStart = function (c1, c2, c3) {\n        if (c1 === PLUS_SIGN || c1 === HYPHEN_MINUS) {\n            if (isDigit(c2)) {\n                return true;\n            }\n            return c2 === FULL_STOP && isDigit(c3);\n        }\n        if (c1 === FULL_STOP) {\n            return isDigit(c2);\n        }\n        return isDigit(c1);\n    };\n    var stringToNumber = function (codePoints) {\n        var c = 0;\n        var sign = 1;\n        if (codePoints[c] === PLUS_SIGN || codePoints[c] === HYPHEN_MINUS) {\n            if (codePoints[c] === HYPHEN_MINUS) {\n                sign = -1;\n            }\n            c++;\n        }\n        var integers = [];\n        while (isDigit(codePoints[c])) {\n            integers.push(codePoints[c++]);\n        }\n        var int = integers.length ? parseInt(fromCodePoint$1.apply(void 0, integers), 10) : 0;\n        if (codePoints[c] === FULL_STOP) {\n            c++;\n        }\n        var fraction = [];\n        while (isDigit(codePoints[c])) {\n            fraction.push(codePoints[c++]);\n        }\n        var fracd = fraction.length;\n        var frac = fracd ? parseInt(fromCodePoint$1.apply(void 0, fraction), 10) : 0;\n        if (codePoints[c] === E || codePoints[c] === e) {\n            c++;\n        }\n        var expsign = 1;\n        if (codePoints[c] === PLUS_SIGN || codePoints[c] === HYPHEN_MINUS) {\n            if (codePoints[c] === HYPHEN_MINUS) {\n                expsign = -1;\n            }\n            c++;\n        }\n        var exponent = [];\n        while (isDigit(codePoints[c])) {\n            exponent.push(codePoints[c++]);\n        }\n        var exp = exponent.length ? parseInt(fromCodePoint$1.apply(void 0, exponent), 10) : 0;\n        return sign * (int + frac * Math.pow(10, -fracd)) * Math.pow(10, expsign * exp);\n    };\n    var LEFT_PARENTHESIS_TOKEN = {\n        type: 2 /* LEFT_PARENTHESIS_TOKEN */\n    };\n    var RIGHT_PARENTHESIS_TOKEN = {\n        type: 3 /* RIGHT_PARENTHESIS_TOKEN */\n    };\n    var COMMA_TOKEN = { type: 4 /* COMMA_TOKEN */ };\n    var SUFFIX_MATCH_TOKEN = { type: 13 /* SUFFIX_MATCH_TOKEN */ };\n    var PREFIX_MATCH_TOKEN = { type: 8 /* PREFIX_MATCH_TOKEN */ };\n    var COLUMN_TOKEN = { type: 21 /* COLUMN_TOKEN */ };\n    var DASH_MATCH_TOKEN = { type: 9 /* DASH_MATCH_TOKEN */ };\n    var INCLUDE_MATCH_TOKEN = { type: 10 /* INCLUDE_MATCH_TOKEN */ };\n    var LEFT_CURLY_BRACKET_TOKEN = {\n        type: 11 /* LEFT_CURLY_BRACKET_TOKEN */\n    };\n    var RIGHT_CURLY_BRACKET_TOKEN = {\n        type: 12 /* RIGHT_CURLY_BRACKET_TOKEN */\n    };\n    var SUBSTRING_MATCH_TOKEN = { type: 14 /* SUBSTRING_MATCH_TOKEN */ };\n    var BAD_URL_TOKEN = { type: 23 /* BAD_URL_TOKEN */ };\n    var BAD_STRING_TOKEN = { type: 1 /* BAD_STRING_TOKEN */ };\n    var CDO_TOKEN = { type: 25 /* CDO_TOKEN */ };\n    var CDC_TOKEN = { type: 24 /* CDC_TOKEN */ };\n    var COLON_TOKEN = { type: 26 /* COLON_TOKEN */ };\n    var SEMICOLON_TOKEN = { type: 27 /* SEMICOLON_TOKEN */ };\n    var LEFT_SQUARE_BRACKET_TOKEN = {\n        type: 28 /* LEFT_SQUARE_BRACKET_TOKEN */\n    };\n    var RIGHT_SQUARE_BRACKET_TOKEN = {\n        type: 29 /* RIGHT_SQUARE_BRACKET_TOKEN */\n    };\n    var WHITESPACE_TOKEN = { type: 31 /* WHITESPACE_TOKEN */ };\n    var EOF_TOKEN = { type: 32 /* EOF_TOKEN */ };\n    var Tokenizer = /** @class */ (function () {\n        function Tokenizer() {\n            this._value = [];\n        }\n        Tokenizer.prototype.write = function (chunk) {\n            this._value = this._value.concat(toCodePoints$1(chunk));\n        };\n        Tokenizer.prototype.read = function () {\n            var tokens = [];\n            var token = this.consumeToken();\n            while (token !== EOF_TOKEN) {\n                tokens.push(token);\n                token = this.consumeToken();\n            }\n            return tokens;\n        };\n        Tokenizer.prototype.consumeToken = function () {\n            var codePoint = this.consumeCodePoint();\n            switch (codePoint) {\n                case QUOTATION_MARK:\n                    return this.consumeStringToken(QUOTATION_MARK);\n                case NUMBER_SIGN:\n                    var c1 = this.peekCodePoint(0);\n                    var c2 = this.peekCodePoint(1);\n                    var c3 = this.peekCodePoint(2);\n                    if (isNameCodePoint(c1) || isValidEscape(c2, c3)) {\n                        var flags = isIdentifierStart(c1, c2, c3) ? FLAG_ID : FLAG_UNRESTRICTED;\n                        var value = this.consumeName();\n                        return { type: 5 /* HASH_TOKEN */, value: value, flags: flags };\n                    }\n                    break;\n                case DOLLAR_SIGN:\n                    if (this.peekCodePoint(0) === EQUALS_SIGN) {\n                        this.consumeCodePoint();\n                        return SUFFIX_MATCH_TOKEN;\n                    }\n                    break;\n                case APOSTROPHE:\n                    return this.consumeStringToken(APOSTROPHE);\n                case LEFT_PARENTHESIS:\n                    return LEFT_PARENTHESIS_TOKEN;\n                case RIGHT_PARENTHESIS:\n                    return RIGHT_PARENTHESIS_TOKEN;\n                case ASTERISK:\n                    if (this.peekCodePoint(0) === EQUALS_SIGN) {\n                        this.consumeCodePoint();\n                        return SUBSTRING_MATCH_TOKEN;\n                    }\n                    break;\n                case PLUS_SIGN:\n                    if (isNumberStart(codePoint, this.peekCodePoint(0), this.peekCodePoint(1))) {\n                        this.reconsumeCodePoint(codePoint);\n                        return this.consumeNumericToken();\n                    }\n                    break;\n                case COMMA:\n                    return COMMA_TOKEN;\n                case HYPHEN_MINUS:\n                    var e1 = codePoint;\n                    var e2 = this.peekCodePoint(0);\n                    var e3 = this.peekCodePoint(1);\n                    if (isNumberStart(e1, e2, e3)) {\n                        this.reconsumeCodePoint(codePoint);\n                        return this.consumeNumericToken();\n                    }\n                    if (isIdentifierStart(e1, e2, e3)) {\n                        this.reconsumeCodePoint(codePoint);\n                        return this.consumeIdentLikeToken();\n                    }\n                    if (e2 === HYPHEN_MINUS && e3 === GREATER_THAN_SIGN) {\n                        this.consumeCodePoint();\n                        this.consumeCodePoint();\n                        return CDC_TOKEN;\n                    }\n                    break;\n                case FULL_STOP:\n                    if (isNumberStart(codePoint, this.peekCodePoint(0), this.peekCodePoint(1))) {\n                        this.reconsumeCodePoint(codePoint);\n                        return this.consumeNumericToken();\n                    }\n                    break;\n                case SOLIDUS:\n                    if (this.peekCodePoint(0) === ASTERISK) {\n                        this.consumeCodePoint();\n                        while (true) {\n                            var c = this.consumeCodePoint();\n                            if (c === ASTERISK) {\n                                c = this.consumeCodePoint();\n                                if (c === SOLIDUS) {\n                                    return this.consumeToken();\n                                }\n                            }\n                            if (c === EOF) {\n                                return this.consumeToken();\n                            }\n                        }\n                    }\n                    break;\n                case COLON:\n                    return COLON_TOKEN;\n                case SEMICOLON:\n                    return SEMICOLON_TOKEN;\n                case LESS_THAN_SIGN:\n                    if (this.peekCodePoint(0) === EXCLAMATION_MARK &&\n                        this.peekCodePoint(1) === HYPHEN_MINUS &&\n                        this.peekCodePoint(2) === HYPHEN_MINUS) {\n                        this.consumeCodePoint();\n                        this.consumeCodePoint();\n                        return CDO_TOKEN;\n                    }\n                    break;\n                case COMMERCIAL_AT:\n                    var a1 = this.peekCodePoint(0);\n                    var a2 = this.peekCodePoint(1);\n                    var a3 = this.peekCodePoint(2);\n                    if (isIdentifierStart(a1, a2, a3)) {\n                        var value = this.consumeName();\n                        return { type: 7 /* AT_KEYWORD_TOKEN */, value: value };\n                    }\n                    break;\n                case LEFT_SQUARE_BRACKET:\n                    return LEFT_SQUARE_BRACKET_TOKEN;\n                case REVERSE_SOLIDUS:\n                    if (isValidEscape(codePoint, this.peekCodePoint(0))) {\n                        this.reconsumeCodePoint(codePoint);\n                        return this.consumeIdentLikeToken();\n                    }\n                    break;\n                case RIGHT_SQUARE_BRACKET:\n                    return RIGHT_SQUARE_BRACKET_TOKEN;\n                case CIRCUMFLEX_ACCENT:\n                    if (this.peekCodePoint(0) === EQUALS_SIGN) {\n                        this.consumeCodePoint();\n                        return PREFIX_MATCH_TOKEN;\n                    }\n                    break;\n                case LEFT_CURLY_BRACKET:\n                    return LEFT_CURLY_BRACKET_TOKEN;\n                case RIGHT_CURLY_BRACKET:\n                    return RIGHT_CURLY_BRACKET_TOKEN;\n                case u:\n                case U:\n                    var u1 = this.peekCodePoint(0);\n                    var u2 = this.peekCodePoint(1);\n                    if (u1 === PLUS_SIGN && (isHex(u2) || u2 === QUESTION_MARK)) {\n                        this.consumeCodePoint();\n                        this.consumeUnicodeRangeToken();\n                    }\n                    this.reconsumeCodePoint(codePoint);\n                    return this.consumeIdentLikeToken();\n                case VERTICAL_LINE:\n                    if (this.peekCodePoint(0) === EQUALS_SIGN) {\n                        this.consumeCodePoint();\n                        return DASH_MATCH_TOKEN;\n                    }\n                    if (this.peekCodePoint(0) === VERTICAL_LINE) {\n                        this.consumeCodePoint();\n                        return COLUMN_TOKEN;\n                    }\n                    break;\n                case TILDE:\n                    if (this.peekCodePoint(0) === EQUALS_SIGN) {\n                        this.consumeCodePoint();\n                        return INCLUDE_MATCH_TOKEN;\n                    }\n                    break;\n                case EOF:\n                    return EOF_TOKEN;\n            }\n            if (isWhiteSpace(codePoint)) {\n                this.consumeWhiteSpace();\n                return WHITESPACE_TOKEN;\n            }\n            if (isDigit(codePoint)) {\n                this.reconsumeCodePoint(codePoint);\n                return this.consumeNumericToken();\n            }\n            if (isNameStartCodePoint(codePoint)) {\n                this.reconsumeCodePoint(codePoint);\n                return this.consumeIdentLikeToken();\n            }\n            return { type: 6 /* DELIM_TOKEN */, value: fromCodePoint$1(codePoint) };\n        };\n        Tokenizer.prototype.consumeCodePoint = function () {\n            var value = this._value.shift();\n            return typeof value === 'undefined' ? -1 : value;\n        };\n        Tokenizer.prototype.reconsumeCodePoint = function (codePoint) {\n            this._value.unshift(codePoint);\n        };\n        Tokenizer.prototype.peekCodePoint = function (delta) {\n            if (delta >= this._value.length) {\n                return -1;\n            }\n            return this._value[delta];\n        };\n        Tokenizer.prototype.consumeUnicodeRangeToken = function () {\n            var digits = [];\n            var codePoint = this.consumeCodePoint();\n            while (isHex(codePoint) && digits.length < 6) {\n                digits.push(codePoint);\n                codePoint = this.consumeCodePoint();\n            }\n            var questionMarks = false;\n            while (codePoint === QUESTION_MARK && digits.length < 6) {\n                digits.push(codePoint);\n                codePoint = this.consumeCodePoint();\n                questionMarks = true;\n            }\n            if (questionMarks) {\n                var start_1 = parseInt(fromCodePoint$1.apply(void 0, digits.map(function (digit) { return (digit === QUESTION_MARK ? ZERO : digit); })), 16);\n                var end = parseInt(fromCodePoint$1.apply(void 0, digits.map(function (digit) { return (digit === QUESTION_MARK ? F : digit); })), 16);\n                return { type: 30 /* UNICODE_RANGE_TOKEN */, start: start_1, end: end };\n            }\n            var start = parseInt(fromCodePoint$1.apply(void 0, digits), 16);\n            if (this.peekCodePoint(0) === HYPHEN_MINUS && isHex(this.peekCodePoint(1))) {\n                this.consumeCodePoint();\n                codePoint = this.consumeCodePoint();\n                var endDigits = [];\n                while (isHex(codePoint) && endDigits.length < 6) {\n                    endDigits.push(codePoint);\n                    codePoint = this.consumeCodePoint();\n                }\n                var end = parseInt(fromCodePoint$1.apply(void 0, endDigits), 16);\n                return { type: 30 /* UNICODE_RANGE_TOKEN */, start: start, end: end };\n            }\n            else {\n                return { type: 30 /* UNICODE_RANGE_TOKEN */, start: start, end: start };\n            }\n        };\n        Tokenizer.prototype.consumeIdentLikeToken = function () {\n            var value = this.consumeName();\n            if (value.toLowerCase() === 'url' && this.peekCodePoint(0) === LEFT_PARENTHESIS) {\n                this.consumeCodePoint();\n                return this.consumeUrlToken();\n            }\n            else if (this.peekCodePoint(0) === LEFT_PARENTHESIS) {\n                this.consumeCodePoint();\n                return { type: 19 /* FUNCTION_TOKEN */, value: value };\n            }\n            return { type: 20 /* IDENT_TOKEN */, value: value };\n        };\n        Tokenizer.prototype.consumeUrlToken = function () {\n            var value = [];\n            this.consumeWhiteSpace();\n            if (this.peekCodePoint(0) === EOF) {\n                return { type: 22 /* URL_TOKEN */, value: '' };\n            }\n            var next = this.peekCodePoint(0);\n            if (next === APOSTROPHE || next === QUOTATION_MARK) {\n                var stringToken = this.consumeStringToken(this.consumeCodePoint());\n                if (stringToken.type === 0 /* STRING_TOKEN */) {\n                    this.consumeWhiteSpace();\n                    if (this.peekCodePoint(0) === EOF || this.peekCodePoint(0) === RIGHT_PARENTHESIS) {\n                        this.consumeCodePoint();\n                        return { type: 22 /* URL_TOKEN */, value: stringToken.value };\n                    }\n                }\n                this.consumeBadUrlRemnants();\n                return BAD_URL_TOKEN;\n            }\n            while (true) {\n                var codePoint = this.consumeCodePoint();\n                if (codePoint === EOF || codePoint === RIGHT_PARENTHESIS) {\n                    return { type: 22 /* URL_TOKEN */, value: fromCodePoint$1.apply(void 0, value) };\n                }\n                else if (isWhiteSpace(codePoint)) {\n                    this.consumeWhiteSpace();\n                    if (this.peekCodePoint(0) === EOF || this.peekCodePoint(0) === RIGHT_PARENTHESIS) {\n                        this.consumeCodePoint();\n                        return { type: 22 /* URL_TOKEN */, value: fromCodePoint$1.apply(void 0, value) };\n                    }\n                    this.consumeBadUrlRemnants();\n                    return BAD_URL_TOKEN;\n                }\n                else if (codePoint === QUOTATION_MARK ||\n                    codePoint === APOSTROPHE ||\n                    codePoint === LEFT_PARENTHESIS ||\n                    isNonPrintableCodePoint(codePoint)) {\n                    this.consumeBadUrlRemnants();\n                    return BAD_URL_TOKEN;\n                }\n                else if (codePoint === REVERSE_SOLIDUS) {\n                    if (isValidEscape(codePoint, this.peekCodePoint(0))) {\n                        value.push(this.consumeEscapedCodePoint());\n                    }\n                    else {\n                        this.consumeBadUrlRemnants();\n                        return BAD_URL_TOKEN;\n                    }\n                }\n                else {\n                    value.push(codePoint);\n                }\n            }\n        };\n        Tokenizer.prototype.consumeWhiteSpace = function () {\n            while (isWhiteSpace(this.peekCodePoint(0))) {\n                this.consumeCodePoint();\n            }\n        };\n        Tokenizer.prototype.consumeBadUrlRemnants = function () {\n            while (true) {\n                var codePoint = this.consumeCodePoint();\n                if (codePoint === RIGHT_PARENTHESIS || codePoint === EOF) {\n                    return;\n                }\n                if (isValidEscape(codePoint, this.peekCodePoint(0))) {\n                    this.consumeEscapedCodePoint();\n                }\n            }\n        };\n        Tokenizer.prototype.consumeStringSlice = function (count) {\n            var SLICE_STACK_SIZE = 50000;\n            var value = '';\n            while (count > 0) {\n                var amount = Math.min(SLICE_STACK_SIZE, count);\n                value += fromCodePoint$1.apply(void 0, this._value.splice(0, amount));\n                count -= amount;\n            }\n            this._value.shift();\n            return value;\n        };\n        Tokenizer.prototype.consumeStringToken = function (endingCodePoint) {\n            var value = '';\n            var i = 0;\n            do {\n                var codePoint = this._value[i];\n                if (codePoint === EOF || codePoint === undefined || codePoint === endingCodePoint) {\n                    value += this.consumeStringSlice(i);\n                    return { type: 0 /* STRING_TOKEN */, value: value };\n                }\n                if (codePoint === LINE_FEED) {\n                    this._value.splice(0, i);\n                    return BAD_STRING_TOKEN;\n                }\n                if (codePoint === REVERSE_SOLIDUS) {\n                    var next = this._value[i + 1];\n                    if (next !== EOF && next !== undefined) {\n                        if (next === LINE_FEED) {\n                            value += this.consumeStringSlice(i);\n                            i = -1;\n                            this._value.shift();\n                        }\n                        else if (isValidEscape(codePoint, next)) {\n                            value += this.consumeStringSlice(i);\n                            value += fromCodePoint$1(this.consumeEscapedCodePoint());\n                            i = -1;\n                        }\n                    }\n                }\n                i++;\n            } while (true);\n        };\n        Tokenizer.prototype.consumeNumber = function () {\n            var repr = [];\n            var type = FLAG_INTEGER;\n            var c1 = this.peekCodePoint(0);\n            if (c1 === PLUS_SIGN || c1 === HYPHEN_MINUS) {\n                repr.push(this.consumeCodePoint());\n            }\n            while (isDigit(this.peekCodePoint(0))) {\n                repr.push(this.consumeCodePoint());\n            }\n            c1 = this.peekCodePoint(0);\n            var c2 = this.peekCodePoint(1);\n            if (c1 === FULL_STOP && isDigit(c2)) {\n                repr.push(this.consumeCodePoint(), this.consumeCodePoint());\n                type = FLAG_NUMBER;\n                while (isDigit(this.peekCodePoint(0))) {\n                    repr.push(this.consumeCodePoint());\n                }\n            }\n            c1 = this.peekCodePoint(0);\n            c2 = this.peekCodePoint(1);\n            var c3 = this.peekCodePoint(2);\n            if ((c1 === E || c1 === e) && (((c2 === PLUS_SIGN || c2 === HYPHEN_MINUS) && isDigit(c3)) || isDigit(c2))) {\n                repr.push(this.consumeCodePoint(), this.consumeCodePoint());\n                type = FLAG_NUMBER;\n                while (isDigit(this.peekCodePoint(0))) {\n                    repr.push(this.consumeCodePoint());\n                }\n            }\n            return [stringToNumber(repr), type];\n        };\n        Tokenizer.prototype.consumeNumericToken = function () {\n            var _a = this.consumeNumber(), number = _a[0], flags = _a[1];\n            var c1 = this.peekCodePoint(0);\n            var c2 = this.peekCodePoint(1);\n            var c3 = this.peekCodePoint(2);\n            if (isIdentifierStart(c1, c2, c3)) {\n                var unit = this.consumeName();\n                return { type: 15 /* DIMENSION_TOKEN */, number: number, flags: flags, unit: unit };\n            }\n            if (c1 === PERCENTAGE_SIGN) {\n                this.consumeCodePoint();\n                return { type: 16 /* PERCENTAGE_TOKEN */, number: number, flags: flags };\n            }\n            return { type: 17 /* NUMBER_TOKEN */, number: number, flags: flags };\n        };\n        Tokenizer.prototype.consumeEscapedCodePoint = function () {\n            var codePoint = this.consumeCodePoint();\n            if (isHex(codePoint)) {\n                var hex = fromCodePoint$1(codePoint);\n                while (isHex(this.peekCodePoint(0)) && hex.length < 6) {\n                    hex += fromCodePoint$1(this.consumeCodePoint());\n                }\n                if (isWhiteSpace(this.peekCodePoint(0))) {\n                    this.consumeCodePoint();\n                }\n                var hexCodePoint = parseInt(hex, 16);\n                if (hexCodePoint === 0 || isSurrogateCodePoint(hexCodePoint) || hexCodePoint > 0x10ffff) {\n                    return REPLACEMENT_CHARACTER;\n                }\n                return hexCodePoint;\n            }\n            if (codePoint === EOF) {\n                return REPLACEMENT_CHARACTER;\n            }\n            return codePoint;\n        };\n        Tokenizer.prototype.consumeName = function () {\n            var result = '';\n            while (true) {\n                var codePoint = this.consumeCodePoint();\n                if (isNameCodePoint(codePoint)) {\n                    result += fromCodePoint$1(codePoint);\n                }\n                else if (isValidEscape(codePoint, this.peekCodePoint(0))) {\n                    result += fromCodePoint$1(this.consumeEscapedCodePoint());\n                }\n                else {\n                    this.reconsumeCodePoint(codePoint);\n                    return result;\n                }\n            }\n        };\n        return Tokenizer;\n    }());\n\n    var Parser = /** @class */ (function () {\n        function Parser(tokens) {\n            this._tokens = tokens;\n        }\n        Parser.create = function (value) {\n            var tokenizer = new Tokenizer();\n            tokenizer.write(value);\n            return new Parser(tokenizer.read());\n        };\n        Parser.parseValue = function (value) {\n            return Parser.create(value).parseComponentValue();\n        };\n        Parser.parseValues = function (value) {\n            return Parser.create(value).parseComponentValues();\n        };\n        Parser.prototype.parseComponentValue = function () {\n            var token = this.consumeToken();\n            while (token.type === 31 /* WHITESPACE_TOKEN */) {\n                token = this.consumeToken();\n            }\n            if (token.type === 32 /* EOF_TOKEN */) {\n                throw new SyntaxError(\"Error parsing CSS component value, unexpected EOF\");\n            }\n            this.reconsumeToken(token);\n            var value = this.consumeComponentValue();\n            do {\n                token = this.consumeToken();\n            } while (token.type === 31 /* WHITESPACE_TOKEN */);\n            if (token.type === 32 /* EOF_TOKEN */) {\n                return value;\n            }\n            throw new SyntaxError(\"Error parsing CSS component value, multiple values found when expecting only one\");\n        };\n        Parser.prototype.parseComponentValues = function () {\n            var values = [];\n            while (true) {\n                var value = this.consumeComponentValue();\n                if (value.type === 32 /* EOF_TOKEN */) {\n                    return values;\n                }\n                values.push(value);\n                values.push();\n            }\n        };\n        Parser.prototype.consumeComponentValue = function () {\n            var token = this.consumeToken();\n            switch (token.type) {\n                case 11 /* LEFT_CURLY_BRACKET_TOKEN */:\n                case 28 /* LEFT_SQUARE_BRACKET_TOKEN */:\n                case 2 /* LEFT_PARENTHESIS_TOKEN */:\n                    return this.consumeSimpleBlock(token.type);\n                case 19 /* FUNCTION_TOKEN */:\n                    return this.consumeFunction(token);\n            }\n            return token;\n        };\n        Parser.prototype.consumeSimpleBlock = function (type) {\n            var block = { type: type, values: [] };\n            var token = this.consumeToken();\n            while (true) {\n                if (token.type === 32 /* EOF_TOKEN */ || isEndingTokenFor(token, type)) {\n                    return block;\n                }\n                this.reconsumeToken(token);\n                block.values.push(this.consumeComponentValue());\n                token = this.consumeToken();\n            }\n        };\n        Parser.prototype.consumeFunction = function (functionToken) {\n            var cssFunction = {\n                name: functionToken.value,\n                values: [],\n                type: 18 /* FUNCTION */\n            };\n            while (true) {\n                var token = this.consumeToken();\n                if (token.type === 32 /* EOF_TOKEN */ || token.type === 3 /* RIGHT_PARENTHESIS_TOKEN */) {\n                    return cssFunction;\n                }\n                this.reconsumeToken(token);\n                cssFunction.values.push(this.consumeComponentValue());\n            }\n        };\n        Parser.prototype.consumeToken = function () {\n            var token = this._tokens.shift();\n            return typeof token === 'undefined' ? EOF_TOKEN : token;\n        };\n        Parser.prototype.reconsumeToken = function (token) {\n            this._tokens.unshift(token);\n        };\n        return Parser;\n    }());\n    var isDimensionToken = function (token) { return token.type === 15 /* DIMENSION_TOKEN */; };\n    var isNumberToken = function (token) { return token.type === 17 /* NUMBER_TOKEN */; };\n    var isIdentToken = function (token) { return token.type === 20 /* IDENT_TOKEN */; };\n    var isStringToken = function (token) { return token.type === 0 /* STRING_TOKEN */; };\n    var isIdentWithValue = function (token, value) {\n        return isIdentToken(token) && token.value === value;\n    };\n    var nonWhiteSpace = function (token) { return token.type !== 31 /* WHITESPACE_TOKEN */; };\n    var nonFunctionArgSeparator = function (token) {\n        return token.type !== 31 /* WHITESPACE_TOKEN */ && token.type !== 4 /* COMMA_TOKEN */;\n    };\n    var parseFunctionArgs = function (tokens) {\n        var args = [];\n        var arg = [];\n        tokens.forEach(function (token) {\n            if (token.type === 4 /* COMMA_TOKEN */) {\n                if (arg.length === 0) {\n                    throw new Error(\"Error parsing function args, zero tokens for arg\");\n                }\n                args.push(arg);\n                arg = [];\n                return;\n            }\n            if (token.type !== 31 /* WHITESPACE_TOKEN */) {\n                arg.push(token);\n            }\n        });\n        if (arg.length) {\n            args.push(arg);\n        }\n        return args;\n    };\n    var isEndingTokenFor = function (token, type) {\n        if (type === 11 /* LEFT_CURLY_BRACKET_TOKEN */ && token.type === 12 /* RIGHT_CURLY_BRACKET_TOKEN */) {\n            return true;\n        }\n        if (type === 28 /* LEFT_SQUARE_BRACKET_TOKEN */ && token.type === 29 /* RIGHT_SQUARE_BRACKET_TOKEN */) {\n            return true;\n        }\n        return type === 2 /* LEFT_PARENTHESIS_TOKEN */ && token.type === 3 /* RIGHT_PARENTHESIS_TOKEN */;\n    };\n\n    var isLength = function (token) {\n        return token.type === 17 /* NUMBER_TOKEN */ || token.type === 15 /* DIMENSION_TOKEN */;\n    };\n\n    var isLengthPercentage = function (token) {\n        return token.type === 16 /* PERCENTAGE_TOKEN */ || isLength(token);\n    };\n    var parseLengthPercentageTuple = function (tokens) {\n        return tokens.length > 1 ? [tokens[0], tokens[1]] : [tokens[0]];\n    };\n    var ZERO_LENGTH = {\n        type: 17 /* NUMBER_TOKEN */,\n        number: 0,\n        flags: FLAG_INTEGER\n    };\n    var FIFTY_PERCENT = {\n        type: 16 /* PERCENTAGE_TOKEN */,\n        number: 50,\n        flags: FLAG_INTEGER\n    };\n    var HUNDRED_PERCENT = {\n        type: 16 /* PERCENTAGE_TOKEN */,\n        number: 100,\n        flags: FLAG_INTEGER\n    };\n    var getAbsoluteValueForTuple = function (tuple, width, height) {\n        var x = tuple[0], y = tuple[1];\n        return [getAbsoluteValue(x, width), getAbsoluteValue(typeof y !== 'undefined' ? y : x, height)];\n    };\n    var getAbsoluteValue = function (token, parent) {\n        if (token.type === 16 /* PERCENTAGE_TOKEN */) {\n            return (token.number / 100) * parent;\n        }\n        if (isDimensionToken(token)) {\n            switch (token.unit) {\n                case 'rem':\n                case 'em':\n                    return 16 * token.number; // TODO use correct font-size\n                case 'px':\n                default:\n                    return token.number;\n            }\n        }\n        return token.number;\n    };\n\n    var DEG = 'deg';\n    var GRAD = 'grad';\n    var RAD = 'rad';\n    var TURN = 'turn';\n    var angle = {\n        name: 'angle',\n        parse: function (_context, value) {\n            if (value.type === 15 /* DIMENSION_TOKEN */) {\n                switch (value.unit) {\n                    case DEG:\n                        return (Math.PI * value.number) / 180;\n                    case GRAD:\n                        return (Math.PI / 200) * value.number;\n                    case RAD:\n                        return value.number;\n                    case TURN:\n                        return Math.PI * 2 * value.number;\n                }\n            }\n            throw new Error(\"Unsupported angle type\");\n        }\n    };\n    var isAngle = function (value) {\n        if (value.type === 15 /* DIMENSION_TOKEN */) {\n            if (value.unit === DEG || value.unit === GRAD || value.unit === RAD || value.unit === TURN) {\n                return true;\n            }\n        }\n        return false;\n    };\n    var parseNamedSide = function (tokens) {\n        var sideOrCorner = tokens\n            .filter(isIdentToken)\n            .map(function (ident) { return ident.value; })\n            .join(' ');\n        switch (sideOrCorner) {\n            case 'to bottom right':\n            case 'to right bottom':\n            case 'left top':\n            case 'top left':\n                return [ZERO_LENGTH, ZERO_LENGTH];\n            case 'to top':\n            case 'bottom':\n                return deg(0);\n            case 'to bottom left':\n            case 'to left bottom':\n            case 'right top':\n            case 'top right':\n                return [ZERO_LENGTH, HUNDRED_PERCENT];\n            case 'to right':\n            case 'left':\n                return deg(90);\n            case 'to top left':\n            case 'to left top':\n            case 'right bottom':\n            case 'bottom right':\n                return [HUNDRED_PERCENT, HUNDRED_PERCENT];\n            case 'to bottom':\n            case 'top':\n                return deg(180);\n            case 'to top right':\n            case 'to right top':\n            case 'left bottom':\n            case 'bottom left':\n                return [HUNDRED_PERCENT, ZERO_LENGTH];\n            case 'to left':\n            case 'right':\n                return deg(270);\n        }\n        return 0;\n    };\n    var deg = function (deg) { return (Math.PI * deg) / 180; };\n\n    var color$1 = {\n        name: 'color',\n        parse: function (context, value) {\n            if (value.type === 18 /* FUNCTION */) {\n                var colorFunction = SUPPORTED_COLOR_FUNCTIONS[value.name];\n                if (typeof colorFunction === 'undefined') {\n                    throw new Error(\"Attempting to parse an unsupported color function \\\"\" + value.name + \"\\\"\");\n                }\n                return colorFunction(context, value.values);\n            }\n            if (value.type === 5 /* HASH_TOKEN */) {\n                if (value.value.length === 3) {\n                    var r = value.value.substring(0, 1);\n                    var g = value.value.substring(1, 2);\n                    var b = value.value.substring(2, 3);\n                    return pack(parseInt(r + r, 16), parseInt(g + g, 16), parseInt(b + b, 16), 1);\n                }\n                if (value.value.length === 4) {\n                    var r = value.value.substring(0, 1);\n                    var g = value.value.substring(1, 2);\n                    var b = value.value.substring(2, 3);\n                    var a = value.value.substring(3, 4);\n                    return pack(parseInt(r + r, 16), parseInt(g + g, 16), parseInt(b + b, 16), parseInt(a + a, 16) / 255);\n                }\n                if (value.value.length === 6) {\n                    var r = value.value.substring(0, 2);\n                    var g = value.value.substring(2, 4);\n                    var b = value.value.substring(4, 6);\n                    return pack(parseInt(r, 16), parseInt(g, 16), parseInt(b, 16), 1);\n                }\n                if (value.value.length === 8) {\n                    var r = value.value.substring(0, 2);\n                    var g = value.value.substring(2, 4);\n                    var b = value.value.substring(4, 6);\n                    var a = value.value.substring(6, 8);\n                    return pack(parseInt(r, 16), parseInt(g, 16), parseInt(b, 16), parseInt(a, 16) / 255);\n                }\n            }\n            if (value.type === 20 /* IDENT_TOKEN */) {\n                var namedColor = COLORS[value.value.toUpperCase()];\n                if (typeof namedColor !== 'undefined') {\n                    return namedColor;\n                }\n            }\n            return COLORS.TRANSPARENT;\n        }\n    };\n    var isTransparent = function (color) { return (0xff & color) === 0; };\n    var asString = function (color) {\n        var alpha = 0xff & color;\n        var blue = 0xff & (color >> 8);\n        var green = 0xff & (color >> 16);\n        var red = 0xff & (color >> 24);\n        return alpha < 255 ? \"rgba(\" + red + \",\" + green + \",\" + blue + \",\" + alpha / 255 + \")\" : \"rgb(\" + red + \",\" + green + \",\" + blue + \")\";\n    };\n    var pack = function (r, g, b, a) {\n        return ((r << 24) | (g << 16) | (b << 8) | (Math.round(a * 255) << 0)) >>> 0;\n    };\n    var getTokenColorValue = function (token, i) {\n        if (token.type === 17 /* NUMBER_TOKEN */) {\n            return token.number;\n        }\n        if (token.type === 16 /* PERCENTAGE_TOKEN */) {\n            var max = i === 3 ? 1 : 255;\n            return i === 3 ? (token.number / 100) * max : Math.round((token.number / 100) * max);\n        }\n        return 0;\n    };\n    var rgb = function (_context, args) {\n        var tokens = args.filter(nonFunctionArgSeparator);\n        if (tokens.length === 3) {\n            var _a = tokens.map(getTokenColorValue), r = _a[0], g = _a[1], b = _a[2];\n            return pack(r, g, b, 1);\n        }\n        if (tokens.length === 4) {\n            var _b = tokens.map(getTokenColorValue), r = _b[0], g = _b[1], b = _b[2], a = _b[3];\n            return pack(r, g, b, a);\n        }\n        return 0;\n    };\n    function hue2rgb(t1, t2, hue) {\n        if (hue < 0) {\n            hue += 1;\n        }\n        if (hue >= 1) {\n            hue -= 1;\n        }\n        if (hue < 1 / 6) {\n            return (t2 - t1) * hue * 6 + t1;\n        }\n        else if (hue < 1 / 2) {\n            return t2;\n        }\n        else if (hue < 2 / 3) {\n            return (t2 - t1) * 6 * (2 / 3 - hue) + t1;\n        }\n        else {\n            return t1;\n        }\n    }\n    var hsl = function (context, args) {\n        var tokens = args.filter(nonFunctionArgSeparator);\n        var hue = tokens[0], saturation = tokens[1], lightness = tokens[2], alpha = tokens[3];\n        var h = (hue.type === 17 /* NUMBER_TOKEN */ ? deg(hue.number) : angle.parse(context, hue)) / (Math.PI * 2);\n        var s = isLengthPercentage(saturation) ? saturation.number / 100 : 0;\n        var l = isLengthPercentage(lightness) ? lightness.number / 100 : 0;\n        var a = typeof alpha !== 'undefined' && isLengthPercentage(alpha) ? getAbsoluteValue(alpha, 1) : 1;\n        if (s === 0) {\n            return pack(l * 255, l * 255, l * 255, 1);\n        }\n        var t2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n        var t1 = l * 2 - t2;\n        var r = hue2rgb(t1, t2, h + 1 / 3);\n        var g = hue2rgb(t1, t2, h);\n        var b = hue2rgb(t1, t2, h - 1 / 3);\n        return pack(r * 255, g * 255, b * 255, a);\n    };\n    var SUPPORTED_COLOR_FUNCTIONS = {\n        hsl: hsl,\n        hsla: hsl,\n        rgb: rgb,\n        rgba: rgb\n    };\n    var parseColor = function (context, value) {\n        return color$1.parse(context, Parser.create(value).parseComponentValue());\n    };\n    var COLORS = {\n        ALICEBLUE: 0xf0f8ffff,\n        ANTIQUEWHITE: 0xfaebd7ff,\n        AQUA: 0x00ffffff,\n        AQUAMARINE: 0x7fffd4ff,\n        AZURE: 0xf0ffffff,\n        BEIGE: 0xf5f5dcff,\n        BISQUE: 0xffe4c4ff,\n        BLACK: 0x000000ff,\n        BLANCHEDALMOND: 0xffebcdff,\n        BLUE: 0x0000ffff,\n        BLUEVIOLET: 0x8a2be2ff,\n        BROWN: 0xa52a2aff,\n        BURLYWOOD: 0xdeb887ff,\n        CADETBLUE: 0x5f9ea0ff,\n        CHARTREUSE: 0x7fff00ff,\n        CHOCOLATE: 0xd2691eff,\n        CORAL: 0xff7f50ff,\n        CORNFLOWERBLUE: 0x6495edff,\n        CORNSILK: 0xfff8dcff,\n        CRIMSON: 0xdc143cff,\n        CYAN: 0x00ffffff,\n        DARKBLUE: 0x00008bff,\n        DARKCYAN: 0x008b8bff,\n        DARKGOLDENROD: 0xb886bbff,\n        DARKGRAY: 0xa9a9a9ff,\n        DARKGREEN: 0x006400ff,\n        DARKGREY: 0xa9a9a9ff,\n        DARKKHAKI: 0xbdb76bff,\n        DARKMAGENTA: 0x8b008bff,\n        DARKOLIVEGREEN: 0x556b2fff,\n        DARKORANGE: 0xff8c00ff,\n        DARKORCHID: 0x9932ccff,\n        DARKRED: 0x8b0000ff,\n        DARKSALMON: 0xe9967aff,\n        DARKSEAGREEN: 0x8fbc8fff,\n        DARKSLATEBLUE: 0x483d8bff,\n        DARKSLATEGRAY: 0x2f4f4fff,\n        DARKSLATEGREY: 0x2f4f4fff,\n        DARKTURQUOISE: 0x00ced1ff,\n        DARKVIOLET: 0x9400d3ff,\n        DEEPPINK: 0xff1493ff,\n        DEEPSKYBLUE: 0x00bfffff,\n        DIMGRAY: 0x696969ff,\n        DIMGREY: 0x696969ff,\n        DODGERBLUE: 0x1e90ffff,\n        FIREBRICK: 0xb22222ff,\n        FLORALWHITE: 0xfffaf0ff,\n        FORESTGREEN: 0x228b22ff,\n        FUCHSIA: 0xff00ffff,\n        GAINSBORO: 0xdcdcdcff,\n        GHOSTWHITE: 0xf8f8ffff,\n        GOLD: 0xffd700ff,\n        GOLDENROD: 0xdaa520ff,\n        GRAY: 0x808080ff,\n        GREEN: 0x008000ff,\n        GREENYELLOW: 0xadff2fff,\n        GREY: 0x808080ff,\n        HONEYDEW: 0xf0fff0ff,\n        HOTPINK: 0xff69b4ff,\n        INDIANRED: 0xcd5c5cff,\n        INDIGO: 0x4b0082ff,\n        IVORY: 0xfffff0ff,\n        KHAKI: 0xf0e68cff,\n        LAVENDER: 0xe6e6faff,\n        LAVENDERBLUSH: 0xfff0f5ff,\n        LAWNGREEN: 0x7cfc00ff,\n        LEMONCHIFFON: 0xfffacdff,\n        LIGHTBLUE: 0xadd8e6ff,\n        LIGHTCORAL: 0xf08080ff,\n        LIGHTCYAN: 0xe0ffffff,\n        LIGHTGOLDENRODYELLOW: 0xfafad2ff,\n        LIGHTGRAY: 0xd3d3d3ff,\n        LIGHTGREEN: 0x90ee90ff,\n        LIGHTGREY: 0xd3d3d3ff,\n        LIGHTPINK: 0xffb6c1ff,\n        LIGHTSALMON: 0xffa07aff,\n        LIGHTSEAGREEN: 0x20b2aaff,\n        LIGHTSKYBLUE: 0x87cefaff,\n        LIGHTSLATEGRAY: 0x778899ff,\n        LIGHTSLATEGREY: 0x778899ff,\n        LIGHTSTEELBLUE: 0xb0c4deff,\n        LIGHTYELLOW: 0xffffe0ff,\n        LIME: 0x00ff00ff,\n        LIMEGREEN: 0x32cd32ff,\n        LINEN: 0xfaf0e6ff,\n        MAGENTA: 0xff00ffff,\n        MAROON: 0x800000ff,\n        MEDIUMAQUAMARINE: 0x66cdaaff,\n        MEDIUMBLUE: 0x0000cdff,\n        MEDIUMORCHID: 0xba55d3ff,\n        MEDIUMPURPLE: 0x9370dbff,\n        MEDIUMSEAGREEN: 0x3cb371ff,\n        MEDIUMSLATEBLUE: 0x7b68eeff,\n        MEDIUMSPRINGGREEN: 0x00fa9aff,\n        MEDIUMTURQUOISE: 0x48d1ccff,\n        MEDIUMVIOLETRED: 0xc71585ff,\n        MIDNIGHTBLUE: 0x191970ff,\n        MINTCREAM: 0xf5fffaff,\n        MISTYROSE: 0xffe4e1ff,\n        MOCCASIN: 0xffe4b5ff,\n        NAVAJOWHITE: 0xffdeadff,\n        NAVY: 0x000080ff,\n        OLDLACE: 0xfdf5e6ff,\n        OLIVE: 0x808000ff,\n        OLIVEDRAB: 0x6b8e23ff,\n        ORANGE: 0xffa500ff,\n        ORANGERED: 0xff4500ff,\n        ORCHID: 0xda70d6ff,\n        PALEGOLDENROD: 0xeee8aaff,\n        PALEGREEN: 0x98fb98ff,\n        PALETURQUOISE: 0xafeeeeff,\n        PALEVIOLETRED: 0xdb7093ff,\n        PAPAYAWHIP: 0xffefd5ff,\n        PEACHPUFF: 0xffdab9ff,\n        PERU: 0xcd853fff,\n        PINK: 0xffc0cbff,\n        PLUM: 0xdda0ddff,\n        POWDERBLUE: 0xb0e0e6ff,\n        PURPLE: 0x800080ff,\n        REBECCAPURPLE: 0x663399ff,\n        RED: 0xff0000ff,\n        ROSYBROWN: 0xbc8f8fff,\n        ROYALBLUE: 0x4169e1ff,\n        SADDLEBROWN: 0x8b4513ff,\n        SALMON: 0xfa8072ff,\n        SANDYBROWN: 0xf4a460ff,\n        SEAGREEN: 0x2e8b57ff,\n        SEASHELL: 0xfff5eeff,\n        SIENNA: 0xa0522dff,\n        SILVER: 0xc0c0c0ff,\n        SKYBLUE: 0x87ceebff,\n        SLATEBLUE: 0x6a5acdff,\n        SLATEGRAY: 0x708090ff,\n        SLATEGREY: 0x708090ff,\n        SNOW: 0xfffafaff,\n        SPRINGGREEN: 0x00ff7fff,\n        STEELBLUE: 0x4682b4ff,\n        TAN: 0xd2b48cff,\n        TEAL: 0x008080ff,\n        THISTLE: 0xd8bfd8ff,\n        TOMATO: 0xff6347ff,\n        TRANSPARENT: 0x00000000,\n        TURQUOISE: 0x40e0d0ff,\n        VIOLET: 0xee82eeff,\n        WHEAT: 0xf5deb3ff,\n        WHITE: 0xffffffff,\n        WHITESMOKE: 0xf5f5f5ff,\n        YELLOW: 0xffff00ff,\n        YELLOWGREEN: 0x9acd32ff\n    };\n\n    var backgroundClip = {\n        name: 'background-clip',\n        initialValue: 'border-box',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return tokens.map(function (token) {\n                if (isIdentToken(token)) {\n                    switch (token.value) {\n                        case 'padding-box':\n                            return 1 /* PADDING_BOX */;\n                        case 'content-box':\n                            return 2 /* CONTENT_BOX */;\n                    }\n                }\n                return 0 /* BORDER_BOX */;\n            });\n        }\n    };\n\n    var backgroundColor = {\n        name: \"background-color\",\n        initialValue: 'transparent',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'color'\n    };\n\n    var parseColorStop = function (context, args) {\n        var color = color$1.parse(context, args[0]);\n        var stop = args[1];\n        return stop && isLengthPercentage(stop) ? { color: color, stop: stop } : { color: color, stop: null };\n    };\n    var processColorStops = function (stops, lineLength) {\n        var first = stops[0];\n        var last = stops[stops.length - 1];\n        if (first.stop === null) {\n            first.stop = ZERO_LENGTH;\n        }\n        if (last.stop === null) {\n            last.stop = HUNDRED_PERCENT;\n        }\n        var processStops = [];\n        var previous = 0;\n        for (var i = 0; i < stops.length; i++) {\n            var stop_1 = stops[i].stop;\n            if (stop_1 !== null) {\n                var absoluteValue = getAbsoluteValue(stop_1, lineLength);\n                if (absoluteValue > previous) {\n                    processStops.push(absoluteValue);\n                }\n                else {\n                    processStops.push(previous);\n                }\n                previous = absoluteValue;\n            }\n            else {\n                processStops.push(null);\n            }\n        }\n        var gapBegin = null;\n        for (var i = 0; i < processStops.length; i++) {\n            var stop_2 = processStops[i];\n            if (stop_2 === null) {\n                if (gapBegin === null) {\n                    gapBegin = i;\n                }\n            }\n            else if (gapBegin !== null) {\n                var gapLength = i - gapBegin;\n                var beforeGap = processStops[gapBegin - 1];\n                var gapValue = (stop_2 - beforeGap) / (gapLength + 1);\n                for (var g = 1; g <= gapLength; g++) {\n                    processStops[gapBegin + g - 1] = gapValue * g;\n                }\n                gapBegin = null;\n            }\n        }\n        return stops.map(function (_a, i) {\n            var color = _a.color;\n            return { color: color, stop: Math.max(Math.min(1, processStops[i] / lineLength), 0) };\n        });\n    };\n    var getAngleFromCorner = function (corner, width, height) {\n        var centerX = width / 2;\n        var centerY = height / 2;\n        var x = getAbsoluteValue(corner[0], width) - centerX;\n        var y = centerY - getAbsoluteValue(corner[1], height);\n        return (Math.atan2(y, x) + Math.PI * 2) % (Math.PI * 2);\n    };\n    var calculateGradientDirection = function (angle, width, height) {\n        var radian = typeof angle === 'number' ? angle : getAngleFromCorner(angle, width, height);\n        var lineLength = Math.abs(width * Math.sin(radian)) + Math.abs(height * Math.cos(radian));\n        var halfWidth = width / 2;\n        var halfHeight = height / 2;\n        var halfLineLength = lineLength / 2;\n        var yDiff = Math.sin(radian - Math.PI / 2) * halfLineLength;\n        var xDiff = Math.cos(radian - Math.PI / 2) * halfLineLength;\n        return [lineLength, halfWidth - xDiff, halfWidth + xDiff, halfHeight - yDiff, halfHeight + yDiff];\n    };\n    var distance = function (a, b) { return Math.sqrt(a * a + b * b); };\n    var findCorner = function (width, height, x, y, closest) {\n        var corners = [\n            [0, 0],\n            [0, height],\n            [width, 0],\n            [width, height]\n        ];\n        return corners.reduce(function (stat, corner) {\n            var cx = corner[0], cy = corner[1];\n            var d = distance(x - cx, y - cy);\n            if (closest ? d < stat.optimumDistance : d > stat.optimumDistance) {\n                return {\n                    optimumCorner: corner,\n                    optimumDistance: d\n                };\n            }\n            return stat;\n        }, {\n            optimumDistance: closest ? Infinity : -Infinity,\n            optimumCorner: null\n        }).optimumCorner;\n    };\n    var calculateRadius = function (gradient, x, y, width, height) {\n        var rx = 0;\n        var ry = 0;\n        switch (gradient.size) {\n            case 0 /* CLOSEST_SIDE */:\n                // The ending shape is sized so that that it exactly meets the side of the gradient box closest to the gradient’s center.\n                // If the shape is an ellipse, it exactly meets the closest side in each dimension.\n                if (gradient.shape === 0 /* CIRCLE */) {\n                    rx = ry = Math.min(Math.abs(x), Math.abs(x - width), Math.abs(y), Math.abs(y - height));\n                }\n                else if (gradient.shape === 1 /* ELLIPSE */) {\n                    rx = Math.min(Math.abs(x), Math.abs(x - width));\n                    ry = Math.min(Math.abs(y), Math.abs(y - height));\n                }\n                break;\n            case 2 /* CLOSEST_CORNER */:\n                // The ending shape is sized so that that it passes through the corner of the gradient box closest to the gradient’s center.\n                // If the shape is an ellipse, the ending shape is given the same aspect-ratio it would have if closest-side were specified.\n                if (gradient.shape === 0 /* CIRCLE */) {\n                    rx = ry = Math.min(distance(x, y), distance(x, y - height), distance(x - width, y), distance(x - width, y - height));\n                }\n                else if (gradient.shape === 1 /* ELLIPSE */) {\n                    // Compute the ratio ry/rx (which is to be the same as for \"closest-side\")\n                    var c = Math.min(Math.abs(y), Math.abs(y - height)) / Math.min(Math.abs(x), Math.abs(x - width));\n                    var _a = findCorner(width, height, x, y, true), cx = _a[0], cy = _a[1];\n                    rx = distance(cx - x, (cy - y) / c);\n                    ry = c * rx;\n                }\n                break;\n            case 1 /* FARTHEST_SIDE */:\n                // Same as closest-side, except the ending shape is sized based on the farthest side(s)\n                if (gradient.shape === 0 /* CIRCLE */) {\n                    rx = ry = Math.max(Math.abs(x), Math.abs(x - width), Math.abs(y), Math.abs(y - height));\n                }\n                else if (gradient.shape === 1 /* ELLIPSE */) {\n                    rx = Math.max(Math.abs(x), Math.abs(x - width));\n                    ry = Math.max(Math.abs(y), Math.abs(y - height));\n                }\n                break;\n            case 3 /* FARTHEST_CORNER */:\n                // Same as closest-corner, except the ending shape is sized based on the farthest corner.\n                // If the shape is an ellipse, the ending shape is given the same aspect ratio it would have if farthest-side were specified.\n                if (gradient.shape === 0 /* CIRCLE */) {\n                    rx = ry = Math.max(distance(x, y), distance(x, y - height), distance(x - width, y), distance(x - width, y - height));\n                }\n                else if (gradient.shape === 1 /* ELLIPSE */) {\n                    // Compute the ratio ry/rx (which is to be the same as for \"farthest-side\")\n                    var c = Math.max(Math.abs(y), Math.abs(y - height)) / Math.max(Math.abs(x), Math.abs(x - width));\n                    var _b = findCorner(width, height, x, y, false), cx = _b[0], cy = _b[1];\n                    rx = distance(cx - x, (cy - y) / c);\n                    ry = c * rx;\n                }\n                break;\n        }\n        if (Array.isArray(gradient.size)) {\n            rx = getAbsoluteValue(gradient.size[0], width);\n            ry = gradient.size.length === 2 ? getAbsoluteValue(gradient.size[1], height) : rx;\n        }\n        return [rx, ry];\n    };\n\n    var linearGradient = function (context, tokens) {\n        var angle$1 = deg(180);\n        var stops = [];\n        parseFunctionArgs(tokens).forEach(function (arg, i) {\n            if (i === 0) {\n                var firstToken = arg[0];\n                if (firstToken.type === 20 /* IDENT_TOKEN */ && firstToken.value === 'to') {\n                    angle$1 = parseNamedSide(arg);\n                    return;\n                }\n                else if (isAngle(firstToken)) {\n                    angle$1 = angle.parse(context, firstToken);\n                    return;\n                }\n            }\n            var colorStop = parseColorStop(context, arg);\n            stops.push(colorStop);\n        });\n        return { angle: angle$1, stops: stops, type: 1 /* LINEAR_GRADIENT */ };\n    };\n\n    var prefixLinearGradient = function (context, tokens) {\n        var angle$1 = deg(180);\n        var stops = [];\n        parseFunctionArgs(tokens).forEach(function (arg, i) {\n            if (i === 0) {\n                var firstToken = arg[0];\n                if (firstToken.type === 20 /* IDENT_TOKEN */ &&\n                    ['top', 'left', 'right', 'bottom'].indexOf(firstToken.value) !== -1) {\n                    angle$1 = parseNamedSide(arg);\n                    return;\n                }\n                else if (isAngle(firstToken)) {\n                    angle$1 = (angle.parse(context, firstToken) + deg(270)) % deg(360);\n                    return;\n                }\n            }\n            var colorStop = parseColorStop(context, arg);\n            stops.push(colorStop);\n        });\n        return {\n            angle: angle$1,\n            stops: stops,\n            type: 1 /* LINEAR_GRADIENT */\n        };\n    };\n\n    var webkitGradient = function (context, tokens) {\n        var angle = deg(180);\n        var stops = [];\n        var type = 1 /* LINEAR_GRADIENT */;\n        var shape = 0 /* CIRCLE */;\n        var size = 3 /* FARTHEST_CORNER */;\n        var position = [];\n        parseFunctionArgs(tokens).forEach(function (arg, i) {\n            var firstToken = arg[0];\n            if (i === 0) {\n                if (isIdentToken(firstToken) && firstToken.value === 'linear') {\n                    type = 1 /* LINEAR_GRADIENT */;\n                    return;\n                }\n                else if (isIdentToken(firstToken) && firstToken.value === 'radial') {\n                    type = 2 /* RADIAL_GRADIENT */;\n                    return;\n                }\n            }\n            if (firstToken.type === 18 /* FUNCTION */) {\n                if (firstToken.name === 'from') {\n                    var color = color$1.parse(context, firstToken.values[0]);\n                    stops.push({ stop: ZERO_LENGTH, color: color });\n                }\n                else if (firstToken.name === 'to') {\n                    var color = color$1.parse(context, firstToken.values[0]);\n                    stops.push({ stop: HUNDRED_PERCENT, color: color });\n                }\n                else if (firstToken.name === 'color-stop') {\n                    var values = firstToken.values.filter(nonFunctionArgSeparator);\n                    if (values.length === 2) {\n                        var color = color$1.parse(context, values[1]);\n                        var stop_1 = values[0];\n                        if (isNumberToken(stop_1)) {\n                            stops.push({\n                                stop: { type: 16 /* PERCENTAGE_TOKEN */, number: stop_1.number * 100, flags: stop_1.flags },\n                                color: color\n                            });\n                        }\n                    }\n                }\n            }\n        });\n        return type === 1 /* LINEAR_GRADIENT */\n            ? {\n                angle: (angle + deg(180)) % deg(360),\n                stops: stops,\n                type: type\n            }\n            : { size: size, shape: shape, stops: stops, position: position, type: type };\n    };\n\n    var CLOSEST_SIDE = 'closest-side';\n    var FARTHEST_SIDE = 'farthest-side';\n    var CLOSEST_CORNER = 'closest-corner';\n    var FARTHEST_CORNER = 'farthest-corner';\n    var CIRCLE = 'circle';\n    var ELLIPSE = 'ellipse';\n    var COVER = 'cover';\n    var CONTAIN = 'contain';\n    var radialGradient = function (context, tokens) {\n        var shape = 0 /* CIRCLE */;\n        var size = 3 /* FARTHEST_CORNER */;\n        var stops = [];\n        var position = [];\n        parseFunctionArgs(tokens).forEach(function (arg, i) {\n            var isColorStop = true;\n            if (i === 0) {\n                var isAtPosition_1 = false;\n                isColorStop = arg.reduce(function (acc, token) {\n                    if (isAtPosition_1) {\n                        if (isIdentToken(token)) {\n                            switch (token.value) {\n                                case 'center':\n                                    position.push(FIFTY_PERCENT);\n                                    return acc;\n                                case 'top':\n                                case 'left':\n                                    position.push(ZERO_LENGTH);\n                                    return acc;\n                                case 'right':\n                                case 'bottom':\n                                    position.push(HUNDRED_PERCENT);\n                                    return acc;\n                            }\n                        }\n                        else if (isLengthPercentage(token) || isLength(token)) {\n                            position.push(token);\n                        }\n                    }\n                    else if (isIdentToken(token)) {\n                        switch (token.value) {\n                            case CIRCLE:\n                                shape = 0 /* CIRCLE */;\n                                return false;\n                            case ELLIPSE:\n                                shape = 1 /* ELLIPSE */;\n                                return false;\n                            case 'at':\n                                isAtPosition_1 = true;\n                                return false;\n                            case CLOSEST_SIDE:\n                                size = 0 /* CLOSEST_SIDE */;\n                                return false;\n                            case COVER:\n                            case FARTHEST_SIDE:\n                                size = 1 /* FARTHEST_SIDE */;\n                                return false;\n                            case CONTAIN:\n                            case CLOSEST_CORNER:\n                                size = 2 /* CLOSEST_CORNER */;\n                                return false;\n                            case FARTHEST_CORNER:\n                                size = 3 /* FARTHEST_CORNER */;\n                                return false;\n                        }\n                    }\n                    else if (isLength(token) || isLengthPercentage(token)) {\n                        if (!Array.isArray(size)) {\n                            size = [];\n                        }\n                        size.push(token);\n                        return false;\n                    }\n                    return acc;\n                }, isColorStop);\n            }\n            if (isColorStop) {\n                var colorStop = parseColorStop(context, arg);\n                stops.push(colorStop);\n            }\n        });\n        return { size: size, shape: shape, stops: stops, position: position, type: 2 /* RADIAL_GRADIENT */ };\n    };\n\n    var prefixRadialGradient = function (context, tokens) {\n        var shape = 0 /* CIRCLE */;\n        var size = 3 /* FARTHEST_CORNER */;\n        var stops = [];\n        var position = [];\n        parseFunctionArgs(tokens).forEach(function (arg, i) {\n            var isColorStop = true;\n            if (i === 0) {\n                isColorStop = arg.reduce(function (acc, token) {\n                    if (isIdentToken(token)) {\n                        switch (token.value) {\n                            case 'center':\n                                position.push(FIFTY_PERCENT);\n                                return false;\n                            case 'top':\n                            case 'left':\n                                position.push(ZERO_LENGTH);\n                                return false;\n                            case 'right':\n                            case 'bottom':\n                                position.push(HUNDRED_PERCENT);\n                                return false;\n                        }\n                    }\n                    else if (isLengthPercentage(token) || isLength(token)) {\n                        position.push(token);\n                        return false;\n                    }\n                    return acc;\n                }, isColorStop);\n            }\n            else if (i === 1) {\n                isColorStop = arg.reduce(function (acc, token) {\n                    if (isIdentToken(token)) {\n                        switch (token.value) {\n                            case CIRCLE:\n                                shape = 0 /* CIRCLE */;\n                                return false;\n                            case ELLIPSE:\n                                shape = 1 /* ELLIPSE */;\n                                return false;\n                            case CONTAIN:\n                            case CLOSEST_SIDE:\n                                size = 0 /* CLOSEST_SIDE */;\n                                return false;\n                            case FARTHEST_SIDE:\n                                size = 1 /* FARTHEST_SIDE */;\n                                return false;\n                            case CLOSEST_CORNER:\n                                size = 2 /* CLOSEST_CORNER */;\n                                return false;\n                            case COVER:\n                            case FARTHEST_CORNER:\n                                size = 3 /* FARTHEST_CORNER */;\n                                return false;\n                        }\n                    }\n                    else if (isLength(token) || isLengthPercentage(token)) {\n                        if (!Array.isArray(size)) {\n                            size = [];\n                        }\n                        size.push(token);\n                        return false;\n                    }\n                    return acc;\n                }, isColorStop);\n            }\n            if (isColorStop) {\n                var colorStop = parseColorStop(context, arg);\n                stops.push(colorStop);\n            }\n        });\n        return { size: size, shape: shape, stops: stops, position: position, type: 2 /* RADIAL_GRADIENT */ };\n    };\n\n    var isLinearGradient = function (background) {\n        return background.type === 1 /* LINEAR_GRADIENT */;\n    };\n    var isRadialGradient = function (background) {\n        return background.type === 2 /* RADIAL_GRADIENT */;\n    };\n    var image = {\n        name: 'image',\n        parse: function (context, value) {\n            if (value.type === 22 /* URL_TOKEN */) {\n                var image_1 = { url: value.value, type: 0 /* URL */ };\n                context.cache.addImage(value.value);\n                return image_1;\n            }\n            if (value.type === 18 /* FUNCTION */) {\n                var imageFunction = SUPPORTED_IMAGE_FUNCTIONS[value.name];\n                if (typeof imageFunction === 'undefined') {\n                    throw new Error(\"Attempting to parse an unsupported image function \\\"\" + value.name + \"\\\"\");\n                }\n                return imageFunction(context, value.values);\n            }\n            throw new Error(\"Unsupported image type \" + value.type);\n        }\n    };\n    function isSupportedImage(value) {\n        return (!(value.type === 20 /* IDENT_TOKEN */ && value.value === 'none') &&\n            (value.type !== 18 /* FUNCTION */ || !!SUPPORTED_IMAGE_FUNCTIONS[value.name]));\n    }\n    var SUPPORTED_IMAGE_FUNCTIONS = {\n        'linear-gradient': linearGradient,\n        '-moz-linear-gradient': prefixLinearGradient,\n        '-ms-linear-gradient': prefixLinearGradient,\n        '-o-linear-gradient': prefixLinearGradient,\n        '-webkit-linear-gradient': prefixLinearGradient,\n        'radial-gradient': radialGradient,\n        '-moz-radial-gradient': prefixRadialGradient,\n        '-ms-radial-gradient': prefixRadialGradient,\n        '-o-radial-gradient': prefixRadialGradient,\n        '-webkit-radial-gradient': prefixRadialGradient,\n        '-webkit-gradient': webkitGradient\n    };\n\n    var backgroundImage = {\n        name: 'background-image',\n        initialValue: 'none',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (context, tokens) {\n            if (tokens.length === 0) {\n                return [];\n            }\n            var first = tokens[0];\n            if (first.type === 20 /* IDENT_TOKEN */ && first.value === 'none') {\n                return [];\n            }\n            return tokens\n                .filter(function (value) { return nonFunctionArgSeparator(value) && isSupportedImage(value); })\n                .map(function (value) { return image.parse(context, value); });\n        }\n    };\n\n    var backgroundOrigin = {\n        name: 'background-origin',\n        initialValue: 'border-box',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return tokens.map(function (token) {\n                if (isIdentToken(token)) {\n                    switch (token.value) {\n                        case 'padding-box':\n                            return 1 /* PADDING_BOX */;\n                        case 'content-box':\n                            return 2 /* CONTENT_BOX */;\n                    }\n                }\n                return 0 /* BORDER_BOX */;\n            });\n        }\n    };\n\n    var backgroundPosition = {\n        name: 'background-position',\n        initialValue: '0% 0%',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (_context, tokens) {\n            return parseFunctionArgs(tokens)\n                .map(function (values) { return values.filter(isLengthPercentage); })\n                .map(parseLengthPercentageTuple);\n        }\n    };\n\n    var backgroundRepeat = {\n        name: 'background-repeat',\n        initialValue: 'repeat',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return parseFunctionArgs(tokens)\n                .map(function (values) {\n                return values\n                    .filter(isIdentToken)\n                    .map(function (token) { return token.value; })\n                    .join(' ');\n            })\n                .map(parseBackgroundRepeat);\n        }\n    };\n    var parseBackgroundRepeat = function (value) {\n        switch (value) {\n            case 'no-repeat':\n                return 1 /* NO_REPEAT */;\n            case 'repeat-x':\n            case 'repeat no-repeat':\n                return 2 /* REPEAT_X */;\n            case 'repeat-y':\n            case 'no-repeat repeat':\n                return 3 /* REPEAT_Y */;\n            case 'repeat':\n            default:\n                return 0 /* REPEAT */;\n        }\n    };\n\n    var BACKGROUND_SIZE;\n    (function (BACKGROUND_SIZE) {\n        BACKGROUND_SIZE[\"AUTO\"] = \"auto\";\n        BACKGROUND_SIZE[\"CONTAIN\"] = \"contain\";\n        BACKGROUND_SIZE[\"COVER\"] = \"cover\";\n    })(BACKGROUND_SIZE || (BACKGROUND_SIZE = {}));\n    var backgroundSize = {\n        name: 'background-size',\n        initialValue: '0',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return parseFunctionArgs(tokens).map(function (values) { return values.filter(isBackgroundSizeInfoToken); });\n        }\n    };\n    var isBackgroundSizeInfoToken = function (value) {\n        return isIdentToken(value) || isLengthPercentage(value);\n    };\n\n    var borderColorForSide = function (side) { return ({\n        name: \"border-\" + side + \"-color\",\n        initialValue: 'transparent',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'color'\n    }); };\n    var borderTopColor = borderColorForSide('top');\n    var borderRightColor = borderColorForSide('right');\n    var borderBottomColor = borderColorForSide('bottom');\n    var borderLeftColor = borderColorForSide('left');\n\n    var borderRadiusForSide = function (side) { return ({\n        name: \"border-radius-\" + side,\n        initialValue: '0 0',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return parseLengthPercentageTuple(tokens.filter(isLengthPercentage));\n        }\n    }); };\n    var borderTopLeftRadius = borderRadiusForSide('top-left');\n    var borderTopRightRadius = borderRadiusForSide('top-right');\n    var borderBottomRightRadius = borderRadiusForSide('bottom-right');\n    var borderBottomLeftRadius = borderRadiusForSide('bottom-left');\n\n    var borderStyleForSide = function (side) { return ({\n        name: \"border-\" + side + \"-style\",\n        initialValue: 'solid',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, style) {\n            switch (style) {\n                case 'none':\n                    return 0 /* NONE */;\n                case 'dashed':\n                    return 2 /* DASHED */;\n                case 'dotted':\n                    return 3 /* DOTTED */;\n                case 'double':\n                    return 4 /* DOUBLE */;\n            }\n            return 1 /* SOLID */;\n        }\n    }); };\n    var borderTopStyle = borderStyleForSide('top');\n    var borderRightStyle = borderStyleForSide('right');\n    var borderBottomStyle = borderStyleForSide('bottom');\n    var borderLeftStyle = borderStyleForSide('left');\n\n    var borderWidthForSide = function (side) { return ({\n        name: \"border-\" + side + \"-width\",\n        initialValue: '0',\n        type: 0 /* VALUE */,\n        prefix: false,\n        parse: function (_context, token) {\n            if (isDimensionToken(token)) {\n                return token.number;\n            }\n            return 0;\n        }\n    }); };\n    var borderTopWidth = borderWidthForSide('top');\n    var borderRightWidth = borderWidthForSide('right');\n    var borderBottomWidth = borderWidthForSide('bottom');\n    var borderLeftWidth = borderWidthForSide('left');\n\n    var color = {\n        name: \"color\",\n        initialValue: 'transparent',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'color'\n    };\n\n    var direction = {\n        name: 'direction',\n        initialValue: 'ltr',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, direction) {\n            switch (direction) {\n                case 'rtl':\n                    return 1 /* RTL */;\n                case 'ltr':\n                default:\n                    return 0 /* LTR */;\n            }\n        }\n    };\n\n    var display = {\n        name: 'display',\n        initialValue: 'inline-block',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return tokens.filter(isIdentToken).reduce(function (bit, token) {\n                return bit | parseDisplayValue(token.value);\n            }, 0 /* NONE */);\n        }\n    };\n    var parseDisplayValue = function (display) {\n        switch (display) {\n            case 'block':\n            case '-webkit-box':\n                return 2 /* BLOCK */;\n            case 'inline':\n                return 4 /* INLINE */;\n            case 'run-in':\n                return 8 /* RUN_IN */;\n            case 'flow':\n                return 16 /* FLOW */;\n            case 'flow-root':\n                return 32 /* FLOW_ROOT */;\n            case 'table':\n                return 64 /* TABLE */;\n            case 'flex':\n            case '-webkit-flex':\n                return 128 /* FLEX */;\n            case 'grid':\n            case '-ms-grid':\n                return 256 /* GRID */;\n            case 'ruby':\n                return 512 /* RUBY */;\n            case 'subgrid':\n                return 1024 /* SUBGRID */;\n            case 'list-item':\n                return 2048 /* LIST_ITEM */;\n            case 'table-row-group':\n                return 4096 /* TABLE_ROW_GROUP */;\n            case 'table-header-group':\n                return 8192 /* TABLE_HEADER_GROUP */;\n            case 'table-footer-group':\n                return 16384 /* TABLE_FOOTER_GROUP */;\n            case 'table-row':\n                return 32768 /* TABLE_ROW */;\n            case 'table-cell':\n                return 65536 /* TABLE_CELL */;\n            case 'table-column-group':\n                return 131072 /* TABLE_COLUMN_GROUP */;\n            case 'table-column':\n                return 262144 /* TABLE_COLUMN */;\n            case 'table-caption':\n                return 524288 /* TABLE_CAPTION */;\n            case 'ruby-base':\n                return 1048576 /* RUBY_BASE */;\n            case 'ruby-text':\n                return 2097152 /* RUBY_TEXT */;\n            case 'ruby-base-container':\n                return 4194304 /* RUBY_BASE_CONTAINER */;\n            case 'ruby-text-container':\n                return 8388608 /* RUBY_TEXT_CONTAINER */;\n            case 'contents':\n                return 16777216 /* CONTENTS */;\n            case 'inline-block':\n                return 33554432 /* INLINE_BLOCK */;\n            case 'inline-list-item':\n                return 67108864 /* INLINE_LIST_ITEM */;\n            case 'inline-table':\n                return 134217728 /* INLINE_TABLE */;\n            case 'inline-flex':\n                return 268435456 /* INLINE_FLEX */;\n            case 'inline-grid':\n                return 536870912 /* INLINE_GRID */;\n        }\n        return 0 /* NONE */;\n    };\n\n    var float = {\n        name: 'float',\n        initialValue: 'none',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, float) {\n            switch (float) {\n                case 'left':\n                    return 1 /* LEFT */;\n                case 'right':\n                    return 2 /* RIGHT */;\n                case 'inline-start':\n                    return 3 /* INLINE_START */;\n                case 'inline-end':\n                    return 4 /* INLINE_END */;\n            }\n            return 0 /* NONE */;\n        }\n    };\n\n    var letterSpacing = {\n        name: 'letter-spacing',\n        initialValue: '0',\n        prefix: false,\n        type: 0 /* VALUE */,\n        parse: function (_context, token) {\n            if (token.type === 20 /* IDENT_TOKEN */ && token.value === 'normal') {\n                return 0;\n            }\n            if (token.type === 17 /* NUMBER_TOKEN */) {\n                return token.number;\n            }\n            if (token.type === 15 /* DIMENSION_TOKEN */) {\n                return token.number;\n            }\n            return 0;\n        }\n    };\n\n    var LINE_BREAK;\n    (function (LINE_BREAK) {\n        LINE_BREAK[\"NORMAL\"] = \"normal\";\n        LINE_BREAK[\"STRICT\"] = \"strict\";\n    })(LINE_BREAK || (LINE_BREAK = {}));\n    var lineBreak = {\n        name: 'line-break',\n        initialValue: 'normal',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, lineBreak) {\n            switch (lineBreak) {\n                case 'strict':\n                    return LINE_BREAK.STRICT;\n                case 'normal':\n                default:\n                    return LINE_BREAK.NORMAL;\n            }\n        }\n    };\n\n    var lineHeight = {\n        name: 'line-height',\n        initialValue: 'normal',\n        prefix: false,\n        type: 4 /* TOKEN_VALUE */\n    };\n    var computeLineHeight = function (token, fontSize) {\n        if (isIdentToken(token) && token.value === 'normal') {\n            return 1.2 * fontSize;\n        }\n        else if (token.type === 17 /* NUMBER_TOKEN */) {\n            return fontSize * token.number;\n        }\n        else if (isLengthPercentage(token)) {\n            return getAbsoluteValue(token, fontSize);\n        }\n        return fontSize;\n    };\n\n    var listStyleImage = {\n        name: 'list-style-image',\n        initialValue: 'none',\n        type: 0 /* VALUE */,\n        prefix: false,\n        parse: function (context, token) {\n            if (token.type === 20 /* IDENT_TOKEN */ && token.value === 'none') {\n                return null;\n            }\n            return image.parse(context, token);\n        }\n    };\n\n    var listStylePosition = {\n        name: 'list-style-position',\n        initialValue: 'outside',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, position) {\n            switch (position) {\n                case 'inside':\n                    return 0 /* INSIDE */;\n                case 'outside':\n                default:\n                    return 1 /* OUTSIDE */;\n            }\n        }\n    };\n\n    var listStyleType = {\n        name: 'list-style-type',\n        initialValue: 'none',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, type) {\n            switch (type) {\n                case 'disc':\n                    return 0 /* DISC */;\n                case 'circle':\n                    return 1 /* CIRCLE */;\n                case 'square':\n                    return 2 /* SQUARE */;\n                case 'decimal':\n                    return 3 /* DECIMAL */;\n                case 'cjk-decimal':\n                    return 4 /* CJK_DECIMAL */;\n                case 'decimal-leading-zero':\n                    return 5 /* DECIMAL_LEADING_ZERO */;\n                case 'lower-roman':\n                    return 6 /* LOWER_ROMAN */;\n                case 'upper-roman':\n                    return 7 /* UPPER_ROMAN */;\n                case 'lower-greek':\n                    return 8 /* LOWER_GREEK */;\n                case 'lower-alpha':\n                    return 9 /* LOWER_ALPHA */;\n                case 'upper-alpha':\n                    return 10 /* UPPER_ALPHA */;\n                case 'arabic-indic':\n                    return 11 /* ARABIC_INDIC */;\n                case 'armenian':\n                    return 12 /* ARMENIAN */;\n                case 'bengali':\n                    return 13 /* BENGALI */;\n                case 'cambodian':\n                    return 14 /* CAMBODIAN */;\n                case 'cjk-earthly-branch':\n                    return 15 /* CJK_EARTHLY_BRANCH */;\n                case 'cjk-heavenly-stem':\n                    return 16 /* CJK_HEAVENLY_STEM */;\n                case 'cjk-ideographic':\n                    return 17 /* CJK_IDEOGRAPHIC */;\n                case 'devanagari':\n                    return 18 /* DEVANAGARI */;\n                case 'ethiopic-numeric':\n                    return 19 /* ETHIOPIC_NUMERIC */;\n                case 'georgian':\n                    return 20 /* GEORGIAN */;\n                case 'gujarati':\n                    return 21 /* GUJARATI */;\n                case 'gurmukhi':\n                    return 22 /* GURMUKHI */;\n                case 'hebrew':\n                    return 22 /* HEBREW */;\n                case 'hiragana':\n                    return 23 /* HIRAGANA */;\n                case 'hiragana-iroha':\n                    return 24 /* HIRAGANA_IROHA */;\n                case 'japanese-formal':\n                    return 25 /* JAPANESE_FORMAL */;\n                case 'japanese-informal':\n                    return 26 /* JAPANESE_INFORMAL */;\n                case 'kannada':\n                    return 27 /* KANNADA */;\n                case 'katakana':\n                    return 28 /* KATAKANA */;\n                case 'katakana-iroha':\n                    return 29 /* KATAKANA_IROHA */;\n                case 'khmer':\n                    return 30 /* KHMER */;\n                case 'korean-hangul-formal':\n                    return 31 /* KOREAN_HANGUL_FORMAL */;\n                case 'korean-hanja-formal':\n                    return 32 /* KOREAN_HANJA_FORMAL */;\n                case 'korean-hanja-informal':\n                    return 33 /* KOREAN_HANJA_INFORMAL */;\n                case 'lao':\n                    return 34 /* LAO */;\n                case 'lower-armenian':\n                    return 35 /* LOWER_ARMENIAN */;\n                case 'malayalam':\n                    return 36 /* MALAYALAM */;\n                case 'mongolian':\n                    return 37 /* MONGOLIAN */;\n                case 'myanmar':\n                    return 38 /* MYANMAR */;\n                case 'oriya':\n                    return 39 /* ORIYA */;\n                case 'persian':\n                    return 40 /* PERSIAN */;\n                case 'simp-chinese-formal':\n                    return 41 /* SIMP_CHINESE_FORMAL */;\n                case 'simp-chinese-informal':\n                    return 42 /* SIMP_CHINESE_INFORMAL */;\n                case 'tamil':\n                    return 43 /* TAMIL */;\n                case 'telugu':\n                    return 44 /* TELUGU */;\n                case 'thai':\n                    return 45 /* THAI */;\n                case 'tibetan':\n                    return 46 /* TIBETAN */;\n                case 'trad-chinese-formal':\n                    return 47 /* TRAD_CHINESE_FORMAL */;\n                case 'trad-chinese-informal':\n                    return 48 /* TRAD_CHINESE_INFORMAL */;\n                case 'upper-armenian':\n                    return 49 /* UPPER_ARMENIAN */;\n                case 'disclosure-open':\n                    return 50 /* DISCLOSURE_OPEN */;\n                case 'disclosure-closed':\n                    return 51 /* DISCLOSURE_CLOSED */;\n                case 'none':\n                default:\n                    return -1 /* NONE */;\n            }\n        }\n    };\n\n    var marginForSide = function (side) { return ({\n        name: \"margin-\" + side,\n        initialValue: '0',\n        prefix: false,\n        type: 4 /* TOKEN_VALUE */\n    }); };\n    var marginTop = marginForSide('top');\n    var marginRight = marginForSide('right');\n    var marginBottom = marginForSide('bottom');\n    var marginLeft = marginForSide('left');\n\n    var overflow = {\n        name: 'overflow',\n        initialValue: 'visible',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return tokens.filter(isIdentToken).map(function (overflow) {\n                switch (overflow.value) {\n                    case 'hidden':\n                        return 1 /* HIDDEN */;\n                    case 'scroll':\n                        return 2 /* SCROLL */;\n                    case 'clip':\n                        return 3 /* CLIP */;\n                    case 'auto':\n                        return 4 /* AUTO */;\n                    case 'visible':\n                    default:\n                        return 0 /* VISIBLE */;\n                }\n            });\n        }\n    };\n\n    var overflowWrap = {\n        name: 'overflow-wrap',\n        initialValue: 'normal',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, overflow) {\n            switch (overflow) {\n                case 'break-word':\n                    return \"break-word\" /* BREAK_WORD */;\n                case 'normal':\n                default:\n                    return \"normal\" /* NORMAL */;\n            }\n        }\n    };\n\n    var paddingForSide = function (side) { return ({\n        name: \"padding-\" + side,\n        initialValue: '0',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'length-percentage'\n    }); };\n    var paddingTop = paddingForSide('top');\n    var paddingRight = paddingForSide('right');\n    var paddingBottom = paddingForSide('bottom');\n    var paddingLeft = paddingForSide('left');\n\n    var textAlign = {\n        name: 'text-align',\n        initialValue: 'left',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, textAlign) {\n            switch (textAlign) {\n                case 'right':\n                    return 2 /* RIGHT */;\n                case 'center':\n                case 'justify':\n                    return 1 /* CENTER */;\n                case 'left':\n                default:\n                    return 0 /* LEFT */;\n            }\n        }\n    };\n\n    var position = {\n        name: 'position',\n        initialValue: 'static',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, position) {\n            switch (position) {\n                case 'relative':\n                    return 1 /* RELATIVE */;\n                case 'absolute':\n                    return 2 /* ABSOLUTE */;\n                case 'fixed':\n                    return 3 /* FIXED */;\n                case 'sticky':\n                    return 4 /* STICKY */;\n            }\n            return 0 /* STATIC */;\n        }\n    };\n\n    var textShadow = {\n        name: 'text-shadow',\n        initialValue: 'none',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (context, tokens) {\n            if (tokens.length === 1 && isIdentWithValue(tokens[0], 'none')) {\n                return [];\n            }\n            return parseFunctionArgs(tokens).map(function (values) {\n                var shadow = {\n                    color: COLORS.TRANSPARENT,\n                    offsetX: ZERO_LENGTH,\n                    offsetY: ZERO_LENGTH,\n                    blur: ZERO_LENGTH\n                };\n                var c = 0;\n                for (var i = 0; i < values.length; i++) {\n                    var token = values[i];\n                    if (isLength(token)) {\n                        if (c === 0) {\n                            shadow.offsetX = token;\n                        }\n                        else if (c === 1) {\n                            shadow.offsetY = token;\n                        }\n                        else {\n                            shadow.blur = token;\n                        }\n                        c++;\n                    }\n                    else {\n                        shadow.color = color$1.parse(context, token);\n                    }\n                }\n                return shadow;\n            });\n        }\n    };\n\n    var textTransform = {\n        name: 'text-transform',\n        initialValue: 'none',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, textTransform) {\n            switch (textTransform) {\n                case 'uppercase':\n                    return 2 /* UPPERCASE */;\n                case 'lowercase':\n                    return 1 /* LOWERCASE */;\n                case 'capitalize':\n                    return 3 /* CAPITALIZE */;\n            }\n            return 0 /* NONE */;\n        }\n    };\n\n    var transform$1 = {\n        name: 'transform',\n        initialValue: 'none',\n        prefix: true,\n        type: 0 /* VALUE */,\n        parse: function (_context, token) {\n            if (token.type === 20 /* IDENT_TOKEN */ && token.value === 'none') {\n                return null;\n            }\n            if (token.type === 18 /* FUNCTION */) {\n                var transformFunction = SUPPORTED_TRANSFORM_FUNCTIONS[token.name];\n                if (typeof transformFunction === 'undefined') {\n                    throw new Error(\"Attempting to parse an unsupported transform function \\\"\" + token.name + \"\\\"\");\n                }\n                return transformFunction(token.values);\n            }\n            return null;\n        }\n    };\n    var matrix = function (args) {\n        var values = args.filter(function (arg) { return arg.type === 17 /* NUMBER_TOKEN */; }).map(function (arg) { return arg.number; });\n        return values.length === 6 ? values : null;\n    };\n    // doesn't support 3D transforms at the moment\n    var matrix3d = function (args) {\n        var values = args.filter(function (arg) { return arg.type === 17 /* NUMBER_TOKEN */; }).map(function (arg) { return arg.number; });\n        var a1 = values[0], b1 = values[1]; values[2]; values[3]; var a2 = values[4], b2 = values[5]; values[6]; values[7]; values[8]; values[9]; values[10]; values[11]; var a4 = values[12], b4 = values[13]; values[14]; values[15];\n        return values.length === 16 ? [a1, b1, a2, b2, a4, b4] : null;\n    };\n    var SUPPORTED_TRANSFORM_FUNCTIONS = {\n        matrix: matrix,\n        matrix3d: matrix3d\n    };\n\n    var DEFAULT_VALUE = {\n        type: 16 /* PERCENTAGE_TOKEN */,\n        number: 50,\n        flags: FLAG_INTEGER\n    };\n    var DEFAULT = [DEFAULT_VALUE, DEFAULT_VALUE];\n    var transformOrigin = {\n        name: 'transform-origin',\n        initialValue: '50% 50%',\n        prefix: true,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            var origins = tokens.filter(isLengthPercentage);\n            if (origins.length !== 2) {\n                return DEFAULT;\n            }\n            return [origins[0], origins[1]];\n        }\n    };\n\n    var visibility = {\n        name: 'visible',\n        initialValue: 'none',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, visibility) {\n            switch (visibility) {\n                case 'hidden':\n                    return 1 /* HIDDEN */;\n                case 'collapse':\n                    return 2 /* COLLAPSE */;\n                case 'visible':\n                default:\n                    return 0 /* VISIBLE */;\n            }\n        }\n    };\n\n    var WORD_BREAK;\n    (function (WORD_BREAK) {\n        WORD_BREAK[\"NORMAL\"] = \"normal\";\n        WORD_BREAK[\"BREAK_ALL\"] = \"break-all\";\n        WORD_BREAK[\"KEEP_ALL\"] = \"keep-all\";\n    })(WORD_BREAK || (WORD_BREAK = {}));\n    var wordBreak = {\n        name: 'word-break',\n        initialValue: 'normal',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, wordBreak) {\n            switch (wordBreak) {\n                case 'break-all':\n                    return WORD_BREAK.BREAK_ALL;\n                case 'keep-all':\n                    return WORD_BREAK.KEEP_ALL;\n                case 'normal':\n                default:\n                    return WORD_BREAK.NORMAL;\n            }\n        }\n    };\n\n    var zIndex = {\n        name: 'z-index',\n        initialValue: 'auto',\n        prefix: false,\n        type: 0 /* VALUE */,\n        parse: function (_context, token) {\n            if (token.type === 20 /* IDENT_TOKEN */) {\n                return { auto: true, order: 0 };\n            }\n            if (isNumberToken(token)) {\n                return { auto: false, order: token.number };\n            }\n            throw new Error(\"Invalid z-index number parsed\");\n        }\n    };\n\n    var time = {\n        name: 'time',\n        parse: function (_context, value) {\n            if (value.type === 15 /* DIMENSION_TOKEN */) {\n                switch (value.unit.toLowerCase()) {\n                    case 's':\n                        return 1000 * value.number;\n                    case 'ms':\n                        return value.number;\n                }\n            }\n            throw new Error(\"Unsupported time type\");\n        }\n    };\n\n    var opacity = {\n        name: 'opacity',\n        initialValue: '1',\n        type: 0 /* VALUE */,\n        prefix: false,\n        parse: function (_context, token) {\n            if (isNumberToken(token)) {\n                return token.number;\n            }\n            return 1;\n        }\n    };\n\n    var textDecorationColor = {\n        name: \"text-decoration-color\",\n        initialValue: 'transparent',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'color'\n    };\n\n    var textDecorationLine = {\n        name: 'text-decoration-line',\n        initialValue: 'none',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            return tokens\n                .filter(isIdentToken)\n                .map(function (token) {\n                switch (token.value) {\n                    case 'underline':\n                        return 1 /* UNDERLINE */;\n                    case 'overline':\n                        return 2 /* OVERLINE */;\n                    case 'line-through':\n                        return 3 /* LINE_THROUGH */;\n                    case 'none':\n                        return 4 /* BLINK */;\n                }\n                return 0 /* NONE */;\n            })\n                .filter(function (line) { return line !== 0 /* NONE */; });\n        }\n    };\n\n    var fontFamily = {\n        name: \"font-family\",\n        initialValue: '',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            var accumulator = [];\n            var results = [];\n            tokens.forEach(function (token) {\n                switch (token.type) {\n                    case 20 /* IDENT_TOKEN */:\n                    case 0 /* STRING_TOKEN */:\n                        accumulator.push(token.value);\n                        break;\n                    case 17 /* NUMBER_TOKEN */:\n                        accumulator.push(token.number.toString());\n                        break;\n                    case 4 /* COMMA_TOKEN */:\n                        results.push(accumulator.join(' '));\n                        accumulator.length = 0;\n                        break;\n                }\n            });\n            if (accumulator.length) {\n                results.push(accumulator.join(' '));\n            }\n            return results.map(function (result) { return (result.indexOf(' ') === -1 ? result : \"'\" + result + \"'\"); });\n        }\n    };\n\n    var fontSize = {\n        name: \"font-size\",\n        initialValue: '0',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'length'\n    };\n\n    var fontWeight = {\n        name: 'font-weight',\n        initialValue: 'normal',\n        type: 0 /* VALUE */,\n        prefix: false,\n        parse: function (_context, token) {\n            if (isNumberToken(token)) {\n                return token.number;\n            }\n            if (isIdentToken(token)) {\n                switch (token.value) {\n                    case 'bold':\n                        return 700;\n                    case 'normal':\n                    default:\n                        return 400;\n                }\n            }\n            return 400;\n        }\n    };\n\n    var fontVariant = {\n        name: 'font-variant',\n        initialValue: 'none',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (_context, tokens) {\n            return tokens.filter(isIdentToken).map(function (token) { return token.value; });\n        }\n    };\n\n    var fontStyle = {\n        name: 'font-style',\n        initialValue: 'normal',\n        prefix: false,\n        type: 2 /* IDENT_VALUE */,\n        parse: function (_context, overflow) {\n            switch (overflow) {\n                case 'oblique':\n                    return \"oblique\" /* OBLIQUE */;\n                case 'italic':\n                    return \"italic\" /* ITALIC */;\n                case 'normal':\n                default:\n                    return \"normal\" /* NORMAL */;\n            }\n        }\n    };\n\n    var contains = function (bit, value) { return (bit & value) !== 0; };\n\n    var content = {\n        name: 'content',\n        initialValue: 'none',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (_context, tokens) {\n            if (tokens.length === 0) {\n                return [];\n            }\n            var first = tokens[0];\n            if (first.type === 20 /* IDENT_TOKEN */ && first.value === 'none') {\n                return [];\n            }\n            return tokens;\n        }\n    };\n\n    var counterIncrement = {\n        name: 'counter-increment',\n        initialValue: 'none',\n        prefix: true,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            if (tokens.length === 0) {\n                return null;\n            }\n            var first = tokens[0];\n            if (first.type === 20 /* IDENT_TOKEN */ && first.value === 'none') {\n                return null;\n            }\n            var increments = [];\n            var filtered = tokens.filter(nonWhiteSpace);\n            for (var i = 0; i < filtered.length; i++) {\n                var counter = filtered[i];\n                var next = filtered[i + 1];\n                if (counter.type === 20 /* IDENT_TOKEN */) {\n                    var increment = next && isNumberToken(next) ? next.number : 1;\n                    increments.push({ counter: counter.value, increment: increment });\n                }\n            }\n            return increments;\n        }\n    };\n\n    var counterReset = {\n        name: 'counter-reset',\n        initialValue: 'none',\n        prefix: true,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            if (tokens.length === 0) {\n                return [];\n            }\n            var resets = [];\n            var filtered = tokens.filter(nonWhiteSpace);\n            for (var i = 0; i < filtered.length; i++) {\n                var counter = filtered[i];\n                var next = filtered[i + 1];\n                if (isIdentToken(counter) && counter.value !== 'none') {\n                    var reset = next && isNumberToken(next) ? next.number : 0;\n                    resets.push({ counter: counter.value, reset: reset });\n                }\n            }\n            return resets;\n        }\n    };\n\n    var duration = {\n        name: 'duration',\n        initialValue: '0s',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (context, tokens) {\n            return tokens.filter(isDimensionToken).map(function (token) { return time.parse(context, token); });\n        }\n    };\n\n    var quotes = {\n        name: 'quotes',\n        initialValue: 'none',\n        prefix: true,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            if (tokens.length === 0) {\n                return null;\n            }\n            var first = tokens[0];\n            if (first.type === 20 /* IDENT_TOKEN */ && first.value === 'none') {\n                return null;\n            }\n            var quotes = [];\n            var filtered = tokens.filter(isStringToken);\n            if (filtered.length % 2 !== 0) {\n                return null;\n            }\n            for (var i = 0; i < filtered.length; i += 2) {\n                var open_1 = filtered[i].value;\n                var close_1 = filtered[i + 1].value;\n                quotes.push({ open: open_1, close: close_1 });\n            }\n            return quotes;\n        }\n    };\n    var getQuote = function (quotes, depth, open) {\n        if (!quotes) {\n            return '';\n        }\n        var quote = quotes[Math.min(depth, quotes.length - 1)];\n        if (!quote) {\n            return '';\n        }\n        return open ? quote.open : quote.close;\n    };\n\n    var boxShadow = {\n        name: 'box-shadow',\n        initialValue: 'none',\n        type: 1 /* LIST */,\n        prefix: false,\n        parse: function (context, tokens) {\n            if (tokens.length === 1 && isIdentWithValue(tokens[0], 'none')) {\n                return [];\n            }\n            return parseFunctionArgs(tokens).map(function (values) {\n                var shadow = {\n                    color: 0x000000ff,\n                    offsetX: ZERO_LENGTH,\n                    offsetY: ZERO_LENGTH,\n                    blur: ZERO_LENGTH,\n                    spread: ZERO_LENGTH,\n                    inset: false\n                };\n                var c = 0;\n                for (var i = 0; i < values.length; i++) {\n                    var token = values[i];\n                    if (isIdentWithValue(token, 'inset')) {\n                        shadow.inset = true;\n                    }\n                    else if (isLength(token)) {\n                        if (c === 0) {\n                            shadow.offsetX = token;\n                        }\n                        else if (c === 1) {\n                            shadow.offsetY = token;\n                        }\n                        else if (c === 2) {\n                            shadow.blur = token;\n                        }\n                        else {\n                            shadow.spread = token;\n                        }\n                        c++;\n                    }\n                    else {\n                        shadow.color = color$1.parse(context, token);\n                    }\n                }\n                return shadow;\n            });\n        }\n    };\n\n    var paintOrder = {\n        name: 'paint-order',\n        initialValue: 'normal',\n        prefix: false,\n        type: 1 /* LIST */,\n        parse: function (_context, tokens) {\n            var DEFAULT_VALUE = [0 /* FILL */, 1 /* STROKE */, 2 /* MARKERS */];\n            var layers = [];\n            tokens.filter(isIdentToken).forEach(function (token) {\n                switch (token.value) {\n                    case 'stroke':\n                        layers.push(1 /* STROKE */);\n                        break;\n                    case 'fill':\n                        layers.push(0 /* FILL */);\n                        break;\n                    case 'markers':\n                        layers.push(2 /* MARKERS */);\n                        break;\n                }\n            });\n            DEFAULT_VALUE.forEach(function (value) {\n                if (layers.indexOf(value) === -1) {\n                    layers.push(value);\n                }\n            });\n            return layers;\n        }\n    };\n\n    var webkitTextStrokeColor = {\n        name: \"-webkit-text-stroke-color\",\n        initialValue: 'currentcolor',\n        prefix: false,\n        type: 3 /* TYPE_VALUE */,\n        format: 'color'\n    };\n\n    var webkitTextStrokeWidth = {\n        name: \"-webkit-text-stroke-width\",\n        initialValue: '0',\n        type: 0 /* VALUE */,\n        prefix: false,\n        parse: function (_context, token) {\n            if (isDimensionToken(token)) {\n                return token.number;\n            }\n            return 0;\n        }\n    };\n\n    var CSSParsedDeclaration = /** @class */ (function () {\n        function CSSParsedDeclaration(context, declaration) {\n            var _a, _b;\n            this.animationDuration = parse(context, duration, declaration.animationDuration);\n            this.backgroundClip = parse(context, backgroundClip, declaration.backgroundClip);\n            this.backgroundColor = parse(context, backgroundColor, declaration.backgroundColor);\n            this.backgroundImage = parse(context, backgroundImage, declaration.backgroundImage);\n            this.backgroundOrigin = parse(context, backgroundOrigin, declaration.backgroundOrigin);\n            this.backgroundPosition = parse(context, backgroundPosition, declaration.backgroundPosition);\n            this.backgroundRepeat = parse(context, backgroundRepeat, declaration.backgroundRepeat);\n            this.backgroundSize = parse(context, backgroundSize, declaration.backgroundSize);\n            this.borderTopColor = parse(context, borderTopColor, declaration.borderTopColor);\n            this.borderRightColor = parse(context, borderRightColor, declaration.borderRightColor);\n            this.borderBottomColor = parse(context, borderBottomColor, declaration.borderBottomColor);\n            this.borderLeftColor = parse(context, borderLeftColor, declaration.borderLeftColor);\n            this.borderTopLeftRadius = parse(context, borderTopLeftRadius, declaration.borderTopLeftRadius);\n            this.borderTopRightRadius = parse(context, borderTopRightRadius, declaration.borderTopRightRadius);\n            this.borderBottomRightRadius = parse(context, borderBottomRightRadius, declaration.borderBottomRightRadius);\n            this.borderBottomLeftRadius = parse(context, borderBottomLeftRadius, declaration.borderBottomLeftRadius);\n            this.borderTopStyle = parse(context, borderTopStyle, declaration.borderTopStyle);\n            this.borderRightStyle = parse(context, borderRightStyle, declaration.borderRightStyle);\n            this.borderBottomStyle = parse(context, borderBottomStyle, declaration.borderBottomStyle);\n            this.borderLeftStyle = parse(context, borderLeftStyle, declaration.borderLeftStyle);\n            this.borderTopWidth = parse(context, borderTopWidth, declaration.borderTopWidth);\n            this.borderRightWidth = parse(context, borderRightWidth, declaration.borderRightWidth);\n            this.borderBottomWidth = parse(context, borderBottomWidth, declaration.borderBottomWidth);\n            this.borderLeftWidth = parse(context, borderLeftWidth, declaration.borderLeftWidth);\n            this.boxShadow = parse(context, boxShadow, declaration.boxShadow);\n            this.color = parse(context, color, declaration.color);\n            this.direction = parse(context, direction, declaration.direction);\n            this.display = parse(context, display, declaration.display);\n            this.float = parse(context, float, declaration.cssFloat);\n            this.fontFamily = parse(context, fontFamily, declaration.fontFamily);\n            this.fontSize = parse(context, fontSize, declaration.fontSize);\n            this.fontStyle = parse(context, fontStyle, declaration.fontStyle);\n            this.fontVariant = parse(context, fontVariant, declaration.fontVariant);\n            this.fontWeight = parse(context, fontWeight, declaration.fontWeight);\n            this.letterSpacing = parse(context, letterSpacing, declaration.letterSpacing);\n            this.lineBreak = parse(context, lineBreak, declaration.lineBreak);\n            this.lineHeight = parse(context, lineHeight, declaration.lineHeight);\n            this.listStyleImage = parse(context, listStyleImage, declaration.listStyleImage);\n            this.listStylePosition = parse(context, listStylePosition, declaration.listStylePosition);\n            this.listStyleType = parse(context, listStyleType, declaration.listStyleType);\n            this.marginTop = parse(context, marginTop, declaration.marginTop);\n            this.marginRight = parse(context, marginRight, declaration.marginRight);\n            this.marginBottom = parse(context, marginBottom, declaration.marginBottom);\n            this.marginLeft = parse(context, marginLeft, declaration.marginLeft);\n            this.opacity = parse(context, opacity, declaration.opacity);\n            var overflowTuple = parse(context, overflow, declaration.overflow);\n            this.overflowX = overflowTuple[0];\n            this.overflowY = overflowTuple[overflowTuple.length > 1 ? 1 : 0];\n            this.overflowWrap = parse(context, overflowWrap, declaration.overflowWrap);\n            this.paddingTop = parse(context, paddingTop, declaration.paddingTop);\n            this.paddingRight = parse(context, paddingRight, declaration.paddingRight);\n            this.paddingBottom = parse(context, paddingBottom, declaration.paddingBottom);\n            this.paddingLeft = parse(context, paddingLeft, declaration.paddingLeft);\n            this.paintOrder = parse(context, paintOrder, declaration.paintOrder);\n            this.position = parse(context, position, declaration.position);\n            this.textAlign = parse(context, textAlign, declaration.textAlign);\n            this.textDecorationColor = parse(context, textDecorationColor, (_a = declaration.textDecorationColor) !== null && _a !== void 0 ? _a : declaration.color);\n            this.textDecorationLine = parse(context, textDecorationLine, (_b = declaration.textDecorationLine) !== null && _b !== void 0 ? _b : declaration.textDecoration);\n            this.textShadow = parse(context, textShadow, declaration.textShadow);\n            this.textTransform = parse(context, textTransform, declaration.textTransform);\n            this.transform = parse(context, transform$1, declaration.transform);\n            this.transformOrigin = parse(context, transformOrigin, declaration.transformOrigin);\n            this.visibility = parse(context, visibility, declaration.visibility);\n            this.webkitTextStrokeColor = parse(context, webkitTextStrokeColor, declaration.webkitTextStrokeColor);\n            this.webkitTextStrokeWidth = parse(context, webkitTextStrokeWidth, declaration.webkitTextStrokeWidth);\n            this.wordBreak = parse(context, wordBreak, declaration.wordBreak);\n            this.zIndex = parse(context, zIndex, declaration.zIndex);\n        }\n        CSSParsedDeclaration.prototype.isVisible = function () {\n            return this.display > 0 && this.opacity > 0 && this.visibility === 0 /* VISIBLE */;\n        };\n        CSSParsedDeclaration.prototype.isTransparent = function () {\n            return isTransparent(this.backgroundColor);\n        };\n        CSSParsedDeclaration.prototype.isTransformed = function () {\n            return this.transform !== null;\n        };\n        CSSParsedDeclaration.prototype.isPositioned = function () {\n            return this.position !== 0 /* STATIC */;\n        };\n        CSSParsedDeclaration.prototype.isPositionedWithZIndex = function () {\n            return this.isPositioned() && !this.zIndex.auto;\n        };\n        CSSParsedDeclaration.prototype.isFloating = function () {\n            return this.float !== 0 /* NONE */;\n        };\n        CSSParsedDeclaration.prototype.isInlineLevel = function () {\n            return (contains(this.display, 4 /* INLINE */) ||\n                contains(this.display, 33554432 /* INLINE_BLOCK */) ||\n                contains(this.display, 268435456 /* INLINE_FLEX */) ||\n                contains(this.display, 536870912 /* INLINE_GRID */) ||\n                contains(this.display, 67108864 /* INLINE_LIST_ITEM */) ||\n                contains(this.display, 134217728 /* INLINE_TABLE */));\n        };\n        return CSSParsedDeclaration;\n    }());\n    var CSSParsedPseudoDeclaration = /** @class */ (function () {\n        function CSSParsedPseudoDeclaration(context, declaration) {\n            this.content = parse(context, content, declaration.content);\n            this.quotes = parse(context, quotes, declaration.quotes);\n        }\n        return CSSParsedPseudoDeclaration;\n    }());\n    var CSSParsedCounterDeclaration = /** @class */ (function () {\n        function CSSParsedCounterDeclaration(context, declaration) {\n            this.counterIncrement = parse(context, counterIncrement, declaration.counterIncrement);\n            this.counterReset = parse(context, counterReset, declaration.counterReset);\n        }\n        return CSSParsedCounterDeclaration;\n    }());\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var parse = function (context, descriptor, style) {\n        var tokenizer = new Tokenizer();\n        var value = style !== null && typeof style !== 'undefined' ? style.toString() : descriptor.initialValue;\n        tokenizer.write(value);\n        var parser = new Parser(tokenizer.read());\n        switch (descriptor.type) {\n            case 2 /* IDENT_VALUE */:\n                var token = parser.parseComponentValue();\n                return descriptor.parse(context, isIdentToken(token) ? token.value : descriptor.initialValue);\n            case 0 /* VALUE */:\n                return descriptor.parse(context, parser.parseComponentValue());\n            case 1 /* LIST */:\n                return descriptor.parse(context, parser.parseComponentValues());\n            case 4 /* TOKEN_VALUE */:\n                return parser.parseComponentValue();\n            case 3 /* TYPE_VALUE */:\n                switch (descriptor.format) {\n                    case 'angle':\n                        return angle.parse(context, parser.parseComponentValue());\n                    case 'color':\n                        return color$1.parse(context, parser.parseComponentValue());\n                    case 'image':\n                        return image.parse(context, parser.parseComponentValue());\n                    case 'length':\n                        var length_1 = parser.parseComponentValue();\n                        return isLength(length_1) ? length_1 : ZERO_LENGTH;\n                    case 'length-percentage':\n                        var value_1 = parser.parseComponentValue();\n                        return isLengthPercentage(value_1) ? value_1 : ZERO_LENGTH;\n                    case 'time':\n                        return time.parse(context, parser.parseComponentValue());\n                }\n                break;\n        }\n    };\n\n    var elementDebuggerAttribute = 'data-html2canvas-debug';\n    var getElementDebugType = function (element) {\n        var attribute = element.getAttribute(elementDebuggerAttribute);\n        switch (attribute) {\n            case 'all':\n                return 1 /* ALL */;\n            case 'clone':\n                return 2 /* CLONE */;\n            case 'parse':\n                return 3 /* PARSE */;\n            case 'render':\n                return 4 /* RENDER */;\n            default:\n                return 0 /* NONE */;\n        }\n    };\n    var isDebugging = function (element, type) {\n        var elementType = getElementDebugType(element);\n        return elementType === 1 /* ALL */ || type === elementType;\n    };\n\n    var ElementContainer = /** @class */ (function () {\n        function ElementContainer(context, element) {\n            this.context = context;\n            this.textNodes = [];\n            this.elements = [];\n            this.flags = 0;\n            if (isDebugging(element, 3 /* PARSE */)) {\n                debugger;\n            }\n            this.styles = new CSSParsedDeclaration(context, window.getComputedStyle(element, null));\n            if (isHTMLElementNode(element)) {\n                if (this.styles.animationDuration.some(function (duration) { return duration > 0; })) {\n                    element.style.animationDuration = '0s';\n                }\n                if (this.styles.transform !== null) {\n                    // getBoundingClientRect takes transforms into account\n                    element.style.transform = 'none';\n                }\n            }\n            this.bounds = parseBounds(this.context, element);\n            if (isDebugging(element, 4 /* RENDER */)) {\n                this.flags |= 16 /* DEBUG_RENDER */;\n            }\n        }\n        return ElementContainer;\n    }());\n\n    /*\n     * text-segmentation 1.0.3 <https://github.com/niklasvh/text-segmentation>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var base64 = '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';\n\n    /*\n     * utrie 1.0.2 <https://github.com/niklasvh/utrie>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var chars$1 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    // Use a lookup table to find the index.\n    var lookup$1 = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n    for (var i$1 = 0; i$1 < chars$1.length; i$1++) {\n        lookup$1[chars$1.charCodeAt(i$1)] = i$1;\n    }\n    var decode = function (base64) {\n        var bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n        if (base64[base64.length - 1] === '=') {\n            bufferLength--;\n            if (base64[base64.length - 2] === '=') {\n                bufferLength--;\n            }\n        }\n        var buffer = typeof ArrayBuffer !== 'undefined' &&\n            typeof Uint8Array !== 'undefined' &&\n            typeof Uint8Array.prototype.slice !== 'undefined'\n            ? new ArrayBuffer(bufferLength)\n            : new Array(bufferLength);\n        var bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n        for (i = 0; i < len; i += 4) {\n            encoded1 = lookup$1[base64.charCodeAt(i)];\n            encoded2 = lookup$1[base64.charCodeAt(i + 1)];\n            encoded3 = lookup$1[base64.charCodeAt(i + 2)];\n            encoded4 = lookup$1[base64.charCodeAt(i + 3)];\n            bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n            bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n            bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n        }\n        return buffer;\n    };\n    var polyUint16Array = function (buffer) {\n        var length = buffer.length;\n        var bytes = [];\n        for (var i = 0; i < length; i += 2) {\n            bytes.push((buffer[i + 1] << 8) | buffer[i]);\n        }\n        return bytes;\n    };\n    var polyUint32Array = function (buffer) {\n        var length = buffer.length;\n        var bytes = [];\n        for (var i = 0; i < length; i += 4) {\n            bytes.push((buffer[i + 3] << 24) | (buffer[i + 2] << 16) | (buffer[i + 1] << 8) | buffer[i]);\n        }\n        return bytes;\n    };\n\n    /** Shift size for getting the index-2 table offset. */\n    var UTRIE2_SHIFT_2 = 5;\n    /** Shift size for getting the index-1 table offset. */\n    var UTRIE2_SHIFT_1 = 6 + 5;\n    /**\n     * Shift size for shifting left the index array values.\n     * Increases possible data size with 16-bit index values at the cost\n     * of compactability.\n     * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n     */\n    var UTRIE2_INDEX_SHIFT = 2;\n    /**\n     * Difference between the two shift sizes,\n     * for getting an index-1 offset from an index-2 offset. 6=11-5\n     */\n    var UTRIE2_SHIFT_1_2 = UTRIE2_SHIFT_1 - UTRIE2_SHIFT_2;\n    /**\n     * The part of the index-2 table for U+D800..U+DBFF stores values for\n     * lead surrogate code _units_ not code _points_.\n     * Values for lead surrogate code _points_ are indexed with this portion of the table.\n     * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n     */\n    var UTRIE2_LSCP_INDEX_2_OFFSET = 0x10000 >> UTRIE2_SHIFT_2;\n    /** Number of entries in a data block. 32=0x20 */\n    var UTRIE2_DATA_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_2;\n    /** Mask for getting the lower bits for the in-data-block offset. */\n    var UTRIE2_DATA_MASK = UTRIE2_DATA_BLOCK_LENGTH - 1;\n    var UTRIE2_LSCP_INDEX_2_LENGTH = 0x400 >> UTRIE2_SHIFT_2;\n    /** Count the lengths of both BMP pieces. 2080=0x820 */\n    var UTRIE2_INDEX_2_BMP_LENGTH = UTRIE2_LSCP_INDEX_2_OFFSET + UTRIE2_LSCP_INDEX_2_LENGTH;\n    /**\n     * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n     * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n     */\n    var UTRIE2_UTF8_2B_INDEX_2_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\n    var UTRIE2_UTF8_2B_INDEX_2_LENGTH = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n    /**\n     * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n     * Variable length, for code points up to highStart, where the last single-value range starts.\n     * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n     * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n     *\n     * The part of the index-2 table for supplementary code points starts\n     * after this index-1 table.\n     *\n     * Both the index-1 table and the following part of the index-2 table\n     * are omitted completely if there is only BMP data.\n     */\n    var UTRIE2_INDEX_1_OFFSET = UTRIE2_UTF8_2B_INDEX_2_OFFSET + UTRIE2_UTF8_2B_INDEX_2_LENGTH;\n    /**\n     * Number of index-1 entries for the BMP. 32=0x20\n     * This part of the index-1 table is omitted from the serialized form.\n     */\n    var UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = 0x10000 >> UTRIE2_SHIFT_1;\n    /** Number of entries in an index-2 block. 64=0x40 */\n    var UTRIE2_INDEX_2_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_1_2;\n    /** Mask for getting the lower bits for the in-index-2-block offset. */\n    var UTRIE2_INDEX_2_MASK = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n    var slice16 = function (view, start, end) {\n        if (view.slice) {\n            return view.slice(start, end);\n        }\n        return new Uint16Array(Array.prototype.slice.call(view, start, end));\n    };\n    var slice32 = function (view, start, end) {\n        if (view.slice) {\n            return view.slice(start, end);\n        }\n        return new Uint32Array(Array.prototype.slice.call(view, start, end));\n    };\n    var createTrieFromBase64 = function (base64, _byteLength) {\n        var buffer = decode(base64);\n        var view32 = Array.isArray(buffer) ? polyUint32Array(buffer) : new Uint32Array(buffer);\n        var view16 = Array.isArray(buffer) ? polyUint16Array(buffer) : new Uint16Array(buffer);\n        var headerLength = 24;\n        var index = slice16(view16, headerLength / 2, view32[4] / 2);\n        var data = view32[5] === 2\n            ? slice16(view16, (headerLength + view32[4]) / 2)\n            : slice32(view32, Math.ceil((headerLength + view32[4]) / 4));\n        return new Trie(view32[0], view32[1], view32[2], view32[3], index, data);\n    };\n    var Trie = /** @class */ (function () {\n        function Trie(initialValue, errorValue, highStart, highValueIndex, index, data) {\n            this.initialValue = initialValue;\n            this.errorValue = errorValue;\n            this.highStart = highStart;\n            this.highValueIndex = highValueIndex;\n            this.index = index;\n            this.data = data;\n        }\n        /**\n         * Get the value for a code point as stored in the Trie.\n         *\n         * @param codePoint the code point\n         * @return the value\n         */\n        Trie.prototype.get = function (codePoint) {\n            var ix;\n            if (codePoint >= 0) {\n                if (codePoint < 0x0d800 || (codePoint > 0x0dbff && codePoint <= 0x0ffff)) {\n                    // Ordinary BMP code point, excluding leading surrogates.\n                    // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                    // 16 bit data is stored in the index array itself.\n                    ix = this.index[codePoint >> UTRIE2_SHIFT_2];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0xffff) {\n                    // Lead Surrogate Code Point.  A Separate index section is stored for\n                    // lead surrogate code units and code points.\n                    //   The main index has the code unit data.\n                    //   For this function, we need the code point data.\n                    // Note: this expression could be refactored for slightly improved efficiency, but\n                    //       surrogate code points will be so rare in practice that it's not worth it.\n                    ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET + ((codePoint - 0xd800) >> UTRIE2_SHIFT_2)];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint < this.highStart) {\n                    // Supplemental code point, use two-level lookup.\n                    ix = UTRIE2_INDEX_1_OFFSET - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH + (codePoint >> UTRIE2_SHIFT_1);\n                    ix = this.index[ix];\n                    ix += (codePoint >> UTRIE2_SHIFT_2) & UTRIE2_INDEX_2_MASK;\n                    ix = this.index[ix];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0x10ffff) {\n                    return this.data[this.highValueIndex];\n                }\n            }\n            // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n            return this.errorValue;\n        };\n        return Trie;\n    }());\n\n    /*\n     * base64-arraybuffer 1.0.2 <https://github.com/niklasvh/base64-arraybuffer>\n     * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>\n     * Released under MIT License\n     */\n    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    // Use a lookup table to find the index.\n    var lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n    for (var i = 0; i < chars.length; i++) {\n        lookup[chars.charCodeAt(i)] = i;\n    }\n\n    var Prepend = 1;\n    var CR = 2;\n    var LF = 3;\n    var Control = 4;\n    var Extend = 5;\n    var SpacingMark = 7;\n    var L = 8;\n    var V = 9;\n    var T = 10;\n    var LV = 11;\n    var LVT = 12;\n    var ZWJ = 13;\n    var Extended_Pictographic = 14;\n    var RI = 15;\n    var toCodePoints = function (str) {\n        var codePoints = [];\n        var i = 0;\n        var length = str.length;\n        while (i < length) {\n            var value = str.charCodeAt(i++);\n            if (value >= 0xd800 && value <= 0xdbff && i < length) {\n                var extra = str.charCodeAt(i++);\n                if ((extra & 0xfc00) === 0xdc00) {\n                    codePoints.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n                }\n                else {\n                    codePoints.push(value);\n                    i--;\n                }\n            }\n            else {\n                codePoints.push(value);\n            }\n        }\n        return codePoints;\n    };\n    var fromCodePoint = function () {\n        var codePoints = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            codePoints[_i] = arguments[_i];\n        }\n        if (String.fromCodePoint) {\n            return String.fromCodePoint.apply(String, codePoints);\n        }\n        var length = codePoints.length;\n        if (!length) {\n            return '';\n        }\n        var codeUnits = [];\n        var index = -1;\n        var result = '';\n        while (++index < length) {\n            var codePoint = codePoints[index];\n            if (codePoint <= 0xffff) {\n                codeUnits.push(codePoint);\n            }\n            else {\n                codePoint -= 0x10000;\n                codeUnits.push((codePoint >> 10) + 0xd800, (codePoint % 0x400) + 0xdc00);\n            }\n            if (index + 1 === length || codeUnits.length > 0x4000) {\n                result += String.fromCharCode.apply(String, codeUnits);\n                codeUnits.length = 0;\n            }\n        }\n        return result;\n    };\n    var UnicodeTrie = createTrieFromBase64(base64);\n    var BREAK_NOT_ALLOWED = '×';\n    var BREAK_ALLOWED = '÷';\n    var codePointToClass = function (codePoint) { return UnicodeTrie.get(codePoint); };\n    var _graphemeBreakAtIndex = function (_codePoints, classTypes, index) {\n        var prevIndex = index - 2;\n        var prev = classTypes[prevIndex];\n        var current = classTypes[index - 1];\n        var next = classTypes[index];\n        // GB3 Do not break between a CR and LF\n        if (current === CR && next === LF) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB4 Otherwise, break before and after controls.\n        if (current === CR || current === LF || current === Control) {\n            return BREAK_ALLOWED;\n        }\n        // GB5\n        if (next === CR || next === LF || next === Control) {\n            return BREAK_ALLOWED;\n        }\n        // Do not break Hangul syllable sequences.\n        // GB6\n        if (current === L && [L, V, LV, LVT].indexOf(next) !== -1) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB7\n        if ((current === LV || current === V) && (next === V || next === T)) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB8\n        if ((current === LVT || current === T) && next === T) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB9 Do not break before extending characters or ZWJ.\n        if (next === ZWJ || next === Extend) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // Do not break before SpacingMarks, or after Prepend characters.\n        // GB9a\n        if (next === SpacingMark) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB9a\n        if (current === Prepend) {\n            return BREAK_NOT_ALLOWED;\n        }\n        // GB11 Do not break within emoji modifier sequences or emoji zwj sequences.\n        if (current === ZWJ && next === Extended_Pictographic) {\n            while (prev === Extend) {\n                prev = classTypes[--prevIndex];\n            }\n            if (prev === Extended_Pictographic) {\n                return BREAK_NOT_ALLOWED;\n            }\n        }\n        // GB12 Do not break within emoji flag sequences.\n        // That is, do not break between regional indicator (RI) symbols\n        // if there is an odd number of RI characters before the break point.\n        if (current === RI && next === RI) {\n            var countRI = 0;\n            while (prev === RI) {\n                countRI++;\n                prev = classTypes[--prevIndex];\n            }\n            if (countRI % 2 === 0) {\n                return BREAK_NOT_ALLOWED;\n            }\n        }\n        return BREAK_ALLOWED;\n    };\n    var GraphemeBreaker = function (str) {\n        var codePoints = toCodePoints(str);\n        var length = codePoints.length;\n        var index = 0;\n        var lastEnd = 0;\n        var classTypes = codePoints.map(codePointToClass);\n        return {\n            next: function () {\n                if (index >= length) {\n                    return { done: true, value: null };\n                }\n                var graphemeBreak = BREAK_NOT_ALLOWED;\n                while (index < length &&\n                    (graphemeBreak = _graphemeBreakAtIndex(codePoints, classTypes, ++index)) === BREAK_NOT_ALLOWED) { }\n                if (graphemeBreak !== BREAK_NOT_ALLOWED || index === length) {\n                    var value = fromCodePoint.apply(null, codePoints.slice(lastEnd, index));\n                    lastEnd = index;\n                    return { value: value, done: false };\n                }\n                return { done: true, value: null };\n            },\n        };\n    };\n    var splitGraphemes = function (str) {\n        var breaker = GraphemeBreaker(str);\n        var graphemes = [];\n        var bk;\n        while (!(bk = breaker.next()).done) {\n            if (bk.value) {\n                graphemes.push(bk.value.slice());\n            }\n        }\n        return graphemes;\n    };\n\n    var testRangeBounds = function (document) {\n        var TEST_HEIGHT = 123;\n        if (document.createRange) {\n            var range = document.createRange();\n            if (range.getBoundingClientRect) {\n                var testElement = document.createElement('boundtest');\n                testElement.style.height = TEST_HEIGHT + \"px\";\n                testElement.style.display = 'block';\n                document.body.appendChild(testElement);\n                range.selectNode(testElement);\n                var rangeBounds = range.getBoundingClientRect();\n                var rangeHeight = Math.round(rangeBounds.height);\n                document.body.removeChild(testElement);\n                if (rangeHeight === TEST_HEIGHT) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n    var testIOSLineBreak = function (document) {\n        var testElement = document.createElement('boundtest');\n        testElement.style.width = '50px';\n        testElement.style.display = 'block';\n        testElement.style.fontSize = '12px';\n        testElement.style.letterSpacing = '0px';\n        testElement.style.wordSpacing = '0px';\n        document.body.appendChild(testElement);\n        var range = document.createRange();\n        testElement.innerHTML = typeof ''.repeat === 'function' ? '&#128104;'.repeat(10) : '';\n        var node = testElement.firstChild;\n        var textList = toCodePoints$1(node.data).map(function (i) { return fromCodePoint$1(i); });\n        var offset = 0;\n        var prev = {};\n        // ios 13 does not handle range getBoundingClientRect line changes correctly #2177\n        var supports = textList.every(function (text, i) {\n            range.setStart(node, offset);\n            range.setEnd(node, offset + text.length);\n            var rect = range.getBoundingClientRect();\n            offset += text.length;\n            var boundAhead = rect.x > prev.x || rect.y > prev.y;\n            prev = rect;\n            if (i === 0) {\n                return true;\n            }\n            return boundAhead;\n        });\n        document.body.removeChild(testElement);\n        return supports;\n    };\n    var testCORS = function () { return typeof new Image().crossOrigin !== 'undefined'; };\n    var testResponseType = function () { return typeof new XMLHttpRequest().responseType === 'string'; };\n    var testSVG = function (document) {\n        var img = new Image();\n        var canvas = document.createElement('canvas');\n        var ctx = canvas.getContext('2d');\n        if (!ctx) {\n            return false;\n        }\n        img.src = \"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>\";\n        try {\n            ctx.drawImage(img, 0, 0);\n            canvas.toDataURL();\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    };\n    var isGreenPixel = function (data) {\n        return data[0] === 0 && data[1] === 255 && data[2] === 0 && data[3] === 255;\n    };\n    var testForeignObject = function (document) {\n        var canvas = document.createElement('canvas');\n        var size = 100;\n        canvas.width = size;\n        canvas.height = size;\n        var ctx = canvas.getContext('2d');\n        if (!ctx) {\n            return Promise.reject(false);\n        }\n        ctx.fillStyle = 'rgb(0, 255, 0)';\n        ctx.fillRect(0, 0, size, size);\n        var img = new Image();\n        var greenImageSrc = canvas.toDataURL();\n        img.src = greenImageSrc;\n        var svg = createForeignObjectSVG(size, size, 0, 0, img);\n        ctx.fillStyle = 'red';\n        ctx.fillRect(0, 0, size, size);\n        return loadSerializedSVG$1(svg)\n            .then(function (img) {\n            ctx.drawImage(img, 0, 0);\n            var data = ctx.getImageData(0, 0, size, size).data;\n            ctx.fillStyle = 'red';\n            ctx.fillRect(0, 0, size, size);\n            var node = document.createElement('div');\n            node.style.backgroundImage = \"url(\" + greenImageSrc + \")\";\n            node.style.height = size + \"px\";\n            // Firefox 55 does not render inline <img /> tags\n            return isGreenPixel(data)\n                ? loadSerializedSVG$1(createForeignObjectSVG(size, size, 0, 0, node))\n                : Promise.reject(false);\n        })\n            .then(function (img) {\n            ctx.drawImage(img, 0, 0);\n            // Edge does not render background-images\n            return isGreenPixel(ctx.getImageData(0, 0, size, size).data);\n        })\n            .catch(function () { return false; });\n    };\n    var createForeignObjectSVG = function (width, height, x, y, node) {\n        var xmlns = 'http://www.w3.org/2000/svg';\n        var svg = document.createElementNS(xmlns, 'svg');\n        var foreignObject = document.createElementNS(xmlns, 'foreignObject');\n        svg.setAttributeNS(null, 'width', width.toString());\n        svg.setAttributeNS(null, 'height', height.toString());\n        foreignObject.setAttributeNS(null, 'width', '100%');\n        foreignObject.setAttributeNS(null, 'height', '100%');\n        foreignObject.setAttributeNS(null, 'x', x.toString());\n        foreignObject.setAttributeNS(null, 'y', y.toString());\n        foreignObject.setAttributeNS(null, 'externalResourcesRequired', 'true');\n        svg.appendChild(foreignObject);\n        foreignObject.appendChild(node);\n        return svg;\n    };\n    var loadSerializedSVG$1 = function (svg) {\n        return new Promise(function (resolve, reject) {\n            var img = new Image();\n            img.onload = function () { return resolve(img); };\n            img.onerror = reject;\n            img.src = \"data:image/svg+xml;charset=utf-8,\" + encodeURIComponent(new XMLSerializer().serializeToString(svg));\n        });\n    };\n    var FEATURES = {\n        get SUPPORT_RANGE_BOUNDS() {\n            var value = testRangeBounds(document);\n            Object.defineProperty(FEATURES, 'SUPPORT_RANGE_BOUNDS', { value: value });\n            return value;\n        },\n        get SUPPORT_WORD_BREAKING() {\n            var value = FEATURES.SUPPORT_RANGE_BOUNDS && testIOSLineBreak(document);\n            Object.defineProperty(FEATURES, 'SUPPORT_WORD_BREAKING', { value: value });\n            return value;\n        },\n        get SUPPORT_SVG_DRAWING() {\n            var value = testSVG(document);\n            Object.defineProperty(FEATURES, 'SUPPORT_SVG_DRAWING', { value: value });\n            return value;\n        },\n        get SUPPORT_FOREIGNOBJECT_DRAWING() {\n            var value = typeof Array.from === 'function' && typeof window.fetch === 'function'\n                ? testForeignObject(document)\n                : Promise.resolve(false);\n            Object.defineProperty(FEATURES, 'SUPPORT_FOREIGNOBJECT_DRAWING', { value: value });\n            return value;\n        },\n        get SUPPORT_CORS_IMAGES() {\n            var value = testCORS();\n            Object.defineProperty(FEATURES, 'SUPPORT_CORS_IMAGES', { value: value });\n            return value;\n        },\n        get SUPPORT_RESPONSE_TYPE() {\n            var value = testResponseType();\n            Object.defineProperty(FEATURES, 'SUPPORT_RESPONSE_TYPE', { value: value });\n            return value;\n        },\n        get SUPPORT_CORS_XHR() {\n            var value = 'withCredentials' in new XMLHttpRequest();\n            Object.defineProperty(FEATURES, 'SUPPORT_CORS_XHR', { value: value });\n            return value;\n        },\n        get SUPPORT_NATIVE_TEXT_SEGMENTATION() {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            var value = !!(typeof Intl !== 'undefined' && Intl.Segmenter);\n            Object.defineProperty(FEATURES, 'SUPPORT_NATIVE_TEXT_SEGMENTATION', { value: value });\n            return value;\n        }\n    };\n\n    var TextBounds = /** @class */ (function () {\n        function TextBounds(text, bounds) {\n            this.text = text;\n            this.bounds = bounds;\n        }\n        return TextBounds;\n    }());\n    var parseTextBounds = function (context, value, styles, node) {\n        var textList = breakText(value, styles);\n        var textBounds = [];\n        var offset = 0;\n        textList.forEach(function (text) {\n            if (styles.textDecorationLine.length || text.trim().length > 0) {\n                if (FEATURES.SUPPORT_RANGE_BOUNDS) {\n                    var clientRects = createRange(node, offset, text.length).getClientRects();\n                    if (clientRects.length > 1) {\n                        var subSegments = segmentGraphemes(text);\n                        var subOffset_1 = 0;\n                        subSegments.forEach(function (subSegment) {\n                            textBounds.push(new TextBounds(subSegment, Bounds.fromDOMRectList(context, createRange(node, subOffset_1 + offset, subSegment.length).getClientRects())));\n                            subOffset_1 += subSegment.length;\n                        });\n                    }\n                    else {\n                        textBounds.push(new TextBounds(text, Bounds.fromDOMRectList(context, clientRects)));\n                    }\n                }\n                else {\n                    var replacementNode = node.splitText(text.length);\n                    textBounds.push(new TextBounds(text, getWrapperBounds(context, node)));\n                    node = replacementNode;\n                }\n            }\n            else if (!FEATURES.SUPPORT_RANGE_BOUNDS) {\n                node = node.splitText(text.length);\n            }\n            offset += text.length;\n        });\n        return textBounds;\n    };\n    var getWrapperBounds = function (context, node) {\n        var ownerDocument = node.ownerDocument;\n        if (ownerDocument) {\n            var wrapper = ownerDocument.createElement('html2canvaswrapper');\n            wrapper.appendChild(node.cloneNode(true));\n            var parentNode = node.parentNode;\n            if (parentNode) {\n                parentNode.replaceChild(wrapper, node);\n                var bounds = parseBounds(context, wrapper);\n                if (wrapper.firstChild) {\n                    parentNode.replaceChild(wrapper.firstChild, wrapper);\n                }\n                return bounds;\n            }\n        }\n        return Bounds.EMPTY;\n    };\n    var createRange = function (node, offset, length) {\n        var ownerDocument = node.ownerDocument;\n        if (!ownerDocument) {\n            throw new Error('Node has no owner document');\n        }\n        var range = ownerDocument.createRange();\n        range.setStart(node, offset);\n        range.setEnd(node, offset + length);\n        return range;\n    };\n    var segmentGraphemes = function (value) {\n        if (FEATURES.SUPPORT_NATIVE_TEXT_SEGMENTATION) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            var segmenter = new Intl.Segmenter(void 0, { granularity: 'grapheme' });\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return Array.from(segmenter.segment(value)).map(function (segment) { return segment.segment; });\n        }\n        return splitGraphemes(value);\n    };\n    var segmentWords = function (value, styles) {\n        if (FEATURES.SUPPORT_NATIVE_TEXT_SEGMENTATION) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            var segmenter = new Intl.Segmenter(void 0, {\n                granularity: 'word'\n            });\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return Array.from(segmenter.segment(value)).map(function (segment) { return segment.segment; });\n        }\n        return breakWords(value, styles);\n    };\n    var breakText = function (value, styles) {\n        return styles.letterSpacing !== 0 ? segmentGraphemes(value) : segmentWords(value, styles);\n    };\n    // https://drafts.csswg.org/css-text/#word-separator\n    var wordSeparators = [0x0020, 0x00a0, 0x1361, 0x10100, 0x10101, 0x1039, 0x1091];\n    var breakWords = function (str, styles) {\n        var breaker = LineBreaker(str, {\n            lineBreak: styles.lineBreak,\n            wordBreak: styles.overflowWrap === \"break-word\" /* BREAK_WORD */ ? 'break-word' : styles.wordBreak\n        });\n        var words = [];\n        var bk;\n        var _loop_1 = function () {\n            if (bk.value) {\n                var value = bk.value.slice();\n                var codePoints = toCodePoints$1(value);\n                var word_1 = '';\n                codePoints.forEach(function (codePoint) {\n                    if (wordSeparators.indexOf(codePoint) === -1) {\n                        word_1 += fromCodePoint$1(codePoint);\n                    }\n                    else {\n                        if (word_1.length) {\n                            words.push(word_1);\n                        }\n                        words.push(fromCodePoint$1(codePoint));\n                        word_1 = '';\n                    }\n                });\n                if (word_1.length) {\n                    words.push(word_1);\n                }\n            }\n        };\n        while (!(bk = breaker.next()).done) {\n            _loop_1();\n        }\n        return words;\n    };\n\n    var TextContainer = /** @class */ (function () {\n        function TextContainer(context, node, styles) {\n            this.text = transform(node.data, styles.textTransform);\n            this.textBounds = parseTextBounds(context, this.text, styles, node);\n        }\n        return TextContainer;\n    }());\n    var transform = function (text, transform) {\n        switch (transform) {\n            case 1 /* LOWERCASE */:\n                return text.toLowerCase();\n            case 3 /* CAPITALIZE */:\n                return text.replace(CAPITALIZE, capitalize);\n            case 2 /* UPPERCASE */:\n                return text.toUpperCase();\n            default:\n                return text;\n        }\n    };\n    var CAPITALIZE = /(^|\\s|:|-|\\(|\\))([a-z])/g;\n    var capitalize = function (m, p1, p2) {\n        if (m.length > 0) {\n            return p1 + p2.toUpperCase();\n        }\n        return m;\n    };\n\n    var ImageElementContainer = /** @class */ (function (_super) {\n        __extends(ImageElementContainer, _super);\n        function ImageElementContainer(context, img) {\n            var _this = _super.call(this, context, img) || this;\n            _this.src = img.currentSrc || img.src;\n            _this.intrinsicWidth = img.naturalWidth;\n            _this.intrinsicHeight = img.naturalHeight;\n            _this.context.cache.addImage(_this.src);\n            return _this;\n        }\n        return ImageElementContainer;\n    }(ElementContainer));\n\n    var CanvasElementContainer = /** @class */ (function (_super) {\n        __extends(CanvasElementContainer, _super);\n        function CanvasElementContainer(context, canvas) {\n            var _this = _super.call(this, context, canvas) || this;\n            _this.canvas = canvas;\n            _this.intrinsicWidth = canvas.width;\n            _this.intrinsicHeight = canvas.height;\n            return _this;\n        }\n        return CanvasElementContainer;\n    }(ElementContainer));\n\n    var SVGElementContainer = /** @class */ (function (_super) {\n        __extends(SVGElementContainer, _super);\n        function SVGElementContainer(context, img) {\n            var _this = _super.call(this, context, img) || this;\n            var s = new XMLSerializer();\n            var bounds = parseBounds(context, img);\n            img.setAttribute('width', bounds.width + \"px\");\n            img.setAttribute('height', bounds.height + \"px\");\n            _this.svg = \"data:image/svg+xml,\" + encodeURIComponent(s.serializeToString(img));\n            _this.intrinsicWidth = img.width.baseVal.value;\n            _this.intrinsicHeight = img.height.baseVal.value;\n            _this.context.cache.addImage(_this.svg);\n            return _this;\n        }\n        return SVGElementContainer;\n    }(ElementContainer));\n\n    var LIElementContainer = /** @class */ (function (_super) {\n        __extends(LIElementContainer, _super);\n        function LIElementContainer(context, element) {\n            var _this = _super.call(this, context, element) || this;\n            _this.value = element.value;\n            return _this;\n        }\n        return LIElementContainer;\n    }(ElementContainer));\n\n    var OLElementContainer = /** @class */ (function (_super) {\n        __extends(OLElementContainer, _super);\n        function OLElementContainer(context, element) {\n            var _this = _super.call(this, context, element) || this;\n            _this.start = element.start;\n            _this.reversed = typeof element.reversed === 'boolean' && element.reversed === true;\n            return _this;\n        }\n        return OLElementContainer;\n    }(ElementContainer));\n\n    var CHECKBOX_BORDER_RADIUS = [\n        {\n            type: 15 /* DIMENSION_TOKEN */,\n            flags: 0,\n            unit: 'px',\n            number: 3\n        }\n    ];\n    var RADIO_BORDER_RADIUS = [\n        {\n            type: 16 /* PERCENTAGE_TOKEN */,\n            flags: 0,\n            number: 50\n        }\n    ];\n    var reformatInputBounds = function (bounds) {\n        if (bounds.width > bounds.height) {\n            return new Bounds(bounds.left + (bounds.width - bounds.height) / 2, bounds.top, bounds.height, bounds.height);\n        }\n        else if (bounds.width < bounds.height) {\n            return new Bounds(bounds.left, bounds.top + (bounds.height - bounds.width) / 2, bounds.width, bounds.width);\n        }\n        return bounds;\n    };\n    var getInputValue = function (node) {\n        var value = node.type === PASSWORD ? new Array(node.value.length + 1).join('\\u2022') : node.value;\n        return value.length === 0 ? node.placeholder || '' : value;\n    };\n    var CHECKBOX = 'checkbox';\n    var RADIO = 'radio';\n    var PASSWORD = 'password';\n    var INPUT_COLOR = 0x2a2a2aff;\n    var InputElementContainer = /** @class */ (function (_super) {\n        __extends(InputElementContainer, _super);\n        function InputElementContainer(context, input) {\n            var _this = _super.call(this, context, input) || this;\n            _this.type = input.type.toLowerCase();\n            _this.checked = input.checked;\n            _this.value = getInputValue(input);\n            if (_this.type === CHECKBOX || _this.type === RADIO) {\n                _this.styles.backgroundColor = 0xdededeff;\n                _this.styles.borderTopColor =\n                    _this.styles.borderRightColor =\n                        _this.styles.borderBottomColor =\n                            _this.styles.borderLeftColor =\n                                0xa5a5a5ff;\n                _this.styles.borderTopWidth =\n                    _this.styles.borderRightWidth =\n                        _this.styles.borderBottomWidth =\n                            _this.styles.borderLeftWidth =\n                                1;\n                _this.styles.borderTopStyle =\n                    _this.styles.borderRightStyle =\n                        _this.styles.borderBottomStyle =\n                            _this.styles.borderLeftStyle =\n                                1 /* SOLID */;\n                _this.styles.backgroundClip = [0 /* BORDER_BOX */];\n                _this.styles.backgroundOrigin = [0 /* BORDER_BOX */];\n                _this.bounds = reformatInputBounds(_this.bounds);\n            }\n            switch (_this.type) {\n                case CHECKBOX:\n                    _this.styles.borderTopRightRadius =\n                        _this.styles.borderTopLeftRadius =\n                            _this.styles.borderBottomRightRadius =\n                                _this.styles.borderBottomLeftRadius =\n                                    CHECKBOX_BORDER_RADIUS;\n                    break;\n                case RADIO:\n                    _this.styles.borderTopRightRadius =\n                        _this.styles.borderTopLeftRadius =\n                            _this.styles.borderBottomRightRadius =\n                                _this.styles.borderBottomLeftRadius =\n                                    RADIO_BORDER_RADIUS;\n                    break;\n            }\n            return _this;\n        }\n        return InputElementContainer;\n    }(ElementContainer));\n\n    var SelectElementContainer = /** @class */ (function (_super) {\n        __extends(SelectElementContainer, _super);\n        function SelectElementContainer(context, element) {\n            var _this = _super.call(this, context, element) || this;\n            var option = element.options[element.selectedIndex || 0];\n            _this.value = option ? option.text || '' : '';\n            return _this;\n        }\n        return SelectElementContainer;\n    }(ElementContainer));\n\n    var TextareaElementContainer = /** @class */ (function (_super) {\n        __extends(TextareaElementContainer, _super);\n        function TextareaElementContainer(context, element) {\n            var _this = _super.call(this, context, element) || this;\n            _this.value = element.value;\n            return _this;\n        }\n        return TextareaElementContainer;\n    }(ElementContainer));\n\n    var IFrameElementContainer = /** @class */ (function (_super) {\n        __extends(IFrameElementContainer, _super);\n        function IFrameElementContainer(context, iframe) {\n            var _this = _super.call(this, context, iframe) || this;\n            _this.src = iframe.src;\n            _this.width = parseInt(iframe.width, 10) || 0;\n            _this.height = parseInt(iframe.height, 10) || 0;\n            _this.backgroundColor = _this.styles.backgroundColor;\n            try {\n                if (iframe.contentWindow &&\n                    iframe.contentWindow.document &&\n                    iframe.contentWindow.document.documentElement) {\n                    _this.tree = parseTree(context, iframe.contentWindow.document.documentElement);\n                    // http://www.w3.org/TR/css3-background/#special-backgrounds\n                    var documentBackgroundColor = iframe.contentWindow.document.documentElement\n                        ? parseColor(context, getComputedStyle(iframe.contentWindow.document.documentElement).backgroundColor)\n                        : COLORS.TRANSPARENT;\n                    var bodyBackgroundColor = iframe.contentWindow.document.body\n                        ? parseColor(context, getComputedStyle(iframe.contentWindow.document.body).backgroundColor)\n                        : COLORS.TRANSPARENT;\n                    _this.backgroundColor = isTransparent(documentBackgroundColor)\n                        ? isTransparent(bodyBackgroundColor)\n                            ? _this.styles.backgroundColor\n                            : bodyBackgroundColor\n                        : documentBackgroundColor;\n                }\n            }\n            catch (e) { }\n            return _this;\n        }\n        return IFrameElementContainer;\n    }(ElementContainer));\n\n    var LIST_OWNERS = ['OL', 'UL', 'MENU'];\n    var parseNodeTree = function (context, node, parent, root) {\n        for (var childNode = node.firstChild, nextNode = void 0; childNode; childNode = nextNode) {\n            nextNode = childNode.nextSibling;\n            if (isTextNode(childNode) && childNode.data.trim().length > 0) {\n                parent.textNodes.push(new TextContainer(context, childNode, parent.styles));\n            }\n            else if (isElementNode(childNode)) {\n                if (isSlotElement(childNode) && childNode.assignedNodes) {\n                    childNode.assignedNodes().forEach(function (childNode) { return parseNodeTree(context, childNode, parent, root); });\n                }\n                else {\n                    var container = createContainer(context, childNode);\n                    if (container.styles.isVisible()) {\n                        if (createsRealStackingContext(childNode, container, root)) {\n                            container.flags |= 4 /* CREATES_REAL_STACKING_CONTEXT */;\n                        }\n                        else if (createsStackingContext(container.styles)) {\n                            container.flags |= 2 /* CREATES_STACKING_CONTEXT */;\n                        }\n                        if (LIST_OWNERS.indexOf(childNode.tagName) !== -1) {\n                            container.flags |= 8 /* IS_LIST_OWNER */;\n                        }\n                        parent.elements.push(container);\n                        childNode.slot;\n                        if (childNode.shadowRoot) {\n                            parseNodeTree(context, childNode.shadowRoot, container, root);\n                        }\n                        else if (!isTextareaElement(childNode) &&\n                            !isSVGElement(childNode) &&\n                            !isSelectElement(childNode)) {\n                            parseNodeTree(context, childNode, container, root);\n                        }\n                    }\n                }\n            }\n        }\n    };\n    var createContainer = function (context, element) {\n        if (isImageElement(element)) {\n            return new ImageElementContainer(context, element);\n        }\n        if (isCanvasElement(element)) {\n            return new CanvasElementContainer(context, element);\n        }\n        if (isSVGElement(element)) {\n            return new SVGElementContainer(context, element);\n        }\n        if (isLIElement(element)) {\n            return new LIElementContainer(context, element);\n        }\n        if (isOLElement(element)) {\n            return new OLElementContainer(context, element);\n        }\n        if (isInputElement(element)) {\n            return new InputElementContainer(context, element);\n        }\n        if (isSelectElement(element)) {\n            return new SelectElementContainer(context, element);\n        }\n        if (isTextareaElement(element)) {\n            return new TextareaElementContainer(context, element);\n        }\n        if (isIFrameElement(element)) {\n            return new IFrameElementContainer(context, element);\n        }\n        return new ElementContainer(context, element);\n    };\n    var parseTree = function (context, element) {\n        var container = createContainer(context, element);\n        container.flags |= 4 /* CREATES_REAL_STACKING_CONTEXT */;\n        parseNodeTree(context, element, container, container);\n        return container;\n    };\n    var createsRealStackingContext = function (node, container, root) {\n        return (container.styles.isPositionedWithZIndex() ||\n            container.styles.opacity < 1 ||\n            container.styles.isTransformed() ||\n            (isBodyElement(node) && root.styles.isTransparent()));\n    };\n    var createsStackingContext = function (styles) { return styles.isPositioned() || styles.isFloating(); };\n    var isTextNode = function (node) { return node.nodeType === Node.TEXT_NODE; };\n    var isElementNode = function (node) { return node.nodeType === Node.ELEMENT_NODE; };\n    var isHTMLElementNode = function (node) {\n        return isElementNode(node) && typeof node.style !== 'undefined' && !isSVGElementNode(node);\n    };\n    var isSVGElementNode = function (element) {\n        return typeof element.className === 'object';\n    };\n    var isLIElement = function (node) { return node.tagName === 'LI'; };\n    var isOLElement = function (node) { return node.tagName === 'OL'; };\n    var isInputElement = function (node) { return node.tagName === 'INPUT'; };\n    var isHTMLElement = function (node) { return node.tagName === 'HTML'; };\n    var isSVGElement = function (node) { return node.tagName === 'svg'; };\n    var isBodyElement = function (node) { return node.tagName === 'BODY'; };\n    var isCanvasElement = function (node) { return node.tagName === 'CANVAS'; };\n    var isVideoElement = function (node) { return node.tagName === 'VIDEO'; };\n    var isImageElement = function (node) { return node.tagName === 'IMG'; };\n    var isIFrameElement = function (node) { return node.tagName === 'IFRAME'; };\n    var isStyleElement = function (node) { return node.tagName === 'STYLE'; };\n    var isScriptElement = function (node) { return node.tagName === 'SCRIPT'; };\n    var isTextareaElement = function (node) { return node.tagName === 'TEXTAREA'; };\n    var isSelectElement = function (node) { return node.tagName === 'SELECT'; };\n    var isSlotElement = function (node) { return node.tagName === 'SLOT'; };\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n    var isCustomElement = function (node) { return node.tagName.indexOf('-') > 0; };\n\n    var CounterState = /** @class */ (function () {\n        function CounterState() {\n            this.counters = {};\n        }\n        CounterState.prototype.getCounterValue = function (name) {\n            var counter = this.counters[name];\n            if (counter && counter.length) {\n                return counter[counter.length - 1];\n            }\n            return 1;\n        };\n        CounterState.prototype.getCounterValues = function (name) {\n            var counter = this.counters[name];\n            return counter ? counter : [];\n        };\n        CounterState.prototype.pop = function (counters) {\n            var _this = this;\n            counters.forEach(function (counter) { return _this.counters[counter].pop(); });\n        };\n        CounterState.prototype.parse = function (style) {\n            var _this = this;\n            var counterIncrement = style.counterIncrement;\n            var counterReset = style.counterReset;\n            var canReset = true;\n            if (counterIncrement !== null) {\n                counterIncrement.forEach(function (entry) {\n                    var counter = _this.counters[entry.counter];\n                    if (counter && entry.increment !== 0) {\n                        canReset = false;\n                        if (!counter.length) {\n                            counter.push(1);\n                        }\n                        counter[Math.max(0, counter.length - 1)] += entry.increment;\n                    }\n                });\n            }\n            var counterNames = [];\n            if (canReset) {\n                counterReset.forEach(function (entry) {\n                    var counter = _this.counters[entry.counter];\n                    counterNames.push(entry.counter);\n                    if (!counter) {\n                        counter = _this.counters[entry.counter] = [];\n                    }\n                    counter.push(entry.reset);\n                });\n            }\n            return counterNames;\n        };\n        return CounterState;\n    }());\n    var ROMAN_UPPER = {\n        integers: [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1],\n        values: ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I']\n    };\n    var ARMENIAN = {\n        integers: [\n            9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70,\n            60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1\n        ],\n        values: [\n            'Ք',\n            'Փ',\n            'Ւ',\n            'Ց',\n            'Ր',\n            'Տ',\n            'Վ',\n            'Ս',\n            'Ռ',\n            'Ջ',\n            'Պ',\n            'Չ',\n            'Ո',\n            'Շ',\n            'Ն',\n            'Յ',\n            'Մ',\n            'Ճ',\n            'Ղ',\n            'Ձ',\n            'Հ',\n            'Կ',\n            'Ծ',\n            'Խ',\n            'Լ',\n            'Ի',\n            'Ժ',\n            'Թ',\n            'Ը',\n            'Է',\n            'Զ',\n            'Ե',\n            'Դ',\n            'Գ',\n            'Բ',\n            'Ա'\n        ]\n    };\n    var HEBREW = {\n        integers: [\n            10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20,\n            19, 18, 17, 16, 15, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1\n        ],\n        values: [\n            'י׳',\n            'ט׳',\n            'ח׳',\n            'ז׳',\n            'ו׳',\n            'ה׳',\n            'ד׳',\n            'ג׳',\n            'ב׳',\n            'א׳',\n            'ת',\n            'ש',\n            'ר',\n            'ק',\n            'צ',\n            'פ',\n            'ע',\n            'ס',\n            'נ',\n            'מ',\n            'ל',\n            'כ',\n            'יט',\n            'יח',\n            'יז',\n            'טז',\n            'טו',\n            'י',\n            'ט',\n            'ח',\n            'ז',\n            'ו',\n            'ה',\n            'ד',\n            'ג',\n            'ב',\n            'א'\n        ]\n    };\n    var GEORGIAN = {\n        integers: [\n            10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90,\n            80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1\n        ],\n        values: [\n            'ჵ',\n            'ჰ',\n            'ჯ',\n            'ჴ',\n            'ხ',\n            'ჭ',\n            'წ',\n            'ძ',\n            'ც',\n            'ჩ',\n            'შ',\n            'ყ',\n            'ღ',\n            'ქ',\n            'ფ',\n            'ჳ',\n            'ტ',\n            'ს',\n            'რ',\n            'ჟ',\n            'პ',\n            'ო',\n            'ჲ',\n            'ნ',\n            'მ',\n            'ლ',\n            'კ',\n            'ი',\n            'თ',\n            'ჱ',\n            'ზ',\n            'ვ',\n            'ე',\n            'დ',\n            'გ',\n            'ბ',\n            'ა'\n        ]\n    };\n    var createAdditiveCounter = function (value, min, max, symbols, fallback, suffix) {\n        if (value < min || value > max) {\n            return createCounterText(value, fallback, suffix.length > 0);\n        }\n        return (symbols.integers.reduce(function (string, integer, index) {\n            while (value >= integer) {\n                value -= integer;\n                string += symbols.values[index];\n            }\n            return string;\n        }, '') + suffix);\n    };\n    var createCounterStyleWithSymbolResolver = function (value, codePointRangeLength, isNumeric, resolver) {\n        var string = '';\n        do {\n            if (!isNumeric) {\n                value--;\n            }\n            string = resolver(value) + string;\n            value /= codePointRangeLength;\n        } while (value * codePointRangeLength >= codePointRangeLength);\n        return string;\n    };\n    var createCounterStyleFromRange = function (value, codePointRangeStart, codePointRangeEnd, isNumeric, suffix) {\n        var codePointRangeLength = codePointRangeEnd - codePointRangeStart + 1;\n        return ((value < 0 ? '-' : '') +\n            (createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, isNumeric, function (codePoint) {\n                return fromCodePoint$1(Math.floor(codePoint % codePointRangeLength) + codePointRangeStart);\n            }) +\n                suffix));\n    };\n    var createCounterStyleFromSymbols = function (value, symbols, suffix) {\n        if (suffix === void 0) { suffix = '. '; }\n        var codePointRangeLength = symbols.length;\n        return (createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, false, function (codePoint) { return symbols[Math.floor(codePoint % codePointRangeLength)]; }) + suffix);\n    };\n    var CJK_ZEROS = 1 << 0;\n    var CJK_TEN_COEFFICIENTS = 1 << 1;\n    var CJK_TEN_HIGH_COEFFICIENTS = 1 << 2;\n    var CJK_HUNDRED_COEFFICIENTS = 1 << 3;\n    var createCJKCounter = function (value, numbers, multipliers, negativeSign, suffix, flags) {\n        if (value < -9999 || value > 9999) {\n            return createCounterText(value, 4 /* CJK_DECIMAL */, suffix.length > 0);\n        }\n        var tmp = Math.abs(value);\n        var string = suffix;\n        if (tmp === 0) {\n            return numbers[0] + string;\n        }\n        for (var digit = 0; tmp > 0 && digit <= 4; digit++) {\n            var coefficient = tmp % 10;\n            if (coefficient === 0 && contains(flags, CJK_ZEROS) && string !== '') {\n                string = numbers[coefficient] + string;\n            }\n            else if (coefficient > 1 ||\n                (coefficient === 1 && digit === 0) ||\n                (coefficient === 1 && digit === 1 && contains(flags, CJK_TEN_COEFFICIENTS)) ||\n                (coefficient === 1 && digit === 1 && contains(flags, CJK_TEN_HIGH_COEFFICIENTS) && value > 100) ||\n                (coefficient === 1 && digit > 1 && contains(flags, CJK_HUNDRED_COEFFICIENTS))) {\n                string = numbers[coefficient] + (digit > 0 ? multipliers[digit - 1] : '') + string;\n            }\n            else if (coefficient === 1 && digit > 0) {\n                string = multipliers[digit - 1] + string;\n            }\n            tmp = Math.floor(tmp / 10);\n        }\n        return (value < 0 ? negativeSign : '') + string;\n    };\n    var CHINESE_INFORMAL_MULTIPLIERS = '十百千萬';\n    var CHINESE_FORMAL_MULTIPLIERS = '拾佰仟萬';\n    var JAPANESE_NEGATIVE = 'マイナス';\n    var KOREAN_NEGATIVE = '마이너스';\n    var createCounterText = function (value, type, appendSuffix) {\n        var defaultSuffix = appendSuffix ? '. ' : '';\n        var cjkSuffix = appendSuffix ? '、' : '';\n        var koreanSuffix = appendSuffix ? ', ' : '';\n        var spaceSuffix = appendSuffix ? ' ' : '';\n        switch (type) {\n            case 0 /* DISC */:\n                return '•' + spaceSuffix;\n            case 1 /* CIRCLE */:\n                return '◦' + spaceSuffix;\n            case 2 /* SQUARE */:\n                return '◾' + spaceSuffix;\n            case 5 /* DECIMAL_LEADING_ZERO */:\n                var string = createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n                return string.length < 4 ? \"0\" + string : string;\n            case 4 /* CJK_DECIMAL */:\n                return createCounterStyleFromSymbols(value, '〇一二三四五六七八九', cjkSuffix);\n            case 6 /* LOWER_ROMAN */:\n                return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, 3 /* DECIMAL */, defaultSuffix).toLowerCase();\n            case 7 /* UPPER_ROMAN */:\n                return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, 3 /* DECIMAL */, defaultSuffix);\n            case 8 /* LOWER_GREEK */:\n                return createCounterStyleFromRange(value, 945, 969, false, defaultSuffix);\n            case 9 /* LOWER_ALPHA */:\n                return createCounterStyleFromRange(value, 97, 122, false, defaultSuffix);\n            case 10 /* UPPER_ALPHA */:\n                return createCounterStyleFromRange(value, 65, 90, false, defaultSuffix);\n            case 11 /* ARABIC_INDIC */:\n                return createCounterStyleFromRange(value, 1632, 1641, true, defaultSuffix);\n            case 12 /* ARMENIAN */:\n            case 49 /* UPPER_ARMENIAN */:\n                return createAdditiveCounter(value, 1, 9999, ARMENIAN, 3 /* DECIMAL */, defaultSuffix);\n            case 35 /* LOWER_ARMENIAN */:\n                return createAdditiveCounter(value, 1, 9999, ARMENIAN, 3 /* DECIMAL */, defaultSuffix).toLowerCase();\n            case 13 /* BENGALI */:\n                return createCounterStyleFromRange(value, 2534, 2543, true, defaultSuffix);\n            case 14 /* CAMBODIAN */:\n            case 30 /* KHMER */:\n                return createCounterStyleFromRange(value, 6112, 6121, true, defaultSuffix);\n            case 15 /* CJK_EARTHLY_BRANCH */:\n                return createCounterStyleFromSymbols(value, '子丑寅卯辰巳午未申酉戌亥', cjkSuffix);\n            case 16 /* CJK_HEAVENLY_STEM */:\n                return createCounterStyleFromSymbols(value, '甲乙丙丁戊己庚辛壬癸', cjkSuffix);\n            case 17 /* CJK_IDEOGRAPHIC */:\n            case 48 /* TRAD_CHINESE_INFORMAL */:\n                return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n            case 47 /* TRAD_CHINESE_FORMAL */:\n                return createCJKCounter(value, '零壹貳參肆伍陸柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n            case 42 /* SIMP_CHINESE_INFORMAL */:\n                return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n            case 41 /* SIMP_CHINESE_FORMAL */:\n                return createCJKCounter(value, '零壹贰叁肆伍陆柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n            case 26 /* JAPANESE_INFORMAL */:\n                return createCJKCounter(value, '〇一二三四五六七八九', '十百千万', JAPANESE_NEGATIVE, cjkSuffix, 0);\n            case 25 /* JAPANESE_FORMAL */:\n                return createCJKCounter(value, '零壱弐参四伍六七八九', '拾百千万', JAPANESE_NEGATIVE, cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n            case 31 /* KOREAN_HANGUL_FORMAL */:\n                return createCJKCounter(value, '영일이삼사오육칠팔구', '십백천만', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n            case 33 /* KOREAN_HANJA_INFORMAL */:\n                return createCJKCounter(value, '零一二三四五六七八九', '十百千萬', KOREAN_NEGATIVE, koreanSuffix, 0);\n            case 32 /* KOREAN_HANJA_FORMAL */:\n                return createCJKCounter(value, '零壹貳參四五六七八九', '拾百千', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n            case 18 /* DEVANAGARI */:\n                return createCounterStyleFromRange(value, 0x966, 0x96f, true, defaultSuffix);\n            case 20 /* GEORGIAN */:\n                return createAdditiveCounter(value, 1, 19999, GEORGIAN, 3 /* DECIMAL */, defaultSuffix);\n            case 21 /* GUJARATI */:\n                return createCounterStyleFromRange(value, 0xae6, 0xaef, true, defaultSuffix);\n            case 22 /* GURMUKHI */:\n                return createCounterStyleFromRange(value, 0xa66, 0xa6f, true, defaultSuffix);\n            case 22 /* HEBREW */:\n                return createAdditiveCounter(value, 1, 10999, HEBREW, 3 /* DECIMAL */, defaultSuffix);\n            case 23 /* HIRAGANA */:\n                return createCounterStyleFromSymbols(value, 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん');\n            case 24 /* HIRAGANA_IROHA */:\n                return createCounterStyleFromSymbols(value, 'いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす');\n            case 27 /* KANNADA */:\n                return createCounterStyleFromRange(value, 0xce6, 0xcef, true, defaultSuffix);\n            case 28 /* KATAKANA */:\n                return createCounterStyleFromSymbols(value, 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン', cjkSuffix);\n            case 29 /* KATAKANA_IROHA */:\n                return createCounterStyleFromSymbols(value, 'イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス', cjkSuffix);\n            case 34 /* LAO */:\n                return createCounterStyleFromRange(value, 0xed0, 0xed9, true, defaultSuffix);\n            case 37 /* MONGOLIAN */:\n                return createCounterStyleFromRange(value, 0x1810, 0x1819, true, defaultSuffix);\n            case 38 /* MYANMAR */:\n                return createCounterStyleFromRange(value, 0x1040, 0x1049, true, defaultSuffix);\n            case 39 /* ORIYA */:\n                return createCounterStyleFromRange(value, 0xb66, 0xb6f, true, defaultSuffix);\n            case 40 /* PERSIAN */:\n                return createCounterStyleFromRange(value, 0x6f0, 0x6f9, true, defaultSuffix);\n            case 43 /* TAMIL */:\n                return createCounterStyleFromRange(value, 0xbe6, 0xbef, true, defaultSuffix);\n            case 44 /* TELUGU */:\n                return createCounterStyleFromRange(value, 0xc66, 0xc6f, true, defaultSuffix);\n            case 45 /* THAI */:\n                return createCounterStyleFromRange(value, 0xe50, 0xe59, true, defaultSuffix);\n            case 46 /* TIBETAN */:\n                return createCounterStyleFromRange(value, 0xf20, 0xf29, true, defaultSuffix);\n            case 3 /* DECIMAL */:\n            default:\n                return createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n        }\n    };\n\n    var IGNORE_ATTRIBUTE = 'data-html2canvas-ignore';\n    var DocumentCloner = /** @class */ (function () {\n        function DocumentCloner(context, element, options) {\n            this.context = context;\n            this.options = options;\n            this.scrolledElements = [];\n            this.referenceElement = element;\n            this.counters = new CounterState();\n            this.quoteDepth = 0;\n            if (!element.ownerDocument) {\n                throw new Error('Cloned element does not have an owner document');\n            }\n            this.documentElement = this.cloneNode(element.ownerDocument.documentElement, false);\n        }\n        DocumentCloner.prototype.toIFrame = function (ownerDocument, windowSize) {\n            var _this = this;\n            var iframe = createIFrameContainer(ownerDocument, windowSize);\n            if (!iframe.contentWindow) {\n                return Promise.reject(\"Unable to find iframe window\");\n            }\n            var scrollX = ownerDocument.defaultView.pageXOffset;\n            var scrollY = ownerDocument.defaultView.pageYOffset;\n            var cloneWindow = iframe.contentWindow;\n            var documentClone = cloneWindow.document;\n            /* Chrome doesn't detect relative background-images assigned in inline <style> sheets when fetched through getComputedStyle\n             if window url is about:blank, we can assign the url to current by writing onto the document\n             */\n            var iframeLoad = iframeLoader(iframe).then(function () { return __awaiter(_this, void 0, void 0, function () {\n                var onclone, referenceElement;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            this.scrolledElements.forEach(restoreNodeScroll);\n                            if (cloneWindow) {\n                                cloneWindow.scrollTo(windowSize.left, windowSize.top);\n                                if (/(iPad|iPhone|iPod)/g.test(navigator.userAgent) &&\n                                    (cloneWindow.scrollY !== windowSize.top || cloneWindow.scrollX !== windowSize.left)) {\n                                    this.context.logger.warn('Unable to restore scroll position for cloned document');\n                                    this.context.windowBounds = this.context.windowBounds.add(cloneWindow.scrollX - windowSize.left, cloneWindow.scrollY - windowSize.top, 0, 0);\n                                }\n                            }\n                            onclone = this.options.onclone;\n                            referenceElement = this.clonedReferenceElement;\n                            if (typeof referenceElement === 'undefined') {\n                                return [2 /*return*/, Promise.reject(\"Error finding the \" + this.referenceElement.nodeName + \" in the cloned document\")];\n                            }\n                            if (!(documentClone.fonts && documentClone.fonts.ready)) return [3 /*break*/, 2];\n                            return [4 /*yield*/, documentClone.fonts.ready];\n                        case 1:\n                            _a.sent();\n                            _a.label = 2;\n                        case 2:\n                            if (!/(AppleWebKit)/g.test(navigator.userAgent)) return [3 /*break*/, 4];\n                            return [4 /*yield*/, imagesReady(documentClone)];\n                        case 3:\n                            _a.sent();\n                            _a.label = 4;\n                        case 4:\n                            if (typeof onclone === 'function') {\n                                return [2 /*return*/, Promise.resolve()\n                                        .then(function () { return onclone(documentClone, referenceElement); })\n                                        .then(function () { return iframe; })];\n                            }\n                            return [2 /*return*/, iframe];\n                    }\n                });\n            }); });\n            documentClone.open();\n            documentClone.write(serializeDoctype(document.doctype) + \"<html></html>\");\n            // Chrome scrolls the parent document for some reason after the write to the cloned window???\n            restoreOwnerScroll(this.referenceElement.ownerDocument, scrollX, scrollY);\n            documentClone.replaceChild(documentClone.adoptNode(this.documentElement), documentClone.documentElement);\n            documentClone.close();\n            return iframeLoad;\n        };\n        DocumentCloner.prototype.createElementClone = function (node) {\n            if (isDebugging(node, 2 /* CLONE */)) {\n                debugger;\n            }\n            if (isCanvasElement(node)) {\n                return this.createCanvasClone(node);\n            }\n            if (isVideoElement(node)) {\n                return this.createVideoClone(node);\n            }\n            if (isStyleElement(node)) {\n                return this.createStyleClone(node);\n            }\n            var clone = node.cloneNode(false);\n            if (isImageElement(clone)) {\n                if (isImageElement(node) && node.currentSrc && node.currentSrc !== node.src) {\n                    clone.src = node.currentSrc;\n                    clone.srcset = '';\n                }\n                if (clone.loading === 'lazy') {\n                    clone.loading = 'eager';\n                }\n            }\n            if (isCustomElement(clone)) {\n                return this.createCustomElementClone(clone);\n            }\n            return clone;\n        };\n        DocumentCloner.prototype.createCustomElementClone = function (node) {\n            var clone = document.createElement('html2canvascustomelement');\n            copyCSSStyles(node.style, clone);\n            return clone;\n        };\n        DocumentCloner.prototype.createStyleClone = function (node) {\n            try {\n                var sheet = node.sheet;\n                if (sheet && sheet.cssRules) {\n                    var css = [].slice.call(sheet.cssRules, 0).reduce(function (css, rule) {\n                        if (rule && typeof rule.cssText === 'string') {\n                            return css + rule.cssText;\n                        }\n                        return css;\n                    }, '');\n                    var style = node.cloneNode(false);\n                    style.textContent = css;\n                    return style;\n                }\n            }\n            catch (e) {\n                // accessing node.sheet.cssRules throws a DOMException\n                this.context.logger.error('Unable to access cssRules property', e);\n                if (e.name !== 'SecurityError') {\n                    throw e;\n                }\n            }\n            return node.cloneNode(false);\n        };\n        DocumentCloner.prototype.createCanvasClone = function (canvas) {\n            var _a;\n            if (this.options.inlineImages && canvas.ownerDocument) {\n                var img = canvas.ownerDocument.createElement('img');\n                try {\n                    img.src = canvas.toDataURL();\n                    return img;\n                }\n                catch (e) {\n                    this.context.logger.info(\"Unable to inline canvas contents, canvas is tainted\", canvas);\n                }\n            }\n            var clonedCanvas = canvas.cloneNode(false);\n            try {\n                clonedCanvas.width = canvas.width;\n                clonedCanvas.height = canvas.height;\n                var ctx = canvas.getContext('2d');\n                var clonedCtx = clonedCanvas.getContext('2d');\n                if (clonedCtx) {\n                    if (!this.options.allowTaint && ctx) {\n                        clonedCtx.putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);\n                    }\n                    else {\n                        var gl = (_a = canvas.getContext('webgl2')) !== null && _a !== void 0 ? _a : canvas.getContext('webgl');\n                        if (gl) {\n                            var attribs = gl.getContextAttributes();\n                            if ((attribs === null || attribs === void 0 ? void 0 : attribs.preserveDrawingBuffer) === false) {\n                                this.context.logger.warn('Unable to clone WebGL context as it has preserveDrawingBuffer=false', canvas);\n                            }\n                        }\n                        clonedCtx.drawImage(canvas, 0, 0);\n                    }\n                }\n                return clonedCanvas;\n            }\n            catch (e) {\n                this.context.logger.info(\"Unable to clone canvas as it is tainted\", canvas);\n            }\n            return clonedCanvas;\n        };\n        DocumentCloner.prototype.createVideoClone = function (video) {\n            var canvas = video.ownerDocument.createElement('canvas');\n            canvas.width = video.offsetWidth;\n            canvas.height = video.offsetHeight;\n            var ctx = canvas.getContext('2d');\n            try {\n                if (ctx) {\n                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n                    if (!this.options.allowTaint) {\n                        ctx.getImageData(0, 0, canvas.width, canvas.height);\n                    }\n                }\n                return canvas;\n            }\n            catch (e) {\n                this.context.logger.info(\"Unable to clone video as it is tainted\", video);\n            }\n            var blankCanvas = video.ownerDocument.createElement('canvas');\n            blankCanvas.width = video.offsetWidth;\n            blankCanvas.height = video.offsetHeight;\n            return blankCanvas;\n        };\n        DocumentCloner.prototype.appendChildNode = function (clone, child, copyStyles) {\n            if (!isElementNode(child) ||\n                (!isScriptElement(child) &&\n                    !child.hasAttribute(IGNORE_ATTRIBUTE) &&\n                    (typeof this.options.ignoreElements !== 'function' || !this.options.ignoreElements(child)))) {\n                if (!this.options.copyStyles || !isElementNode(child) || !isStyleElement(child)) {\n                    clone.appendChild(this.cloneNode(child, copyStyles));\n                }\n            }\n        };\n        DocumentCloner.prototype.cloneChildNodes = function (node, clone, copyStyles) {\n            var _this = this;\n            for (var child = node.shadowRoot ? node.shadowRoot.firstChild : node.firstChild; child; child = child.nextSibling) {\n                if (isElementNode(child) && isSlotElement(child) && typeof child.assignedNodes === 'function') {\n                    var assignedNodes = child.assignedNodes();\n                    if (assignedNodes.length) {\n                        assignedNodes.forEach(function (assignedNode) { return _this.appendChildNode(clone, assignedNode, copyStyles); });\n                    }\n                }\n                else {\n                    this.appendChildNode(clone, child, copyStyles);\n                }\n            }\n        };\n        DocumentCloner.prototype.cloneNode = function (node, copyStyles) {\n            if (isTextNode(node)) {\n                return document.createTextNode(node.data);\n            }\n            if (!node.ownerDocument) {\n                return node.cloneNode(false);\n            }\n            var window = node.ownerDocument.defaultView;\n            if (window && isElementNode(node) && (isHTMLElementNode(node) || isSVGElementNode(node))) {\n                var clone = this.createElementClone(node);\n                clone.style.transitionProperty = 'none';\n                var style = window.getComputedStyle(node);\n                var styleBefore = window.getComputedStyle(node, ':before');\n                var styleAfter = window.getComputedStyle(node, ':after');\n                if (this.referenceElement === node && isHTMLElementNode(clone)) {\n                    this.clonedReferenceElement = clone;\n                }\n                if (isBodyElement(clone)) {\n                    createPseudoHideStyles(clone);\n                }\n                var counters = this.counters.parse(new CSSParsedCounterDeclaration(this.context, style));\n                var before = this.resolvePseudoContent(node, clone, styleBefore, PseudoElementType.BEFORE);\n                if (isCustomElement(node)) {\n                    copyStyles = true;\n                }\n                if (!isVideoElement(node)) {\n                    this.cloneChildNodes(node, clone, copyStyles);\n                }\n                if (before) {\n                    clone.insertBefore(before, clone.firstChild);\n                }\n                var after = this.resolvePseudoContent(node, clone, styleAfter, PseudoElementType.AFTER);\n                if (after) {\n                    clone.appendChild(after);\n                }\n                this.counters.pop(counters);\n                if ((style && (this.options.copyStyles || isSVGElementNode(node)) && !isIFrameElement(node)) ||\n                    copyStyles) {\n                    copyCSSStyles(style, clone);\n                }\n                if (node.scrollTop !== 0 || node.scrollLeft !== 0) {\n                    this.scrolledElements.push([clone, node.scrollLeft, node.scrollTop]);\n                }\n                if ((isTextareaElement(node) || isSelectElement(node)) &&\n                    (isTextareaElement(clone) || isSelectElement(clone))) {\n                    clone.value = node.value;\n                }\n                return clone;\n            }\n            return node.cloneNode(false);\n        };\n        DocumentCloner.prototype.resolvePseudoContent = function (node, clone, style, pseudoElt) {\n            var _this = this;\n            if (!style) {\n                return;\n            }\n            var value = style.content;\n            var document = clone.ownerDocument;\n            if (!document || !value || value === 'none' || value === '-moz-alt-content' || style.display === 'none') {\n                return;\n            }\n            this.counters.parse(new CSSParsedCounterDeclaration(this.context, style));\n            var declaration = new CSSParsedPseudoDeclaration(this.context, style);\n            var anonymousReplacedElement = document.createElement('html2canvaspseudoelement');\n            copyCSSStyles(style, anonymousReplacedElement);\n            declaration.content.forEach(function (token) {\n                if (token.type === 0 /* STRING_TOKEN */) {\n                    anonymousReplacedElement.appendChild(document.createTextNode(token.value));\n                }\n                else if (token.type === 22 /* URL_TOKEN */) {\n                    var img = document.createElement('img');\n                    img.src = token.value;\n                    img.style.opacity = '1';\n                    anonymousReplacedElement.appendChild(img);\n                }\n                else if (token.type === 18 /* FUNCTION */) {\n                    if (token.name === 'attr') {\n                        var attr = token.values.filter(isIdentToken);\n                        if (attr.length) {\n                            anonymousReplacedElement.appendChild(document.createTextNode(node.getAttribute(attr[0].value) || ''));\n                        }\n                    }\n                    else if (token.name === 'counter') {\n                        var _a = token.values.filter(nonFunctionArgSeparator), counter = _a[0], counterStyle = _a[1];\n                        if (counter && isIdentToken(counter)) {\n                            var counterState = _this.counters.getCounterValue(counter.value);\n                            var counterType = counterStyle && isIdentToken(counterStyle)\n                                ? listStyleType.parse(_this.context, counterStyle.value)\n                                : 3 /* DECIMAL */;\n                            anonymousReplacedElement.appendChild(document.createTextNode(createCounterText(counterState, counterType, false)));\n                        }\n                    }\n                    else if (token.name === 'counters') {\n                        var _b = token.values.filter(nonFunctionArgSeparator), counter = _b[0], delim = _b[1], counterStyle = _b[2];\n                        if (counter && isIdentToken(counter)) {\n                            var counterStates = _this.counters.getCounterValues(counter.value);\n                            var counterType_1 = counterStyle && isIdentToken(counterStyle)\n                                ? listStyleType.parse(_this.context, counterStyle.value)\n                                : 3 /* DECIMAL */;\n                            var separator = delim && delim.type === 0 /* STRING_TOKEN */ ? delim.value : '';\n                            var text = counterStates\n                                .map(function (value) { return createCounterText(value, counterType_1, false); })\n                                .join(separator);\n                            anonymousReplacedElement.appendChild(document.createTextNode(text));\n                        }\n                    }\n                    else ;\n                }\n                else if (token.type === 20 /* IDENT_TOKEN */) {\n                    switch (token.value) {\n                        case 'open-quote':\n                            anonymousReplacedElement.appendChild(document.createTextNode(getQuote(declaration.quotes, _this.quoteDepth++, true)));\n                            break;\n                        case 'close-quote':\n                            anonymousReplacedElement.appendChild(document.createTextNode(getQuote(declaration.quotes, --_this.quoteDepth, false)));\n                            break;\n                        default:\n                            // safari doesn't parse string tokens correctly because of lack of quotes\n                            anonymousReplacedElement.appendChild(document.createTextNode(token.value));\n                    }\n                }\n            });\n            anonymousReplacedElement.className = PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + \" \" + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n            var newClassName = pseudoElt === PseudoElementType.BEFORE\n                ? \" \" + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE\n                : \" \" + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n            if (isSVGElementNode(clone)) {\n                clone.className.baseValue += newClassName;\n            }\n            else {\n                clone.className += newClassName;\n            }\n            return anonymousReplacedElement;\n        };\n        DocumentCloner.destroy = function (container) {\n            if (container.parentNode) {\n                container.parentNode.removeChild(container);\n                return true;\n            }\n            return false;\n        };\n        return DocumentCloner;\n    }());\n    var PseudoElementType;\n    (function (PseudoElementType) {\n        PseudoElementType[PseudoElementType[\"BEFORE\"] = 0] = \"BEFORE\";\n        PseudoElementType[PseudoElementType[\"AFTER\"] = 1] = \"AFTER\";\n    })(PseudoElementType || (PseudoElementType = {}));\n    var createIFrameContainer = function (ownerDocument, bounds) {\n        var cloneIframeContainer = ownerDocument.createElement('iframe');\n        cloneIframeContainer.className = 'html2canvas-container';\n        cloneIframeContainer.style.visibility = 'hidden';\n        cloneIframeContainer.style.position = 'fixed';\n        cloneIframeContainer.style.left = '-10000px';\n        cloneIframeContainer.style.top = '0px';\n        cloneIframeContainer.style.border = '0';\n        cloneIframeContainer.width = bounds.width.toString();\n        cloneIframeContainer.height = bounds.height.toString();\n        cloneIframeContainer.scrolling = 'no'; // ios won't scroll without it\n        cloneIframeContainer.setAttribute(IGNORE_ATTRIBUTE, 'true');\n        ownerDocument.body.appendChild(cloneIframeContainer);\n        return cloneIframeContainer;\n    };\n    var imageReady = function (img) {\n        return new Promise(function (resolve) {\n            if (img.complete) {\n                resolve();\n                return;\n            }\n            if (!img.src) {\n                resolve();\n                return;\n            }\n            img.onload = resolve;\n            img.onerror = resolve;\n        });\n    };\n    var imagesReady = function (document) {\n        return Promise.all([].slice.call(document.images, 0).map(imageReady));\n    };\n    var iframeLoader = function (iframe) {\n        return new Promise(function (resolve, reject) {\n            var cloneWindow = iframe.contentWindow;\n            if (!cloneWindow) {\n                return reject(\"No window assigned for iframe\");\n            }\n            var documentClone = cloneWindow.document;\n            cloneWindow.onload = iframe.onload = function () {\n                cloneWindow.onload = iframe.onload = null;\n                var interval = setInterval(function () {\n                    if (documentClone.body.childNodes.length > 0 && documentClone.readyState === 'complete') {\n                        clearInterval(interval);\n                        resolve(iframe);\n                    }\n                }, 50);\n            };\n        });\n    };\n    var ignoredStyleProperties = [\n        'all',\n        'd',\n        'content' // Safari shows pseudoelements if content is set\n    ];\n    var copyCSSStyles = function (style, target) {\n        // Edge does not provide value for cssText\n        for (var i = style.length - 1; i >= 0; i--) {\n            var property = style.item(i);\n            if (ignoredStyleProperties.indexOf(property) === -1) {\n                target.style.setProperty(property, style.getPropertyValue(property));\n            }\n        }\n        return target;\n    };\n    var serializeDoctype = function (doctype) {\n        var str = '';\n        if (doctype) {\n            str += '<!DOCTYPE ';\n            if (doctype.name) {\n                str += doctype.name;\n            }\n            if (doctype.internalSubset) {\n                str += doctype.internalSubset;\n            }\n            if (doctype.publicId) {\n                str += \"\\\"\" + doctype.publicId + \"\\\"\";\n            }\n            if (doctype.systemId) {\n                str += \"\\\"\" + doctype.systemId + \"\\\"\";\n            }\n            str += '>';\n        }\n        return str;\n    };\n    var restoreOwnerScroll = function (ownerDocument, x, y) {\n        if (ownerDocument &&\n            ownerDocument.defaultView &&\n            (x !== ownerDocument.defaultView.pageXOffset || y !== ownerDocument.defaultView.pageYOffset)) {\n            ownerDocument.defaultView.scrollTo(x, y);\n        }\n    };\n    var restoreNodeScroll = function (_a) {\n        var element = _a[0], x = _a[1], y = _a[2];\n        element.scrollLeft = x;\n        element.scrollTop = y;\n    };\n    var PSEUDO_BEFORE = ':before';\n    var PSEUDO_AFTER = ':after';\n    var PSEUDO_HIDE_ELEMENT_CLASS_BEFORE = '___html2canvas___pseudoelement_before';\n    var PSEUDO_HIDE_ELEMENT_CLASS_AFTER = '___html2canvas___pseudoelement_after';\n    var PSEUDO_HIDE_ELEMENT_STYLE = \"{\\n    content: \\\"\\\" !important;\\n    display: none !important;\\n}\";\n    var createPseudoHideStyles = function (body) {\n        createStyles(body, \".\" + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + PSEUDO_BEFORE + PSEUDO_HIDE_ELEMENT_STYLE + \"\\n         .\" + PSEUDO_HIDE_ELEMENT_CLASS_AFTER + PSEUDO_AFTER + PSEUDO_HIDE_ELEMENT_STYLE);\n    };\n    var createStyles = function (body, styles) {\n        var document = body.ownerDocument;\n        if (document) {\n            var style = document.createElement('style');\n            style.textContent = styles;\n            body.appendChild(style);\n        }\n    };\n\n    var CacheStorage = /** @class */ (function () {\n        function CacheStorage() {\n        }\n        CacheStorage.getOrigin = function (url) {\n            var link = CacheStorage._link;\n            if (!link) {\n                return 'about:blank';\n            }\n            link.href = url;\n            link.href = link.href; // IE9, LOL! - http://jsfiddle.net/niklasvh/2e48b/\n            return link.protocol + link.hostname + link.port;\n        };\n        CacheStorage.isSameOrigin = function (src) {\n            return CacheStorage.getOrigin(src) === CacheStorage._origin;\n        };\n        CacheStorage.setContext = function (window) {\n            CacheStorage._link = window.document.createElement('a');\n            CacheStorage._origin = CacheStorage.getOrigin(window.location.href);\n        };\n        CacheStorage._origin = 'about:blank';\n        return CacheStorage;\n    }());\n    var Cache = /** @class */ (function () {\n        function Cache(context, _options) {\n            this.context = context;\n            this._options = _options;\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._cache = {};\n        }\n        Cache.prototype.addImage = function (src) {\n            var result = Promise.resolve();\n            if (this.has(src)) {\n                return result;\n            }\n            if (isBlobImage(src) || isRenderable(src)) {\n                (this._cache[src] = this.loadImage(src)).catch(function () {\n                    // prevent unhandled rejection\n                });\n                return result;\n            }\n            return result;\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Cache.prototype.match = function (src) {\n            return this._cache[src];\n        };\n        Cache.prototype.loadImage = function (key) {\n            return __awaiter(this, void 0, void 0, function () {\n                var isSameOrigin, useCORS, useProxy, src;\n                var _this = this;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            isSameOrigin = CacheStorage.isSameOrigin(key);\n                            useCORS = !isInlineImage(key) && this._options.useCORS === true && FEATURES.SUPPORT_CORS_IMAGES && !isSameOrigin;\n                            useProxy = !isInlineImage(key) &&\n                                !isSameOrigin &&\n                                !isBlobImage(key) &&\n                                typeof this._options.proxy === 'string' &&\n                                FEATURES.SUPPORT_CORS_XHR &&\n                                !useCORS;\n                            if (!isSameOrigin &&\n                                this._options.allowTaint === false &&\n                                !isInlineImage(key) &&\n                                !isBlobImage(key) &&\n                                !useProxy &&\n                                !useCORS) {\n                                return [2 /*return*/];\n                            }\n                            src = key;\n                            if (!useProxy) return [3 /*break*/, 2];\n                            return [4 /*yield*/, this.proxy(src)];\n                        case 1:\n                            src = _a.sent();\n                            _a.label = 2;\n                        case 2:\n                            this.context.logger.debug(\"Added image \" + key.substring(0, 256));\n                            return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                    var img = new Image();\n                                    img.onload = function () { return resolve(img); };\n                                    img.onerror = reject;\n                                    //ios safari 10.3 taints canvas with data urls unless crossOrigin is set to anonymous\n                                    if (isInlineBase64Image(src) || useCORS) {\n                                        img.crossOrigin = 'anonymous';\n                                    }\n                                    img.src = src;\n                                    if (img.complete === true) {\n                                        // Inline XML images may fail to parse, throwing an Error later on\n                                        setTimeout(function () { return resolve(img); }, 500);\n                                    }\n                                    if (_this._options.imageTimeout > 0) {\n                                        setTimeout(function () { return reject(\"Timed out (\" + _this._options.imageTimeout + \"ms) loading image\"); }, _this._options.imageTimeout);\n                                    }\n                                })];\n                        case 3: return [2 /*return*/, _a.sent()];\n                    }\n                });\n            });\n        };\n        Cache.prototype.has = function (key) {\n            return typeof this._cache[key] !== 'undefined';\n        };\n        Cache.prototype.keys = function () {\n            return Promise.resolve(Object.keys(this._cache));\n        };\n        Cache.prototype.proxy = function (src) {\n            var _this = this;\n            var proxy = this._options.proxy;\n            if (!proxy) {\n                throw new Error('No proxy defined');\n            }\n            var key = src.substring(0, 256);\n            return new Promise(function (resolve, reject) {\n                var responseType = FEATURES.SUPPORT_RESPONSE_TYPE ? 'blob' : 'text';\n                var xhr = new XMLHttpRequest();\n                xhr.onload = function () {\n                    if (xhr.status === 200) {\n                        if (responseType === 'text') {\n                            resolve(xhr.response);\n                        }\n                        else {\n                            var reader_1 = new FileReader();\n                            reader_1.addEventListener('load', function () { return resolve(reader_1.result); }, false);\n                            reader_1.addEventListener('error', function (e) { return reject(e); }, false);\n                            reader_1.readAsDataURL(xhr.response);\n                        }\n                    }\n                    else {\n                        reject(\"Failed to proxy resource \" + key + \" with status code \" + xhr.status);\n                    }\n                };\n                xhr.onerror = reject;\n                var queryString = proxy.indexOf('?') > -1 ? '&' : '?';\n                xhr.open('GET', \"\" + proxy + queryString + \"url=\" + encodeURIComponent(src) + \"&responseType=\" + responseType);\n                if (responseType !== 'text' && xhr instanceof XMLHttpRequest) {\n                    xhr.responseType = responseType;\n                }\n                if (_this._options.imageTimeout) {\n                    var timeout_1 = _this._options.imageTimeout;\n                    xhr.timeout = timeout_1;\n                    xhr.ontimeout = function () { return reject(\"Timed out (\" + timeout_1 + \"ms) proxying \" + key); };\n                }\n                xhr.send();\n            });\n        };\n        return Cache;\n    }());\n    var INLINE_SVG = /^data:image\\/svg\\+xml/i;\n    var INLINE_BASE64 = /^data:image\\/.*;base64,/i;\n    var INLINE_IMG = /^data:image\\/.*/i;\n    var isRenderable = function (src) { return FEATURES.SUPPORT_SVG_DRAWING || !isSVG(src); };\n    var isInlineImage = function (src) { return INLINE_IMG.test(src); };\n    var isInlineBase64Image = function (src) { return INLINE_BASE64.test(src); };\n    var isBlobImage = function (src) { return src.substr(0, 4) === 'blob'; };\n    var isSVG = function (src) { return src.substr(-3).toLowerCase() === 'svg' || INLINE_SVG.test(src); };\n\n    var Vector = /** @class */ (function () {\n        function Vector(x, y) {\n            this.type = 0 /* VECTOR */;\n            this.x = x;\n            this.y = y;\n        }\n        Vector.prototype.add = function (deltaX, deltaY) {\n            return new Vector(this.x + deltaX, this.y + deltaY);\n        };\n        return Vector;\n    }());\n\n    var lerp = function (a, b, t) {\n        return new Vector(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);\n    };\n    var BezierCurve = /** @class */ (function () {\n        function BezierCurve(start, startControl, endControl, end) {\n            this.type = 1 /* BEZIER_CURVE */;\n            this.start = start;\n            this.startControl = startControl;\n            this.endControl = endControl;\n            this.end = end;\n        }\n        BezierCurve.prototype.subdivide = function (t, firstHalf) {\n            var ab = lerp(this.start, this.startControl, t);\n            var bc = lerp(this.startControl, this.endControl, t);\n            var cd = lerp(this.endControl, this.end, t);\n            var abbc = lerp(ab, bc, t);\n            var bccd = lerp(bc, cd, t);\n            var dest = lerp(abbc, bccd, t);\n            return firstHalf ? new BezierCurve(this.start, ab, abbc, dest) : new BezierCurve(dest, bccd, cd, this.end);\n        };\n        BezierCurve.prototype.add = function (deltaX, deltaY) {\n            return new BezierCurve(this.start.add(deltaX, deltaY), this.startControl.add(deltaX, deltaY), this.endControl.add(deltaX, deltaY), this.end.add(deltaX, deltaY));\n        };\n        BezierCurve.prototype.reverse = function () {\n            return new BezierCurve(this.end, this.endControl, this.startControl, this.start);\n        };\n        return BezierCurve;\n    }());\n    var isBezierCurve = function (path) { return path.type === 1 /* BEZIER_CURVE */; };\n\n    var BoundCurves = /** @class */ (function () {\n        function BoundCurves(element) {\n            var styles = element.styles;\n            var bounds = element.bounds;\n            var _a = getAbsoluteValueForTuple(styles.borderTopLeftRadius, bounds.width, bounds.height), tlh = _a[0], tlv = _a[1];\n            var _b = getAbsoluteValueForTuple(styles.borderTopRightRadius, bounds.width, bounds.height), trh = _b[0], trv = _b[1];\n            var _c = getAbsoluteValueForTuple(styles.borderBottomRightRadius, bounds.width, bounds.height), brh = _c[0], brv = _c[1];\n            var _d = getAbsoluteValueForTuple(styles.borderBottomLeftRadius, bounds.width, bounds.height), blh = _d[0], blv = _d[1];\n            var factors = [];\n            factors.push((tlh + trh) / bounds.width);\n            factors.push((blh + brh) / bounds.width);\n            factors.push((tlv + blv) / bounds.height);\n            factors.push((trv + brv) / bounds.height);\n            var maxFactor = Math.max.apply(Math, factors);\n            if (maxFactor > 1) {\n                tlh /= maxFactor;\n                tlv /= maxFactor;\n                trh /= maxFactor;\n                trv /= maxFactor;\n                brh /= maxFactor;\n                brv /= maxFactor;\n                blh /= maxFactor;\n                blv /= maxFactor;\n            }\n            var topWidth = bounds.width - trh;\n            var rightHeight = bounds.height - brv;\n            var bottomWidth = bounds.width - brh;\n            var leftHeight = bounds.height - blv;\n            var borderTopWidth = styles.borderTopWidth;\n            var borderRightWidth = styles.borderRightWidth;\n            var borderBottomWidth = styles.borderBottomWidth;\n            var borderLeftWidth = styles.borderLeftWidth;\n            var paddingTop = getAbsoluteValue(styles.paddingTop, element.bounds.width);\n            var paddingRight = getAbsoluteValue(styles.paddingRight, element.bounds.width);\n            var paddingBottom = getAbsoluteValue(styles.paddingBottom, element.bounds.width);\n            var paddingLeft = getAbsoluteValue(styles.paddingLeft, element.bounds.width);\n            this.topLeftBorderDoubleOuterBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth / 3, bounds.top + borderTopWidth / 3, tlh - borderLeftWidth / 3, tlv - borderTopWidth / 3, CORNER.TOP_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth / 3, bounds.top + borderTopWidth / 3);\n            this.topRightBorderDoubleOuterBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + topWidth, bounds.top + borderTopWidth / 3, trh - borderRightWidth / 3, trv - borderTopWidth / 3, CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth / 3, bounds.top + borderTopWidth / 3);\n            this.bottomRightBorderDoubleOuterBox =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh - borderRightWidth / 3, brv - borderBottomWidth / 3, CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth / 3, bounds.top + bounds.height - borderBottomWidth / 3);\n            this.bottomLeftBorderDoubleOuterBox =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth / 3, bounds.top + leftHeight, blh - borderLeftWidth / 3, blv - borderBottomWidth / 3, CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth / 3, bounds.top + bounds.height - borderBottomWidth / 3);\n            this.topLeftBorderDoubleInnerBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + (borderLeftWidth * 2) / 3, bounds.top + (borderTopWidth * 2) / 3, tlh - (borderLeftWidth * 2) / 3, tlv - (borderTopWidth * 2) / 3, CORNER.TOP_LEFT)\n                    : new Vector(bounds.left + (borderLeftWidth * 2) / 3, bounds.top + (borderTopWidth * 2) / 3);\n            this.topRightBorderDoubleInnerBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + topWidth, bounds.top + (borderTopWidth * 2) / 3, trh - (borderRightWidth * 2) / 3, trv - (borderTopWidth * 2) / 3, CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width - (borderRightWidth * 2) / 3, bounds.top + (borderTopWidth * 2) / 3);\n            this.bottomRightBorderDoubleInnerBox =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh - (borderRightWidth * 2) / 3, brv - (borderBottomWidth * 2) / 3, CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width - (borderRightWidth * 2) / 3, bounds.top + bounds.height - (borderBottomWidth * 2) / 3);\n            this.bottomLeftBorderDoubleInnerBox =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left + (borderLeftWidth * 2) / 3, bounds.top + leftHeight, blh - (borderLeftWidth * 2) / 3, blv - (borderBottomWidth * 2) / 3, CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left + (borderLeftWidth * 2) / 3, bounds.top + bounds.height - (borderBottomWidth * 2) / 3);\n            this.topLeftBorderStroke =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth / 2, bounds.top + borderTopWidth / 2, tlh - borderLeftWidth / 2, tlv - borderTopWidth / 2, CORNER.TOP_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth / 2, bounds.top + borderTopWidth / 2);\n            this.topRightBorderStroke =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + topWidth, bounds.top + borderTopWidth / 2, trh - borderRightWidth / 2, trv - borderTopWidth / 2, CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth / 2, bounds.top + borderTopWidth / 2);\n            this.bottomRightBorderStroke =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh - borderRightWidth / 2, brv - borderBottomWidth / 2, CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth / 2, bounds.top + bounds.height - borderBottomWidth / 2);\n            this.bottomLeftBorderStroke =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth / 2, bounds.top + leftHeight, blh - borderLeftWidth / 2, blv - borderBottomWidth / 2, CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth / 2, bounds.top + bounds.height - borderBottomWidth / 2);\n            this.topLeftBorderBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left, bounds.top, tlh, tlv, CORNER.TOP_LEFT)\n                    : new Vector(bounds.left, bounds.top);\n            this.topRightBorderBox =\n                trh > 0 || trv > 0\n                    ? getCurvePoints(bounds.left + topWidth, bounds.top, trh, trv, CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width, bounds.top);\n            this.bottomRightBorderBox =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh, brv, CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width, bounds.top + bounds.height);\n            this.bottomLeftBorderBox =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left, bounds.top + leftHeight, blh, blv, CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left, bounds.top + bounds.height);\n            this.topLeftPaddingBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth, bounds.top + borderTopWidth, Math.max(0, tlh - borderLeftWidth), Math.max(0, tlv - borderTopWidth), CORNER.TOP_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth, bounds.top + borderTopWidth);\n            this.topRightPaddingBox =\n                trh > 0 || trv > 0\n                    ? getCurvePoints(bounds.left + Math.min(topWidth, bounds.width - borderRightWidth), bounds.top + borderTopWidth, topWidth > bounds.width + borderRightWidth ? 0 : Math.max(0, trh - borderRightWidth), Math.max(0, trv - borderTopWidth), CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth, bounds.top + borderTopWidth);\n            this.bottomRightPaddingBox =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + Math.min(bottomWidth, bounds.width - borderLeftWidth), bounds.top + Math.min(rightHeight, bounds.height - borderBottomWidth), Math.max(0, brh - borderRightWidth), Math.max(0, brv - borderBottomWidth), CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width - borderRightWidth, bounds.top + bounds.height - borderBottomWidth);\n            this.bottomLeftPaddingBox =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth, bounds.top + Math.min(leftHeight, bounds.height - borderBottomWidth), Math.max(0, blh - borderLeftWidth), Math.max(0, blv - borderBottomWidth), CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth, bounds.top + bounds.height - borderBottomWidth);\n            this.topLeftContentBox =\n                tlh > 0 || tlv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth + paddingLeft, bounds.top + borderTopWidth + paddingTop, Math.max(0, tlh - (borderLeftWidth + paddingLeft)), Math.max(0, tlv - (borderTopWidth + paddingTop)), CORNER.TOP_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth + paddingLeft, bounds.top + borderTopWidth + paddingTop);\n            this.topRightContentBox =\n                trh > 0 || trv > 0\n                    ? getCurvePoints(bounds.left + Math.min(topWidth, bounds.width + borderLeftWidth + paddingLeft), bounds.top + borderTopWidth + paddingTop, topWidth > bounds.width + borderLeftWidth + paddingLeft ? 0 : trh - borderLeftWidth + paddingLeft, trv - (borderTopWidth + paddingTop), CORNER.TOP_RIGHT)\n                    : new Vector(bounds.left + bounds.width - (borderRightWidth + paddingRight), bounds.top + borderTopWidth + paddingTop);\n            this.bottomRightContentBox =\n                brh > 0 || brv > 0\n                    ? getCurvePoints(bounds.left + Math.min(bottomWidth, bounds.width - (borderLeftWidth + paddingLeft)), bounds.top + Math.min(rightHeight, bounds.height + borderTopWidth + paddingTop), Math.max(0, brh - (borderRightWidth + paddingRight)), brv - (borderBottomWidth + paddingBottom), CORNER.BOTTOM_RIGHT)\n                    : new Vector(bounds.left + bounds.width - (borderRightWidth + paddingRight), bounds.top + bounds.height - (borderBottomWidth + paddingBottom));\n            this.bottomLeftContentBox =\n                blh > 0 || blv > 0\n                    ? getCurvePoints(bounds.left + borderLeftWidth + paddingLeft, bounds.top + leftHeight, Math.max(0, blh - (borderLeftWidth + paddingLeft)), blv - (borderBottomWidth + paddingBottom), CORNER.BOTTOM_LEFT)\n                    : new Vector(bounds.left + borderLeftWidth + paddingLeft, bounds.top + bounds.height - (borderBottomWidth + paddingBottom));\n        }\n        return BoundCurves;\n    }());\n    var CORNER;\n    (function (CORNER) {\n        CORNER[CORNER[\"TOP_LEFT\"] = 0] = \"TOP_LEFT\";\n        CORNER[CORNER[\"TOP_RIGHT\"] = 1] = \"TOP_RIGHT\";\n        CORNER[CORNER[\"BOTTOM_RIGHT\"] = 2] = \"BOTTOM_RIGHT\";\n        CORNER[CORNER[\"BOTTOM_LEFT\"] = 3] = \"BOTTOM_LEFT\";\n    })(CORNER || (CORNER = {}));\n    var getCurvePoints = function (x, y, r1, r2, position) {\n        var kappa = 4 * ((Math.sqrt(2) - 1) / 3);\n        var ox = r1 * kappa; // control point offset horizontal\n        var oy = r2 * kappa; // control point offset vertical\n        var xm = x + r1; // x-middle\n        var ym = y + r2; // y-middle\n        switch (position) {\n            case CORNER.TOP_LEFT:\n                return new BezierCurve(new Vector(x, ym), new Vector(x, ym - oy), new Vector(xm - ox, y), new Vector(xm, y));\n            case CORNER.TOP_RIGHT:\n                return new BezierCurve(new Vector(x, y), new Vector(x + ox, y), new Vector(xm, ym - oy), new Vector(xm, ym));\n            case CORNER.BOTTOM_RIGHT:\n                return new BezierCurve(new Vector(xm, y), new Vector(xm, y + oy), new Vector(x + ox, ym), new Vector(x, ym));\n            case CORNER.BOTTOM_LEFT:\n            default:\n                return new BezierCurve(new Vector(xm, ym), new Vector(xm - ox, ym), new Vector(x, y + oy), new Vector(x, y));\n        }\n    };\n    var calculateBorderBoxPath = function (curves) {\n        return [curves.topLeftBorderBox, curves.topRightBorderBox, curves.bottomRightBorderBox, curves.bottomLeftBorderBox];\n    };\n    var calculateContentBoxPath = function (curves) {\n        return [\n            curves.topLeftContentBox,\n            curves.topRightContentBox,\n            curves.bottomRightContentBox,\n            curves.bottomLeftContentBox\n        ];\n    };\n    var calculatePaddingBoxPath = function (curves) {\n        return [\n            curves.topLeftPaddingBox,\n            curves.topRightPaddingBox,\n            curves.bottomRightPaddingBox,\n            curves.bottomLeftPaddingBox\n        ];\n    };\n\n    var TransformEffect = /** @class */ (function () {\n        function TransformEffect(offsetX, offsetY, matrix) {\n            this.offsetX = offsetX;\n            this.offsetY = offsetY;\n            this.matrix = matrix;\n            this.type = 0 /* TRANSFORM */;\n            this.target = 2 /* BACKGROUND_BORDERS */ | 4 /* CONTENT */;\n        }\n        return TransformEffect;\n    }());\n    var ClipEffect = /** @class */ (function () {\n        function ClipEffect(path, target) {\n            this.path = path;\n            this.target = target;\n            this.type = 1 /* CLIP */;\n        }\n        return ClipEffect;\n    }());\n    var OpacityEffect = /** @class */ (function () {\n        function OpacityEffect(opacity) {\n            this.opacity = opacity;\n            this.type = 2 /* OPACITY */;\n            this.target = 2 /* BACKGROUND_BORDERS */ | 4 /* CONTENT */;\n        }\n        return OpacityEffect;\n    }());\n    var isTransformEffect = function (effect) {\n        return effect.type === 0 /* TRANSFORM */;\n    };\n    var isClipEffect = function (effect) { return effect.type === 1 /* CLIP */; };\n    var isOpacityEffect = function (effect) { return effect.type === 2 /* OPACITY */; };\n\n    var equalPath = function (a, b) {\n        if (a.length === b.length) {\n            return a.some(function (v, i) { return v === b[i]; });\n        }\n        return false;\n    };\n    var transformPath = function (path, deltaX, deltaY, deltaW, deltaH) {\n        return path.map(function (point, index) {\n            switch (index) {\n                case 0:\n                    return point.add(deltaX, deltaY);\n                case 1:\n                    return point.add(deltaX + deltaW, deltaY);\n                case 2:\n                    return point.add(deltaX + deltaW, deltaY + deltaH);\n                case 3:\n                    return point.add(deltaX, deltaY + deltaH);\n            }\n            return point;\n        });\n    };\n\n    var StackingContext = /** @class */ (function () {\n        function StackingContext(container) {\n            this.element = container;\n            this.inlineLevel = [];\n            this.nonInlineLevel = [];\n            this.negativeZIndex = [];\n            this.zeroOrAutoZIndexOrTransformedOrOpacity = [];\n            this.positiveZIndex = [];\n            this.nonPositionedFloats = [];\n            this.nonPositionedInlineLevel = [];\n        }\n        return StackingContext;\n    }());\n    var ElementPaint = /** @class */ (function () {\n        function ElementPaint(container, parent) {\n            this.container = container;\n            this.parent = parent;\n            this.effects = [];\n            this.curves = new BoundCurves(this.container);\n            if (this.container.styles.opacity < 1) {\n                this.effects.push(new OpacityEffect(this.container.styles.opacity));\n            }\n            if (this.container.styles.transform !== null) {\n                var offsetX = this.container.bounds.left + this.container.styles.transformOrigin[0].number;\n                var offsetY = this.container.bounds.top + this.container.styles.transformOrigin[1].number;\n                var matrix = this.container.styles.transform;\n                this.effects.push(new TransformEffect(offsetX, offsetY, matrix));\n            }\n            if (this.container.styles.overflowX !== 0 /* VISIBLE */) {\n                var borderBox = calculateBorderBoxPath(this.curves);\n                var paddingBox = calculatePaddingBoxPath(this.curves);\n                if (equalPath(borderBox, paddingBox)) {\n                    this.effects.push(new ClipEffect(borderBox, 2 /* BACKGROUND_BORDERS */ | 4 /* CONTENT */));\n                }\n                else {\n                    this.effects.push(new ClipEffect(borderBox, 2 /* BACKGROUND_BORDERS */));\n                    this.effects.push(new ClipEffect(paddingBox, 4 /* CONTENT */));\n                }\n            }\n        }\n        ElementPaint.prototype.getEffects = function (target) {\n            var inFlow = [2 /* ABSOLUTE */, 3 /* FIXED */].indexOf(this.container.styles.position) === -1;\n            var parent = this.parent;\n            var effects = this.effects.slice(0);\n            while (parent) {\n                var croplessEffects = parent.effects.filter(function (effect) { return !isClipEffect(effect); });\n                if (inFlow || parent.container.styles.position !== 0 /* STATIC */ || !parent.parent) {\n                    effects.unshift.apply(effects, croplessEffects);\n                    inFlow = [2 /* ABSOLUTE */, 3 /* FIXED */].indexOf(parent.container.styles.position) === -1;\n                    if (parent.container.styles.overflowX !== 0 /* VISIBLE */) {\n                        var borderBox = calculateBorderBoxPath(parent.curves);\n                        var paddingBox = calculatePaddingBoxPath(parent.curves);\n                        if (!equalPath(borderBox, paddingBox)) {\n                            effects.unshift(new ClipEffect(paddingBox, 2 /* BACKGROUND_BORDERS */ | 4 /* CONTENT */));\n                        }\n                    }\n                }\n                else {\n                    effects.unshift.apply(effects, croplessEffects);\n                }\n                parent = parent.parent;\n            }\n            return effects.filter(function (effect) { return contains(effect.target, target); });\n        };\n        return ElementPaint;\n    }());\n    var parseStackTree = function (parent, stackingContext, realStackingContext, listItems) {\n        parent.container.elements.forEach(function (child) {\n            var treatAsRealStackingContext = contains(child.flags, 4 /* CREATES_REAL_STACKING_CONTEXT */);\n            var createsStackingContext = contains(child.flags, 2 /* CREATES_STACKING_CONTEXT */);\n            var paintContainer = new ElementPaint(child, parent);\n            if (contains(child.styles.display, 2048 /* LIST_ITEM */)) {\n                listItems.push(paintContainer);\n            }\n            var listOwnerItems = contains(child.flags, 8 /* IS_LIST_OWNER */) ? [] : listItems;\n            if (treatAsRealStackingContext || createsStackingContext) {\n                var parentStack = treatAsRealStackingContext || child.styles.isPositioned() ? realStackingContext : stackingContext;\n                var stack = new StackingContext(paintContainer);\n                if (child.styles.isPositioned() || child.styles.opacity < 1 || child.styles.isTransformed()) {\n                    var order_1 = child.styles.zIndex.order;\n                    if (order_1 < 0) {\n                        var index_1 = 0;\n                        parentStack.negativeZIndex.some(function (current, i) {\n                            if (order_1 > current.element.container.styles.zIndex.order) {\n                                index_1 = i;\n                                return false;\n                            }\n                            else if (index_1 > 0) {\n                                return true;\n                            }\n                            return false;\n                        });\n                        parentStack.negativeZIndex.splice(index_1, 0, stack);\n                    }\n                    else if (order_1 > 0) {\n                        var index_2 = 0;\n                        parentStack.positiveZIndex.some(function (current, i) {\n                            if (order_1 >= current.element.container.styles.zIndex.order) {\n                                index_2 = i + 1;\n                                return false;\n                            }\n                            else if (index_2 > 0) {\n                                return true;\n                            }\n                            return false;\n                        });\n                        parentStack.positiveZIndex.splice(index_2, 0, stack);\n                    }\n                    else {\n                        parentStack.zeroOrAutoZIndexOrTransformedOrOpacity.push(stack);\n                    }\n                }\n                else {\n                    if (child.styles.isFloating()) {\n                        parentStack.nonPositionedFloats.push(stack);\n                    }\n                    else {\n                        parentStack.nonPositionedInlineLevel.push(stack);\n                    }\n                }\n                parseStackTree(paintContainer, stack, treatAsRealStackingContext ? stack : realStackingContext, listOwnerItems);\n            }\n            else {\n                if (child.styles.isInlineLevel()) {\n                    stackingContext.inlineLevel.push(paintContainer);\n                }\n                else {\n                    stackingContext.nonInlineLevel.push(paintContainer);\n                }\n                parseStackTree(paintContainer, stackingContext, realStackingContext, listOwnerItems);\n            }\n            if (contains(child.flags, 8 /* IS_LIST_OWNER */)) {\n                processListItems(child, listOwnerItems);\n            }\n        });\n    };\n    var processListItems = function (owner, elements) {\n        var numbering = owner instanceof OLElementContainer ? owner.start : 1;\n        var reversed = owner instanceof OLElementContainer ? owner.reversed : false;\n        for (var i = 0; i < elements.length; i++) {\n            var item = elements[i];\n            if (item.container instanceof LIElementContainer &&\n                typeof item.container.value === 'number' &&\n                item.container.value !== 0) {\n                numbering = item.container.value;\n            }\n            item.listValue = createCounterText(numbering, item.container.styles.listStyleType, true);\n            numbering += reversed ? -1 : 1;\n        }\n    };\n    var parseStackingContexts = function (container) {\n        var paintContainer = new ElementPaint(container, null);\n        var root = new StackingContext(paintContainer);\n        var listItems = [];\n        parseStackTree(paintContainer, root, root, listItems);\n        processListItems(paintContainer.container, listItems);\n        return root;\n    };\n\n    var parsePathForBorder = function (curves, borderSide) {\n        switch (borderSide) {\n            case 0:\n                return createPathFromCurves(curves.topLeftBorderBox, curves.topLeftPaddingBox, curves.topRightBorderBox, curves.topRightPaddingBox);\n            case 1:\n                return createPathFromCurves(curves.topRightBorderBox, curves.topRightPaddingBox, curves.bottomRightBorderBox, curves.bottomRightPaddingBox);\n            case 2:\n                return createPathFromCurves(curves.bottomRightBorderBox, curves.bottomRightPaddingBox, curves.bottomLeftBorderBox, curves.bottomLeftPaddingBox);\n            case 3:\n            default:\n                return createPathFromCurves(curves.bottomLeftBorderBox, curves.bottomLeftPaddingBox, curves.topLeftBorderBox, curves.topLeftPaddingBox);\n        }\n    };\n    var parsePathForBorderDoubleOuter = function (curves, borderSide) {\n        switch (borderSide) {\n            case 0:\n                return createPathFromCurves(curves.topLeftBorderBox, curves.topLeftBorderDoubleOuterBox, curves.topRightBorderBox, curves.topRightBorderDoubleOuterBox);\n            case 1:\n                return createPathFromCurves(curves.topRightBorderBox, curves.topRightBorderDoubleOuterBox, curves.bottomRightBorderBox, curves.bottomRightBorderDoubleOuterBox);\n            case 2:\n                return createPathFromCurves(curves.bottomRightBorderBox, curves.bottomRightBorderDoubleOuterBox, curves.bottomLeftBorderBox, curves.bottomLeftBorderDoubleOuterBox);\n            case 3:\n            default:\n                return createPathFromCurves(curves.bottomLeftBorderBox, curves.bottomLeftBorderDoubleOuterBox, curves.topLeftBorderBox, curves.topLeftBorderDoubleOuterBox);\n        }\n    };\n    var parsePathForBorderDoubleInner = function (curves, borderSide) {\n        switch (borderSide) {\n            case 0:\n                return createPathFromCurves(curves.topLeftBorderDoubleInnerBox, curves.topLeftPaddingBox, curves.topRightBorderDoubleInnerBox, curves.topRightPaddingBox);\n            case 1:\n                return createPathFromCurves(curves.topRightBorderDoubleInnerBox, curves.topRightPaddingBox, curves.bottomRightBorderDoubleInnerBox, curves.bottomRightPaddingBox);\n            case 2:\n                return createPathFromCurves(curves.bottomRightBorderDoubleInnerBox, curves.bottomRightPaddingBox, curves.bottomLeftBorderDoubleInnerBox, curves.bottomLeftPaddingBox);\n            case 3:\n            default:\n                return createPathFromCurves(curves.bottomLeftBorderDoubleInnerBox, curves.bottomLeftPaddingBox, curves.topLeftBorderDoubleInnerBox, curves.topLeftPaddingBox);\n        }\n    };\n    var parsePathForBorderStroke = function (curves, borderSide) {\n        switch (borderSide) {\n            case 0:\n                return createStrokePathFromCurves(curves.topLeftBorderStroke, curves.topRightBorderStroke);\n            case 1:\n                return createStrokePathFromCurves(curves.topRightBorderStroke, curves.bottomRightBorderStroke);\n            case 2:\n                return createStrokePathFromCurves(curves.bottomRightBorderStroke, curves.bottomLeftBorderStroke);\n            case 3:\n            default:\n                return createStrokePathFromCurves(curves.bottomLeftBorderStroke, curves.topLeftBorderStroke);\n        }\n    };\n    var createStrokePathFromCurves = function (outer1, outer2) {\n        var path = [];\n        if (isBezierCurve(outer1)) {\n            path.push(outer1.subdivide(0.5, false));\n        }\n        else {\n            path.push(outer1);\n        }\n        if (isBezierCurve(outer2)) {\n            path.push(outer2.subdivide(0.5, true));\n        }\n        else {\n            path.push(outer2);\n        }\n        return path;\n    };\n    var createPathFromCurves = function (outer1, inner1, outer2, inner2) {\n        var path = [];\n        if (isBezierCurve(outer1)) {\n            path.push(outer1.subdivide(0.5, false));\n        }\n        else {\n            path.push(outer1);\n        }\n        if (isBezierCurve(outer2)) {\n            path.push(outer2.subdivide(0.5, true));\n        }\n        else {\n            path.push(outer2);\n        }\n        if (isBezierCurve(inner2)) {\n            path.push(inner2.subdivide(0.5, true).reverse());\n        }\n        else {\n            path.push(inner2);\n        }\n        if (isBezierCurve(inner1)) {\n            path.push(inner1.subdivide(0.5, false).reverse());\n        }\n        else {\n            path.push(inner1);\n        }\n        return path;\n    };\n\n    var paddingBox = function (element) {\n        var bounds = element.bounds;\n        var styles = element.styles;\n        return bounds.add(styles.borderLeftWidth, styles.borderTopWidth, -(styles.borderRightWidth + styles.borderLeftWidth), -(styles.borderTopWidth + styles.borderBottomWidth));\n    };\n    var contentBox = function (element) {\n        var styles = element.styles;\n        var bounds = element.bounds;\n        var paddingLeft = getAbsoluteValue(styles.paddingLeft, bounds.width);\n        var paddingRight = getAbsoluteValue(styles.paddingRight, bounds.width);\n        var paddingTop = getAbsoluteValue(styles.paddingTop, bounds.width);\n        var paddingBottom = getAbsoluteValue(styles.paddingBottom, bounds.width);\n        return bounds.add(paddingLeft + styles.borderLeftWidth, paddingTop + styles.borderTopWidth, -(styles.borderRightWidth + styles.borderLeftWidth + paddingLeft + paddingRight), -(styles.borderTopWidth + styles.borderBottomWidth + paddingTop + paddingBottom));\n    };\n\n    var calculateBackgroundPositioningArea = function (backgroundOrigin, element) {\n        if (backgroundOrigin === 0 /* BORDER_BOX */) {\n            return element.bounds;\n        }\n        if (backgroundOrigin === 2 /* CONTENT_BOX */) {\n            return contentBox(element);\n        }\n        return paddingBox(element);\n    };\n    var calculateBackgroundPaintingArea = function (backgroundClip, element) {\n        if (backgroundClip === 0 /* BORDER_BOX */) {\n            return element.bounds;\n        }\n        if (backgroundClip === 2 /* CONTENT_BOX */) {\n            return contentBox(element);\n        }\n        return paddingBox(element);\n    };\n    var calculateBackgroundRendering = function (container, index, intrinsicSize) {\n        var backgroundPositioningArea = calculateBackgroundPositioningArea(getBackgroundValueForIndex(container.styles.backgroundOrigin, index), container);\n        var backgroundPaintingArea = calculateBackgroundPaintingArea(getBackgroundValueForIndex(container.styles.backgroundClip, index), container);\n        var backgroundImageSize = calculateBackgroundSize(getBackgroundValueForIndex(container.styles.backgroundSize, index), intrinsicSize, backgroundPositioningArea);\n        var sizeWidth = backgroundImageSize[0], sizeHeight = backgroundImageSize[1];\n        var position = getAbsoluteValueForTuple(getBackgroundValueForIndex(container.styles.backgroundPosition, index), backgroundPositioningArea.width - sizeWidth, backgroundPositioningArea.height - sizeHeight);\n        var path = calculateBackgroundRepeatPath(getBackgroundValueForIndex(container.styles.backgroundRepeat, index), position, backgroundImageSize, backgroundPositioningArea, backgroundPaintingArea);\n        var offsetX = Math.round(backgroundPositioningArea.left + position[0]);\n        var offsetY = Math.round(backgroundPositioningArea.top + position[1]);\n        return [path, offsetX, offsetY, sizeWidth, sizeHeight];\n    };\n    var isAuto = function (token) { return isIdentToken(token) && token.value === BACKGROUND_SIZE.AUTO; };\n    var hasIntrinsicValue = function (value) { return typeof value === 'number'; };\n    var calculateBackgroundSize = function (size, _a, bounds) {\n        var intrinsicWidth = _a[0], intrinsicHeight = _a[1], intrinsicProportion = _a[2];\n        var first = size[0], second = size[1];\n        if (!first) {\n            return [0, 0];\n        }\n        if (isLengthPercentage(first) && second && isLengthPercentage(second)) {\n            return [getAbsoluteValue(first, bounds.width), getAbsoluteValue(second, bounds.height)];\n        }\n        var hasIntrinsicProportion = hasIntrinsicValue(intrinsicProportion);\n        if (isIdentToken(first) && (first.value === BACKGROUND_SIZE.CONTAIN || first.value === BACKGROUND_SIZE.COVER)) {\n            if (hasIntrinsicValue(intrinsicProportion)) {\n                var targetRatio = bounds.width / bounds.height;\n                return targetRatio < intrinsicProportion !== (first.value === BACKGROUND_SIZE.COVER)\n                    ? [bounds.width, bounds.width / intrinsicProportion]\n                    : [bounds.height * intrinsicProportion, bounds.height];\n            }\n            return [bounds.width, bounds.height];\n        }\n        var hasIntrinsicWidth = hasIntrinsicValue(intrinsicWidth);\n        var hasIntrinsicHeight = hasIntrinsicValue(intrinsicHeight);\n        var hasIntrinsicDimensions = hasIntrinsicWidth || hasIntrinsicHeight;\n        // If the background-size is auto or auto auto:\n        if (isAuto(first) && (!second || isAuto(second))) {\n            // If the image has both horizontal and vertical intrinsic dimensions, it's rendered at that size.\n            if (hasIntrinsicWidth && hasIntrinsicHeight) {\n                return [intrinsicWidth, intrinsicHeight];\n            }\n            // If the image has no intrinsic dimensions and has no intrinsic proportions,\n            // it's rendered at the size of the background positioning area.\n            if (!hasIntrinsicProportion && !hasIntrinsicDimensions) {\n                return [bounds.width, bounds.height];\n            }\n            // TODO If the image has no intrinsic dimensions but has intrinsic proportions, it's rendered as if contain had been specified instead.\n            // If the image has only one intrinsic dimension and has intrinsic proportions, it's rendered at the size corresponding to that one dimension.\n            // The other dimension is computed using the specified dimension and the intrinsic proportions.\n            if (hasIntrinsicDimensions && hasIntrinsicProportion) {\n                var width_1 = hasIntrinsicWidth\n                    ? intrinsicWidth\n                    : intrinsicHeight * intrinsicProportion;\n                var height_1 = hasIntrinsicHeight\n                    ? intrinsicHeight\n                    : intrinsicWidth / intrinsicProportion;\n                return [width_1, height_1];\n            }\n            // If the image has only one intrinsic dimension but has no intrinsic proportions,\n            // it's rendered using the specified dimension and the other dimension of the background positioning area.\n            var width_2 = hasIntrinsicWidth ? intrinsicWidth : bounds.width;\n            var height_2 = hasIntrinsicHeight ? intrinsicHeight : bounds.height;\n            return [width_2, height_2];\n        }\n        // If the image has intrinsic proportions, it's stretched to the specified dimension.\n        // The unspecified dimension is computed using the specified dimension and the intrinsic proportions.\n        if (hasIntrinsicProportion) {\n            var width_3 = 0;\n            var height_3 = 0;\n            if (isLengthPercentage(first)) {\n                width_3 = getAbsoluteValue(first, bounds.width);\n            }\n            else if (isLengthPercentage(second)) {\n                height_3 = getAbsoluteValue(second, bounds.height);\n            }\n            if (isAuto(first)) {\n                width_3 = height_3 * intrinsicProportion;\n            }\n            else if (!second || isAuto(second)) {\n                height_3 = width_3 / intrinsicProportion;\n            }\n            return [width_3, height_3];\n        }\n        // If the image has no intrinsic proportions, it's stretched to the specified dimension.\n        // The unspecified dimension is computed using the image's corresponding intrinsic dimension,\n        // if there is one. If there is no such intrinsic dimension,\n        // it becomes the corresponding dimension of the background positioning area.\n        var width = null;\n        var height = null;\n        if (isLengthPercentage(first)) {\n            width = getAbsoluteValue(first, bounds.width);\n        }\n        else if (second && isLengthPercentage(second)) {\n            height = getAbsoluteValue(second, bounds.height);\n        }\n        if (width !== null && (!second || isAuto(second))) {\n            height =\n                hasIntrinsicWidth && hasIntrinsicHeight\n                    ? (width / intrinsicWidth) * intrinsicHeight\n                    : bounds.height;\n        }\n        if (height !== null && isAuto(first)) {\n            width =\n                hasIntrinsicWidth && hasIntrinsicHeight\n                    ? (height / intrinsicHeight) * intrinsicWidth\n                    : bounds.width;\n        }\n        if (width !== null && height !== null) {\n            return [width, height];\n        }\n        throw new Error(\"Unable to calculate background-size for element\");\n    };\n    var getBackgroundValueForIndex = function (values, index) {\n        var value = values[index];\n        if (typeof value === 'undefined') {\n            return values[0];\n        }\n        return value;\n    };\n    var calculateBackgroundRepeatPath = function (repeat, _a, _b, backgroundPositioningArea, backgroundPaintingArea) {\n        var x = _a[0], y = _a[1];\n        var width = _b[0], height = _b[1];\n        switch (repeat) {\n            case 2 /* REPEAT_X */:\n                return [\n                    new Vector(Math.round(backgroundPositioningArea.left), Math.round(backgroundPositioningArea.top + y)),\n                    new Vector(Math.round(backgroundPositioningArea.left + backgroundPositioningArea.width), Math.round(backgroundPositioningArea.top + y)),\n                    new Vector(Math.round(backgroundPositioningArea.left + backgroundPositioningArea.width), Math.round(height + backgroundPositioningArea.top + y)),\n                    new Vector(Math.round(backgroundPositioningArea.left), Math.round(height + backgroundPositioningArea.top + y))\n                ];\n            case 3 /* REPEAT_Y */:\n                return [\n                    new Vector(Math.round(backgroundPositioningArea.left + x), Math.round(backgroundPositioningArea.top)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x + width), Math.round(backgroundPositioningArea.top)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x + width), Math.round(backgroundPositioningArea.height + backgroundPositioningArea.top)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x), Math.round(backgroundPositioningArea.height + backgroundPositioningArea.top))\n                ];\n            case 1 /* NO_REPEAT */:\n                return [\n                    new Vector(Math.round(backgroundPositioningArea.left + x), Math.round(backgroundPositioningArea.top + y)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x + width), Math.round(backgroundPositioningArea.top + y)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x + width), Math.round(backgroundPositioningArea.top + y + height)),\n                    new Vector(Math.round(backgroundPositioningArea.left + x), Math.round(backgroundPositioningArea.top + y + height))\n                ];\n            default:\n                return [\n                    new Vector(Math.round(backgroundPaintingArea.left), Math.round(backgroundPaintingArea.top)),\n                    new Vector(Math.round(backgroundPaintingArea.left + backgroundPaintingArea.width), Math.round(backgroundPaintingArea.top)),\n                    new Vector(Math.round(backgroundPaintingArea.left + backgroundPaintingArea.width), Math.round(backgroundPaintingArea.height + backgroundPaintingArea.top)),\n                    new Vector(Math.round(backgroundPaintingArea.left), Math.round(backgroundPaintingArea.height + backgroundPaintingArea.top))\n                ];\n        }\n    };\n\n    var SMALL_IMAGE = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n\n    var SAMPLE_TEXT = 'Hidden Text';\n    var FontMetrics = /** @class */ (function () {\n        function FontMetrics(document) {\n            this._data = {};\n            this._document = document;\n        }\n        FontMetrics.prototype.parseMetrics = function (fontFamily, fontSize) {\n            var container = this._document.createElement('div');\n            var img = this._document.createElement('img');\n            var span = this._document.createElement('span');\n            var body = this._document.body;\n            container.style.visibility = 'hidden';\n            container.style.fontFamily = fontFamily;\n            container.style.fontSize = fontSize;\n            container.style.margin = '0';\n            container.style.padding = '0';\n            container.style.whiteSpace = 'nowrap';\n            body.appendChild(container);\n            img.src = SMALL_IMAGE;\n            img.width = 1;\n            img.height = 1;\n            img.style.margin = '0';\n            img.style.padding = '0';\n            img.style.verticalAlign = 'baseline';\n            span.style.fontFamily = fontFamily;\n            span.style.fontSize = fontSize;\n            span.style.margin = '0';\n            span.style.padding = '0';\n            span.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n            container.appendChild(span);\n            container.appendChild(img);\n            var baseline = img.offsetTop - span.offsetTop + 2;\n            container.removeChild(span);\n            container.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n            container.style.lineHeight = 'normal';\n            img.style.verticalAlign = 'super';\n            var middle = img.offsetTop - container.offsetTop + 2;\n            body.removeChild(container);\n            return { baseline: baseline, middle: middle };\n        };\n        FontMetrics.prototype.getMetrics = function (fontFamily, fontSize) {\n            var key = fontFamily + \" \" + fontSize;\n            if (typeof this._data[key] === 'undefined') {\n                this._data[key] = this.parseMetrics(fontFamily, fontSize);\n            }\n            return this._data[key];\n        };\n        return FontMetrics;\n    }());\n\n    var Renderer = /** @class */ (function () {\n        function Renderer(context, options) {\n            this.context = context;\n            this.options = options;\n        }\n        return Renderer;\n    }());\n\n    var MASK_OFFSET = 10000;\n    var CanvasRenderer = /** @class */ (function (_super) {\n        __extends(CanvasRenderer, _super);\n        function CanvasRenderer(context, options) {\n            var _this = _super.call(this, context, options) || this;\n            _this._activeEffects = [];\n            _this.canvas = options.canvas ? options.canvas : document.createElement('canvas');\n            _this.ctx = _this.canvas.getContext('2d');\n            if (!options.canvas) {\n                _this.canvas.width = Math.floor(options.width * options.scale);\n                _this.canvas.height = Math.floor(options.height * options.scale);\n                _this.canvas.style.width = options.width + \"px\";\n                _this.canvas.style.height = options.height + \"px\";\n            }\n            _this.fontMetrics = new FontMetrics(document);\n            _this.ctx.scale(_this.options.scale, _this.options.scale);\n            _this.ctx.translate(-options.x, -options.y);\n            _this.ctx.textBaseline = 'bottom';\n            _this._activeEffects = [];\n            _this.context.logger.debug(\"Canvas renderer initialized (\" + options.width + \"x\" + options.height + \") with scale \" + options.scale);\n            return _this;\n        }\n        CanvasRenderer.prototype.applyEffects = function (effects) {\n            var _this = this;\n            while (this._activeEffects.length) {\n                this.popEffect();\n            }\n            effects.forEach(function (effect) { return _this.applyEffect(effect); });\n        };\n        CanvasRenderer.prototype.applyEffect = function (effect) {\n            this.ctx.save();\n            if (isOpacityEffect(effect)) {\n                this.ctx.globalAlpha = effect.opacity;\n            }\n            if (isTransformEffect(effect)) {\n                this.ctx.translate(effect.offsetX, effect.offsetY);\n                this.ctx.transform(effect.matrix[0], effect.matrix[1], effect.matrix[2], effect.matrix[3], effect.matrix[4], effect.matrix[5]);\n                this.ctx.translate(-effect.offsetX, -effect.offsetY);\n            }\n            if (isClipEffect(effect)) {\n                this.path(effect.path);\n                this.ctx.clip();\n            }\n            this._activeEffects.push(effect);\n        };\n        CanvasRenderer.prototype.popEffect = function () {\n            this._activeEffects.pop();\n            this.ctx.restore();\n        };\n        CanvasRenderer.prototype.renderStack = function (stack) {\n            return __awaiter(this, void 0, void 0, function () {\n                var styles;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            styles = stack.element.container.styles;\n                            if (!styles.isVisible()) return [3 /*break*/, 2];\n                            return [4 /*yield*/, this.renderStackContent(stack)];\n                        case 1:\n                            _a.sent();\n                            _a.label = 2;\n                        case 2: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderNode = function (paint) {\n            return __awaiter(this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            if (contains(paint.container.flags, 16 /* DEBUG_RENDER */)) {\n                                debugger;\n                            }\n                            if (!paint.container.styles.isVisible()) return [3 /*break*/, 3];\n                            return [4 /*yield*/, this.renderNodeBackgroundAndBorders(paint)];\n                        case 1:\n                            _a.sent();\n                            return [4 /*yield*/, this.renderNodeContent(paint)];\n                        case 2:\n                            _a.sent();\n                            _a.label = 3;\n                        case 3: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderTextWithLetterSpacing = function (text, letterSpacing, baseline) {\n            var _this = this;\n            if (letterSpacing === 0) {\n                this.ctx.fillText(text.text, text.bounds.left, text.bounds.top + baseline);\n            }\n            else {\n                var letters = segmentGraphemes(text.text);\n                letters.reduce(function (left, letter) {\n                    _this.ctx.fillText(letter, left, text.bounds.top + baseline);\n                    return left + _this.ctx.measureText(letter).width;\n                }, text.bounds.left);\n            }\n        };\n        CanvasRenderer.prototype.createFontStyle = function (styles) {\n            var fontVariant = styles.fontVariant\n                .filter(function (variant) { return variant === 'normal' || variant === 'small-caps'; })\n                .join('');\n            var fontFamily = fixIOSSystemFonts(styles.fontFamily).join(', ');\n            var fontSize = isDimensionToken(styles.fontSize)\n                ? \"\" + styles.fontSize.number + styles.fontSize.unit\n                : styles.fontSize.number + \"px\";\n            return [\n                [styles.fontStyle, fontVariant, styles.fontWeight, fontSize, fontFamily].join(' '),\n                fontFamily,\n                fontSize\n            ];\n        };\n        CanvasRenderer.prototype.renderTextNode = function (text, styles) {\n            return __awaiter(this, void 0, void 0, function () {\n                var _a, font, fontFamily, fontSize, _b, baseline, middle, paintOrder;\n                var _this = this;\n                return __generator(this, function (_c) {\n                    _a = this.createFontStyle(styles), font = _a[0], fontFamily = _a[1], fontSize = _a[2];\n                    this.ctx.font = font;\n                    this.ctx.direction = styles.direction === 1 /* RTL */ ? 'rtl' : 'ltr';\n                    this.ctx.textAlign = 'left';\n                    this.ctx.textBaseline = 'alphabetic';\n                    _b = this.fontMetrics.getMetrics(fontFamily, fontSize), baseline = _b.baseline, middle = _b.middle;\n                    paintOrder = styles.paintOrder;\n                    text.textBounds.forEach(function (text) {\n                        paintOrder.forEach(function (paintOrderLayer) {\n                            switch (paintOrderLayer) {\n                                case 0 /* FILL */:\n                                    _this.ctx.fillStyle = asString(styles.color);\n                                    _this.renderTextWithLetterSpacing(text, styles.letterSpacing, baseline);\n                                    var textShadows = styles.textShadow;\n                                    if (textShadows.length && text.text.trim().length) {\n                                        textShadows\n                                            .slice(0)\n                                            .reverse()\n                                            .forEach(function (textShadow) {\n                                            _this.ctx.shadowColor = asString(textShadow.color);\n                                            _this.ctx.shadowOffsetX = textShadow.offsetX.number * _this.options.scale;\n                                            _this.ctx.shadowOffsetY = textShadow.offsetY.number * _this.options.scale;\n                                            _this.ctx.shadowBlur = textShadow.blur.number;\n                                            _this.renderTextWithLetterSpacing(text, styles.letterSpacing, baseline);\n                                        });\n                                        _this.ctx.shadowColor = '';\n                                        _this.ctx.shadowOffsetX = 0;\n                                        _this.ctx.shadowOffsetY = 0;\n                                        _this.ctx.shadowBlur = 0;\n                                    }\n                                    if (styles.textDecorationLine.length) {\n                                        _this.ctx.fillStyle = asString(styles.textDecorationColor || styles.color);\n                                        styles.textDecorationLine.forEach(function (textDecorationLine) {\n                                            switch (textDecorationLine) {\n                                                case 1 /* UNDERLINE */:\n                                                    // Draws a line at the baseline of the font\n                                                    // TODO As some browsers display the line as more than 1px if the font-size is big,\n                                                    // need to take that into account both in position and size\n                                                    _this.ctx.fillRect(text.bounds.left, Math.round(text.bounds.top + baseline), text.bounds.width, 1);\n                                                    break;\n                                                case 2 /* OVERLINE */:\n                                                    _this.ctx.fillRect(text.bounds.left, Math.round(text.bounds.top), text.bounds.width, 1);\n                                                    break;\n                                                case 3 /* LINE_THROUGH */:\n                                                    // TODO try and find exact position for line-through\n                                                    _this.ctx.fillRect(text.bounds.left, Math.ceil(text.bounds.top + middle), text.bounds.width, 1);\n                                                    break;\n                                            }\n                                        });\n                                    }\n                                    break;\n                                case 1 /* STROKE */:\n                                    if (styles.webkitTextStrokeWidth && text.text.trim().length) {\n                                        _this.ctx.strokeStyle = asString(styles.webkitTextStrokeColor);\n                                        _this.ctx.lineWidth = styles.webkitTextStrokeWidth;\n                                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                                        _this.ctx.lineJoin = !!window.chrome ? 'miter' : 'round';\n                                        _this.ctx.strokeText(text.text, text.bounds.left, text.bounds.top + baseline);\n                                    }\n                                    _this.ctx.strokeStyle = '';\n                                    _this.ctx.lineWidth = 0;\n                                    _this.ctx.lineJoin = 'miter';\n                                    break;\n                            }\n                        });\n                    });\n                    return [2 /*return*/];\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderReplacedElement = function (container, curves, image) {\n            if (image && container.intrinsicWidth > 0 && container.intrinsicHeight > 0) {\n                var box = contentBox(container);\n                var path = calculatePaddingBoxPath(curves);\n                this.path(path);\n                this.ctx.save();\n                this.ctx.clip();\n                this.ctx.drawImage(image, 0, 0, container.intrinsicWidth, container.intrinsicHeight, box.left, box.top, box.width, box.height);\n                this.ctx.restore();\n            }\n        };\n        CanvasRenderer.prototype.renderNodeContent = function (paint) {\n            return __awaiter(this, void 0, void 0, function () {\n                var container, curves, styles, _i, _a, child, image, image, iframeRenderer, canvas, size, _b, fontFamily, fontSize, baseline, bounds, x, textBounds, img, image, url, fontFamily, bounds;\n                return __generator(this, function (_c) {\n                    switch (_c.label) {\n                        case 0:\n                            this.applyEffects(paint.getEffects(4 /* CONTENT */));\n                            container = paint.container;\n                            curves = paint.curves;\n                            styles = container.styles;\n                            _i = 0, _a = container.textNodes;\n                            _c.label = 1;\n                        case 1:\n                            if (!(_i < _a.length)) return [3 /*break*/, 4];\n                            child = _a[_i];\n                            return [4 /*yield*/, this.renderTextNode(child, styles)];\n                        case 2:\n                            _c.sent();\n                            _c.label = 3;\n                        case 3:\n                            _i++;\n                            return [3 /*break*/, 1];\n                        case 4:\n                            if (!(container instanceof ImageElementContainer)) return [3 /*break*/, 8];\n                            _c.label = 5;\n                        case 5:\n                            _c.trys.push([5, 7, , 8]);\n                            return [4 /*yield*/, this.context.cache.match(container.src)];\n                        case 6:\n                            image = _c.sent();\n                            this.renderReplacedElement(container, curves, image);\n                            return [3 /*break*/, 8];\n                        case 7:\n                            _c.sent();\n                            this.context.logger.error(\"Error loading image \" + container.src);\n                            return [3 /*break*/, 8];\n                        case 8:\n                            if (container instanceof CanvasElementContainer) {\n                                this.renderReplacedElement(container, curves, container.canvas);\n                            }\n                            if (!(container instanceof SVGElementContainer)) return [3 /*break*/, 12];\n                            _c.label = 9;\n                        case 9:\n                            _c.trys.push([9, 11, , 12]);\n                            return [4 /*yield*/, this.context.cache.match(container.svg)];\n                        case 10:\n                            image = _c.sent();\n                            this.renderReplacedElement(container, curves, image);\n                            return [3 /*break*/, 12];\n                        case 11:\n                            _c.sent();\n                            this.context.logger.error(\"Error loading svg \" + container.svg.substring(0, 255));\n                            return [3 /*break*/, 12];\n                        case 12:\n                            if (!(container instanceof IFrameElementContainer && container.tree)) return [3 /*break*/, 14];\n                            iframeRenderer = new CanvasRenderer(this.context, {\n                                scale: this.options.scale,\n                                backgroundColor: container.backgroundColor,\n                                x: 0,\n                                y: 0,\n                                width: container.width,\n                                height: container.height\n                            });\n                            return [4 /*yield*/, iframeRenderer.render(container.tree)];\n                        case 13:\n                            canvas = _c.sent();\n                            if (container.width && container.height) {\n                                this.ctx.drawImage(canvas, 0, 0, container.width, container.height, container.bounds.left, container.bounds.top, container.bounds.width, container.bounds.height);\n                            }\n                            _c.label = 14;\n                        case 14:\n                            if (container instanceof InputElementContainer) {\n                                size = Math.min(container.bounds.width, container.bounds.height);\n                                if (container.type === CHECKBOX) {\n                                    if (container.checked) {\n                                        this.ctx.save();\n                                        this.path([\n                                            new Vector(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79),\n                                            new Vector(container.bounds.left + size * 0.16, container.bounds.top + size * 0.5549),\n                                            new Vector(container.bounds.left + size * 0.27347, container.bounds.top + size * 0.44071),\n                                            new Vector(container.bounds.left + size * 0.39694, container.bounds.top + size * 0.5649),\n                                            new Vector(container.bounds.left + size * 0.72983, container.bounds.top + size * 0.23),\n                                            new Vector(container.bounds.left + size * 0.84, container.bounds.top + size * 0.34085),\n                                            new Vector(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79)\n                                        ]);\n                                        this.ctx.fillStyle = asString(INPUT_COLOR);\n                                        this.ctx.fill();\n                                        this.ctx.restore();\n                                    }\n                                }\n                                else if (container.type === RADIO) {\n                                    if (container.checked) {\n                                        this.ctx.save();\n                                        this.ctx.beginPath();\n                                        this.ctx.arc(container.bounds.left + size / 2, container.bounds.top + size / 2, size / 4, 0, Math.PI * 2, true);\n                                        this.ctx.fillStyle = asString(INPUT_COLOR);\n                                        this.ctx.fill();\n                                        this.ctx.restore();\n                                    }\n                                }\n                            }\n                            if (isTextInputElement(container) && container.value.length) {\n                                _b = this.createFontStyle(styles), fontFamily = _b[0], fontSize = _b[1];\n                                baseline = this.fontMetrics.getMetrics(fontFamily, fontSize).baseline;\n                                this.ctx.font = fontFamily;\n                                this.ctx.fillStyle = asString(styles.color);\n                                this.ctx.textBaseline = 'alphabetic';\n                                this.ctx.textAlign = canvasTextAlign(container.styles.textAlign);\n                                bounds = contentBox(container);\n                                x = 0;\n                                switch (container.styles.textAlign) {\n                                    case 1 /* CENTER */:\n                                        x += bounds.width / 2;\n                                        break;\n                                    case 2 /* RIGHT */:\n                                        x += bounds.width;\n                                        break;\n                                }\n                                textBounds = bounds.add(x, 0, 0, -bounds.height / 2 + 1);\n                                this.ctx.save();\n                                this.path([\n                                    new Vector(bounds.left, bounds.top),\n                                    new Vector(bounds.left + bounds.width, bounds.top),\n                                    new Vector(bounds.left + bounds.width, bounds.top + bounds.height),\n                                    new Vector(bounds.left, bounds.top + bounds.height)\n                                ]);\n                                this.ctx.clip();\n                                this.renderTextWithLetterSpacing(new TextBounds(container.value, textBounds), styles.letterSpacing, baseline);\n                                this.ctx.restore();\n                                this.ctx.textBaseline = 'alphabetic';\n                                this.ctx.textAlign = 'left';\n                            }\n                            if (!contains(container.styles.display, 2048 /* LIST_ITEM */)) return [3 /*break*/, 20];\n                            if (!(container.styles.listStyleImage !== null)) return [3 /*break*/, 19];\n                            img = container.styles.listStyleImage;\n                            if (!(img.type === 0 /* URL */)) return [3 /*break*/, 18];\n                            image = void 0;\n                            url = img.url;\n                            _c.label = 15;\n                        case 15:\n                            _c.trys.push([15, 17, , 18]);\n                            return [4 /*yield*/, this.context.cache.match(url)];\n                        case 16:\n                            image = _c.sent();\n                            this.ctx.drawImage(image, container.bounds.left - (image.width + 10), container.bounds.top);\n                            return [3 /*break*/, 18];\n                        case 17:\n                            _c.sent();\n                            this.context.logger.error(\"Error loading list-style-image \" + url);\n                            return [3 /*break*/, 18];\n                        case 18: return [3 /*break*/, 20];\n                        case 19:\n                            if (paint.listValue && container.styles.listStyleType !== -1 /* NONE */) {\n                                fontFamily = this.createFontStyle(styles)[0];\n                                this.ctx.font = fontFamily;\n                                this.ctx.fillStyle = asString(styles.color);\n                                this.ctx.textBaseline = 'middle';\n                                this.ctx.textAlign = 'right';\n                                bounds = new Bounds(container.bounds.left, container.bounds.top + getAbsoluteValue(container.styles.paddingTop, container.bounds.width), container.bounds.width, computeLineHeight(styles.lineHeight, styles.fontSize.number) / 2 + 1);\n                                this.renderTextWithLetterSpacing(new TextBounds(paint.listValue, bounds), styles.letterSpacing, computeLineHeight(styles.lineHeight, styles.fontSize.number) / 2 + 2);\n                                this.ctx.textBaseline = 'bottom';\n                                this.ctx.textAlign = 'left';\n                            }\n                            _c.label = 20;\n                        case 20: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderStackContent = function (stack) {\n            return __awaiter(this, void 0, void 0, function () {\n                var _i, _a, child, _b, _c, child, _d, _e, child, _f, _g, child, _h, _j, child, _k, _l, child, _m, _o, child;\n                return __generator(this, function (_p) {\n                    switch (_p.label) {\n                        case 0:\n                            if (contains(stack.element.container.flags, 16 /* DEBUG_RENDER */)) {\n                                debugger;\n                            }\n                            // https://www.w3.org/TR/css-position-3/#painting-order\n                            // 1. the background and borders of the element forming the stacking context.\n                            return [4 /*yield*/, this.renderNodeBackgroundAndBorders(stack.element)];\n                        case 1:\n                            // https://www.w3.org/TR/css-position-3/#painting-order\n                            // 1. the background and borders of the element forming the stacking context.\n                            _p.sent();\n                            _i = 0, _a = stack.negativeZIndex;\n                            _p.label = 2;\n                        case 2:\n                            if (!(_i < _a.length)) return [3 /*break*/, 5];\n                            child = _a[_i];\n                            return [4 /*yield*/, this.renderStack(child)];\n                        case 3:\n                            _p.sent();\n                            _p.label = 4;\n                        case 4:\n                            _i++;\n                            return [3 /*break*/, 2];\n                        case 5: \n                        // 3. For all its in-flow, non-positioned, block-level descendants in tree order:\n                        return [4 /*yield*/, this.renderNodeContent(stack.element)];\n                        case 6:\n                            // 3. For all its in-flow, non-positioned, block-level descendants in tree order:\n                            _p.sent();\n                            _b = 0, _c = stack.nonInlineLevel;\n                            _p.label = 7;\n                        case 7:\n                            if (!(_b < _c.length)) return [3 /*break*/, 10];\n                            child = _c[_b];\n                            return [4 /*yield*/, this.renderNode(child)];\n                        case 8:\n                            _p.sent();\n                            _p.label = 9;\n                        case 9:\n                            _b++;\n                            return [3 /*break*/, 7];\n                        case 10:\n                            _d = 0, _e = stack.nonPositionedFloats;\n                            _p.label = 11;\n                        case 11:\n                            if (!(_d < _e.length)) return [3 /*break*/, 14];\n                            child = _e[_d];\n                            return [4 /*yield*/, this.renderStack(child)];\n                        case 12:\n                            _p.sent();\n                            _p.label = 13;\n                        case 13:\n                            _d++;\n                            return [3 /*break*/, 11];\n                        case 14:\n                            _f = 0, _g = stack.nonPositionedInlineLevel;\n                            _p.label = 15;\n                        case 15:\n                            if (!(_f < _g.length)) return [3 /*break*/, 18];\n                            child = _g[_f];\n                            return [4 /*yield*/, this.renderStack(child)];\n                        case 16:\n                            _p.sent();\n                            _p.label = 17;\n                        case 17:\n                            _f++;\n                            return [3 /*break*/, 15];\n                        case 18:\n                            _h = 0, _j = stack.inlineLevel;\n                            _p.label = 19;\n                        case 19:\n                            if (!(_h < _j.length)) return [3 /*break*/, 22];\n                            child = _j[_h];\n                            return [4 /*yield*/, this.renderNode(child)];\n                        case 20:\n                            _p.sent();\n                            _p.label = 21;\n                        case 21:\n                            _h++;\n                            return [3 /*break*/, 19];\n                        case 22:\n                            _k = 0, _l = stack.zeroOrAutoZIndexOrTransformedOrOpacity;\n                            _p.label = 23;\n                        case 23:\n                            if (!(_k < _l.length)) return [3 /*break*/, 26];\n                            child = _l[_k];\n                            return [4 /*yield*/, this.renderStack(child)];\n                        case 24:\n                            _p.sent();\n                            _p.label = 25;\n                        case 25:\n                            _k++;\n                            return [3 /*break*/, 23];\n                        case 26:\n                            _m = 0, _o = stack.positiveZIndex;\n                            _p.label = 27;\n                        case 27:\n                            if (!(_m < _o.length)) return [3 /*break*/, 30];\n                            child = _o[_m];\n                            return [4 /*yield*/, this.renderStack(child)];\n                        case 28:\n                            _p.sent();\n                            _p.label = 29;\n                        case 29:\n                            _m++;\n                            return [3 /*break*/, 27];\n                        case 30: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.mask = function (paths) {\n            this.ctx.beginPath();\n            this.ctx.moveTo(0, 0);\n            this.ctx.lineTo(this.canvas.width, 0);\n            this.ctx.lineTo(this.canvas.width, this.canvas.height);\n            this.ctx.lineTo(0, this.canvas.height);\n            this.ctx.lineTo(0, 0);\n            this.formatPath(paths.slice(0).reverse());\n            this.ctx.closePath();\n        };\n        CanvasRenderer.prototype.path = function (paths) {\n            this.ctx.beginPath();\n            this.formatPath(paths);\n            this.ctx.closePath();\n        };\n        CanvasRenderer.prototype.formatPath = function (paths) {\n            var _this = this;\n            paths.forEach(function (point, index) {\n                var start = isBezierCurve(point) ? point.start : point;\n                if (index === 0) {\n                    _this.ctx.moveTo(start.x, start.y);\n                }\n                else {\n                    _this.ctx.lineTo(start.x, start.y);\n                }\n                if (isBezierCurve(point)) {\n                    _this.ctx.bezierCurveTo(point.startControl.x, point.startControl.y, point.endControl.x, point.endControl.y, point.end.x, point.end.y);\n                }\n            });\n        };\n        CanvasRenderer.prototype.renderRepeat = function (path, pattern, offsetX, offsetY) {\n            this.path(path);\n            this.ctx.fillStyle = pattern;\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.fill();\n            this.ctx.translate(-offsetX, -offsetY);\n        };\n        CanvasRenderer.prototype.resizeImage = function (image, width, height) {\n            var _a;\n            if (image.width === width && image.height === height) {\n                return image;\n            }\n            var ownerDocument = (_a = this.canvas.ownerDocument) !== null && _a !== void 0 ? _a : document;\n            var canvas = ownerDocument.createElement('canvas');\n            canvas.width = Math.max(1, width);\n            canvas.height = Math.max(1, height);\n            var ctx = canvas.getContext('2d');\n            ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, width, height);\n            return canvas;\n        };\n        CanvasRenderer.prototype.renderBackgroundImage = function (container) {\n            return __awaiter(this, void 0, void 0, function () {\n                var index, _loop_1, this_1, _i, _a, backgroundImage;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            index = container.styles.backgroundImage.length - 1;\n                            _loop_1 = function (backgroundImage) {\n                                var image, url, _c, path, x, y, width, height, pattern, _d, path, x, y, width, height, _e, lineLength, x0, x1, y0, y1, canvas, ctx, gradient_1, pattern, _f, path, left, top_1, width, height, position, x, y, _g, rx, ry, radialGradient_1, midX, midY, f, invF;\n                                return __generator(this, function (_h) {\n                                    switch (_h.label) {\n                                        case 0:\n                                            if (!(backgroundImage.type === 0 /* URL */)) return [3 /*break*/, 5];\n                                            image = void 0;\n                                            url = backgroundImage.url;\n                                            _h.label = 1;\n                                        case 1:\n                                            _h.trys.push([1, 3, , 4]);\n                                            return [4 /*yield*/, this_1.context.cache.match(url)];\n                                        case 2:\n                                            image = _h.sent();\n                                            return [3 /*break*/, 4];\n                                        case 3:\n                                            _h.sent();\n                                            this_1.context.logger.error(\"Error loading background-image \" + url);\n                                            return [3 /*break*/, 4];\n                                        case 4:\n                                            if (image) {\n                                                _c = calculateBackgroundRendering(container, index, [\n                                                    image.width,\n                                                    image.height,\n                                                    image.width / image.height\n                                                ]), path = _c[0], x = _c[1], y = _c[2], width = _c[3], height = _c[4];\n                                                pattern = this_1.ctx.createPattern(this_1.resizeImage(image, width, height), 'repeat');\n                                                this_1.renderRepeat(path, pattern, x, y);\n                                            }\n                                            return [3 /*break*/, 6];\n                                        case 5:\n                                            if (isLinearGradient(backgroundImage)) {\n                                                _d = calculateBackgroundRendering(container, index, [null, null, null]), path = _d[0], x = _d[1], y = _d[2], width = _d[3], height = _d[4];\n                                                _e = calculateGradientDirection(backgroundImage.angle, width, height), lineLength = _e[0], x0 = _e[1], x1 = _e[2], y0 = _e[3], y1 = _e[4];\n                                                canvas = document.createElement('canvas');\n                                                canvas.width = width;\n                                                canvas.height = height;\n                                                ctx = canvas.getContext('2d');\n                                                gradient_1 = ctx.createLinearGradient(x0, y0, x1, y1);\n                                                processColorStops(backgroundImage.stops, lineLength).forEach(function (colorStop) {\n                                                    return gradient_1.addColorStop(colorStop.stop, asString(colorStop.color));\n                                                });\n                                                ctx.fillStyle = gradient_1;\n                                                ctx.fillRect(0, 0, width, height);\n                                                if (width > 0 && height > 0) {\n                                                    pattern = this_1.ctx.createPattern(canvas, 'repeat');\n                                                    this_1.renderRepeat(path, pattern, x, y);\n                                                }\n                                            }\n                                            else if (isRadialGradient(backgroundImage)) {\n                                                _f = calculateBackgroundRendering(container, index, [\n                                                    null,\n                                                    null,\n                                                    null\n                                                ]), path = _f[0], left = _f[1], top_1 = _f[2], width = _f[3], height = _f[4];\n                                                position = backgroundImage.position.length === 0 ? [FIFTY_PERCENT] : backgroundImage.position;\n                                                x = getAbsoluteValue(position[0], width);\n                                                y = getAbsoluteValue(position[position.length - 1], height);\n                                                _g = calculateRadius(backgroundImage, x, y, width, height), rx = _g[0], ry = _g[1];\n                                                if (rx > 0 && ry > 0) {\n                                                    radialGradient_1 = this_1.ctx.createRadialGradient(left + x, top_1 + y, 0, left + x, top_1 + y, rx);\n                                                    processColorStops(backgroundImage.stops, rx * 2).forEach(function (colorStop) {\n                                                        return radialGradient_1.addColorStop(colorStop.stop, asString(colorStop.color));\n                                                    });\n                                                    this_1.path(path);\n                                                    this_1.ctx.fillStyle = radialGradient_1;\n                                                    if (rx !== ry) {\n                                                        midX = container.bounds.left + 0.5 * container.bounds.width;\n                                                        midY = container.bounds.top + 0.5 * container.bounds.height;\n                                                        f = ry / rx;\n                                                        invF = 1 / f;\n                                                        this_1.ctx.save();\n                                                        this_1.ctx.translate(midX, midY);\n                                                        this_1.ctx.transform(1, 0, 0, f, 0, 0);\n                                                        this_1.ctx.translate(-midX, -midY);\n                                                        this_1.ctx.fillRect(left, invF * (top_1 - midY) + midY, width, height * invF);\n                                                        this_1.ctx.restore();\n                                                    }\n                                                    else {\n                                                        this_1.ctx.fill();\n                                                    }\n                                                }\n                                            }\n                                            _h.label = 6;\n                                        case 6:\n                                            index--;\n                                            return [2 /*return*/];\n                                    }\n                                });\n                            };\n                            this_1 = this;\n                            _i = 0, _a = container.styles.backgroundImage.slice(0).reverse();\n                            _b.label = 1;\n                        case 1:\n                            if (!(_i < _a.length)) return [3 /*break*/, 4];\n                            backgroundImage = _a[_i];\n                            return [5 /*yield**/, _loop_1(backgroundImage)];\n                        case 2:\n                            _b.sent();\n                            _b.label = 3;\n                        case 3:\n                            _i++;\n                            return [3 /*break*/, 1];\n                        case 4: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderSolidBorder = function (color, side, curvePoints) {\n            return __awaiter(this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                    this.path(parsePathForBorder(curvePoints, side));\n                    this.ctx.fillStyle = asString(color);\n                    this.ctx.fill();\n                    return [2 /*return*/];\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderDoubleBorder = function (color, width, side, curvePoints) {\n            return __awaiter(this, void 0, void 0, function () {\n                var outerPaths, innerPaths;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            if (!(width < 3)) return [3 /*break*/, 2];\n                            return [4 /*yield*/, this.renderSolidBorder(color, side, curvePoints)];\n                        case 1:\n                            _a.sent();\n                            return [2 /*return*/];\n                        case 2:\n                            outerPaths = parsePathForBorderDoubleOuter(curvePoints, side);\n                            this.path(outerPaths);\n                            this.ctx.fillStyle = asString(color);\n                            this.ctx.fill();\n                            innerPaths = parsePathForBorderDoubleInner(curvePoints, side);\n                            this.path(innerPaths);\n                            this.ctx.fill();\n                            return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderNodeBackgroundAndBorders = function (paint) {\n            return __awaiter(this, void 0, void 0, function () {\n                var styles, hasBackground, borders, backgroundPaintingArea, side, _i, borders_1, border;\n                var _this = this;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            this.applyEffects(paint.getEffects(2 /* BACKGROUND_BORDERS */));\n                            styles = paint.container.styles;\n                            hasBackground = !isTransparent(styles.backgroundColor) || styles.backgroundImage.length;\n                            borders = [\n                                { style: styles.borderTopStyle, color: styles.borderTopColor, width: styles.borderTopWidth },\n                                { style: styles.borderRightStyle, color: styles.borderRightColor, width: styles.borderRightWidth },\n                                { style: styles.borderBottomStyle, color: styles.borderBottomColor, width: styles.borderBottomWidth },\n                                { style: styles.borderLeftStyle, color: styles.borderLeftColor, width: styles.borderLeftWidth }\n                            ];\n                            backgroundPaintingArea = calculateBackgroundCurvedPaintingArea(getBackgroundValueForIndex(styles.backgroundClip, 0), paint.curves);\n                            if (!(hasBackground || styles.boxShadow.length)) return [3 /*break*/, 2];\n                            this.ctx.save();\n                            this.path(backgroundPaintingArea);\n                            this.ctx.clip();\n                            if (!isTransparent(styles.backgroundColor)) {\n                                this.ctx.fillStyle = asString(styles.backgroundColor);\n                                this.ctx.fill();\n                            }\n                            return [4 /*yield*/, this.renderBackgroundImage(paint.container)];\n                        case 1:\n                            _a.sent();\n                            this.ctx.restore();\n                            styles.boxShadow\n                                .slice(0)\n                                .reverse()\n                                .forEach(function (shadow) {\n                                _this.ctx.save();\n                                var borderBoxArea = calculateBorderBoxPath(paint.curves);\n                                var maskOffset = shadow.inset ? 0 : MASK_OFFSET;\n                                var shadowPaintingArea = transformPath(borderBoxArea, -maskOffset + (shadow.inset ? 1 : -1) * shadow.spread.number, (shadow.inset ? 1 : -1) * shadow.spread.number, shadow.spread.number * (shadow.inset ? -2 : 2), shadow.spread.number * (shadow.inset ? -2 : 2));\n                                if (shadow.inset) {\n                                    _this.path(borderBoxArea);\n                                    _this.ctx.clip();\n                                    _this.mask(shadowPaintingArea);\n                                }\n                                else {\n                                    _this.mask(borderBoxArea);\n                                    _this.ctx.clip();\n                                    _this.path(shadowPaintingArea);\n                                }\n                                _this.ctx.shadowOffsetX = shadow.offsetX.number + maskOffset;\n                                _this.ctx.shadowOffsetY = shadow.offsetY.number;\n                                _this.ctx.shadowColor = asString(shadow.color);\n                                _this.ctx.shadowBlur = shadow.blur.number;\n                                _this.ctx.fillStyle = shadow.inset ? asString(shadow.color) : 'rgba(0,0,0,1)';\n                                _this.ctx.fill();\n                                _this.ctx.restore();\n                            });\n                            _a.label = 2;\n                        case 2:\n                            side = 0;\n                            _i = 0, borders_1 = borders;\n                            _a.label = 3;\n                        case 3:\n                            if (!(_i < borders_1.length)) return [3 /*break*/, 13];\n                            border = borders_1[_i];\n                            if (!(border.style !== 0 /* NONE */ && !isTransparent(border.color) && border.width > 0)) return [3 /*break*/, 11];\n                            if (!(border.style === 2 /* DASHED */)) return [3 /*break*/, 5];\n                            return [4 /*yield*/, this.renderDashedDottedBorder(border.color, border.width, side, paint.curves, 2 /* DASHED */)];\n                        case 4:\n                            _a.sent();\n                            return [3 /*break*/, 11];\n                        case 5:\n                            if (!(border.style === 3 /* DOTTED */)) return [3 /*break*/, 7];\n                            return [4 /*yield*/, this.renderDashedDottedBorder(border.color, border.width, side, paint.curves, 3 /* DOTTED */)];\n                        case 6:\n                            _a.sent();\n                            return [3 /*break*/, 11];\n                        case 7:\n                            if (!(border.style === 4 /* DOUBLE */)) return [3 /*break*/, 9];\n                            return [4 /*yield*/, this.renderDoubleBorder(border.color, border.width, side, paint.curves)];\n                        case 8:\n                            _a.sent();\n                            return [3 /*break*/, 11];\n                        case 9: return [4 /*yield*/, this.renderSolidBorder(border.color, side, paint.curves)];\n                        case 10:\n                            _a.sent();\n                            _a.label = 11;\n                        case 11:\n                            side++;\n                            _a.label = 12;\n                        case 12:\n                            _i++;\n                            return [3 /*break*/, 3];\n                        case 13: return [2 /*return*/];\n                    }\n                });\n            });\n        };\n        CanvasRenderer.prototype.renderDashedDottedBorder = function (color, width, side, curvePoints, style) {\n            return __awaiter(this, void 0, void 0, function () {\n                var strokePaths, boxPaths, startX, startY, endX, endY, length, dashLength, spaceLength, useLineDash, multiplier, numberOfDashes, minSpace, maxSpace, path1, path2, path1, path2;\n                return __generator(this, function (_a) {\n                    this.ctx.save();\n                    strokePaths = parsePathForBorderStroke(curvePoints, side);\n                    boxPaths = parsePathForBorder(curvePoints, side);\n                    if (style === 2 /* DASHED */) {\n                        this.path(boxPaths);\n                        this.ctx.clip();\n                    }\n                    if (isBezierCurve(boxPaths[0])) {\n                        startX = boxPaths[0].start.x;\n                        startY = boxPaths[0].start.y;\n                    }\n                    else {\n                        startX = boxPaths[0].x;\n                        startY = boxPaths[0].y;\n                    }\n                    if (isBezierCurve(boxPaths[1])) {\n                        endX = boxPaths[1].end.x;\n                        endY = boxPaths[1].end.y;\n                    }\n                    else {\n                        endX = boxPaths[1].x;\n                        endY = boxPaths[1].y;\n                    }\n                    if (side === 0 || side === 2) {\n                        length = Math.abs(startX - endX);\n                    }\n                    else {\n                        length = Math.abs(startY - endY);\n                    }\n                    this.ctx.beginPath();\n                    if (style === 3 /* DOTTED */) {\n                        this.formatPath(strokePaths);\n                    }\n                    else {\n                        this.formatPath(boxPaths.slice(0, 2));\n                    }\n                    dashLength = width < 3 ? width * 3 : width * 2;\n                    spaceLength = width < 3 ? width * 2 : width;\n                    if (style === 3 /* DOTTED */) {\n                        dashLength = width;\n                        spaceLength = width;\n                    }\n                    useLineDash = true;\n                    if (length <= dashLength * 2) {\n                        useLineDash = false;\n                    }\n                    else if (length <= dashLength * 2 + spaceLength) {\n                        multiplier = length / (2 * dashLength + spaceLength);\n                        dashLength *= multiplier;\n                        spaceLength *= multiplier;\n                    }\n                    else {\n                        numberOfDashes = Math.floor((length + spaceLength) / (dashLength + spaceLength));\n                        minSpace = (length - numberOfDashes * dashLength) / (numberOfDashes - 1);\n                        maxSpace = (length - (numberOfDashes + 1) * dashLength) / numberOfDashes;\n                        spaceLength =\n                            maxSpace <= 0 || Math.abs(spaceLength - minSpace) < Math.abs(spaceLength - maxSpace)\n                                ? minSpace\n                                : maxSpace;\n                    }\n                    if (useLineDash) {\n                        if (style === 3 /* DOTTED */) {\n                            this.ctx.setLineDash([0, dashLength + spaceLength]);\n                        }\n                        else {\n                            this.ctx.setLineDash([dashLength, spaceLength]);\n                        }\n                    }\n                    if (style === 3 /* DOTTED */) {\n                        this.ctx.lineCap = 'round';\n                        this.ctx.lineWidth = width;\n                    }\n                    else {\n                        this.ctx.lineWidth = width * 2 + 1.1;\n                    }\n                    this.ctx.strokeStyle = asString(color);\n                    this.ctx.stroke();\n                    this.ctx.setLineDash([]);\n                    // dashed round edge gap\n                    if (style === 2 /* DASHED */) {\n                        if (isBezierCurve(boxPaths[0])) {\n                            path1 = boxPaths[3];\n                            path2 = boxPaths[0];\n                            this.ctx.beginPath();\n                            this.formatPath([new Vector(path1.end.x, path1.end.y), new Vector(path2.start.x, path2.start.y)]);\n                            this.ctx.stroke();\n                        }\n                        if (isBezierCurve(boxPaths[1])) {\n                            path1 = boxPaths[1];\n                            path2 = boxPaths[2];\n                            this.ctx.beginPath();\n                            this.formatPath([new Vector(path1.end.x, path1.end.y), new Vector(path2.start.x, path2.start.y)]);\n                            this.ctx.stroke();\n                        }\n                    }\n                    this.ctx.restore();\n                    return [2 /*return*/];\n                });\n            });\n        };\n        CanvasRenderer.prototype.render = function (element) {\n            return __awaiter(this, void 0, void 0, function () {\n                var stack;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            if (this.options.backgroundColor) {\n                                this.ctx.fillStyle = asString(this.options.backgroundColor);\n                                this.ctx.fillRect(this.options.x, this.options.y, this.options.width, this.options.height);\n                            }\n                            stack = parseStackingContexts(element);\n                            return [4 /*yield*/, this.renderStack(stack)];\n                        case 1:\n                            _a.sent();\n                            this.applyEffects([]);\n                            return [2 /*return*/, this.canvas];\n                    }\n                });\n            });\n        };\n        return CanvasRenderer;\n    }(Renderer));\n    var isTextInputElement = function (container) {\n        if (container instanceof TextareaElementContainer) {\n            return true;\n        }\n        else if (container instanceof SelectElementContainer) {\n            return true;\n        }\n        else if (container instanceof InputElementContainer && container.type !== RADIO && container.type !== CHECKBOX) {\n            return true;\n        }\n        return false;\n    };\n    var calculateBackgroundCurvedPaintingArea = function (clip, curves) {\n        switch (clip) {\n            case 0 /* BORDER_BOX */:\n                return calculateBorderBoxPath(curves);\n            case 2 /* CONTENT_BOX */:\n                return calculateContentBoxPath(curves);\n            case 1 /* PADDING_BOX */:\n            default:\n                return calculatePaddingBoxPath(curves);\n        }\n    };\n    var canvasTextAlign = function (textAlign) {\n        switch (textAlign) {\n            case 1 /* CENTER */:\n                return 'center';\n            case 2 /* RIGHT */:\n                return 'right';\n            case 0 /* LEFT */:\n            default:\n                return 'left';\n        }\n    };\n    // see https://github.com/niklasvh/html2canvas/pull/2645\n    var iOSBrokenFonts = ['-apple-system', 'system-ui'];\n    var fixIOSSystemFonts = function (fontFamilies) {\n        return /iPhone OS 15_(0|1)/.test(window.navigator.userAgent)\n            ? fontFamilies.filter(function (fontFamily) { return iOSBrokenFonts.indexOf(fontFamily) === -1; })\n            : fontFamilies;\n    };\n\n    var ForeignObjectRenderer = /** @class */ (function (_super) {\n        __extends(ForeignObjectRenderer, _super);\n        function ForeignObjectRenderer(context, options) {\n            var _this = _super.call(this, context, options) || this;\n            _this.canvas = options.canvas ? options.canvas : document.createElement('canvas');\n            _this.ctx = _this.canvas.getContext('2d');\n            _this.options = options;\n            _this.canvas.width = Math.floor(options.width * options.scale);\n            _this.canvas.height = Math.floor(options.height * options.scale);\n            _this.canvas.style.width = options.width + \"px\";\n            _this.canvas.style.height = options.height + \"px\";\n            _this.ctx.scale(_this.options.scale, _this.options.scale);\n            _this.ctx.translate(-options.x, -options.y);\n            _this.context.logger.debug(\"EXPERIMENTAL ForeignObject renderer initialized (\" + options.width + \"x\" + options.height + \" at \" + options.x + \",\" + options.y + \") with scale \" + options.scale);\n            return _this;\n        }\n        ForeignObjectRenderer.prototype.render = function (element) {\n            return __awaiter(this, void 0, void 0, function () {\n                var svg, img;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            svg = createForeignObjectSVG(this.options.width * this.options.scale, this.options.height * this.options.scale, this.options.scale, this.options.scale, element);\n                            return [4 /*yield*/, loadSerializedSVG(svg)];\n                        case 1:\n                            img = _a.sent();\n                            if (this.options.backgroundColor) {\n                                this.ctx.fillStyle = asString(this.options.backgroundColor);\n                                this.ctx.fillRect(0, 0, this.options.width * this.options.scale, this.options.height * this.options.scale);\n                            }\n                            this.ctx.drawImage(img, -this.options.x * this.options.scale, -this.options.y * this.options.scale);\n                            return [2 /*return*/, this.canvas];\n                    }\n                });\n            });\n        };\n        return ForeignObjectRenderer;\n    }(Renderer));\n    var loadSerializedSVG = function (svg) {\n        return new Promise(function (resolve, reject) {\n            var img = new Image();\n            img.onload = function () {\n                resolve(img);\n            };\n            img.onerror = reject;\n            img.src = \"data:image/svg+xml;charset=utf-8,\" + encodeURIComponent(new XMLSerializer().serializeToString(svg));\n        });\n    };\n\n    var Logger = /** @class */ (function () {\n        function Logger(_a) {\n            var id = _a.id, enabled = _a.enabled;\n            this.id = id;\n            this.enabled = enabled;\n            this.start = Date.now();\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Logger.prototype.debug = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (this.enabled) {\n                // eslint-disable-next-line no-console\n                if (typeof window !== 'undefined' && window.console && typeof console.debug === 'function') {\n                    // eslint-disable-next-line no-console\n                    console.debug.apply(console, __spreadArray([this.id, this.getTime() + \"ms\"], args));\n                }\n                else {\n                    this.info.apply(this, args);\n                }\n            }\n        };\n        Logger.prototype.getTime = function () {\n            return Date.now() - this.start;\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Logger.prototype.info = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (this.enabled) {\n                // eslint-disable-next-line no-console\n                if (typeof window !== 'undefined' && window.console && typeof console.info === 'function') {\n                    // eslint-disable-next-line no-console\n                    console.info.apply(console, __spreadArray([this.id, this.getTime() + \"ms\"], args));\n                }\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Logger.prototype.warn = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (this.enabled) {\n                // eslint-disable-next-line no-console\n                if (typeof window !== 'undefined' && window.console && typeof console.warn === 'function') {\n                    // eslint-disable-next-line no-console\n                    console.warn.apply(console, __spreadArray([this.id, this.getTime() + \"ms\"], args));\n                }\n                else {\n                    this.info.apply(this, args);\n                }\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Logger.prototype.error = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (this.enabled) {\n                // eslint-disable-next-line no-console\n                if (typeof window !== 'undefined' && window.console && typeof console.error === 'function') {\n                    // eslint-disable-next-line no-console\n                    console.error.apply(console, __spreadArray([this.id, this.getTime() + \"ms\"], args));\n                }\n                else {\n                    this.info.apply(this, args);\n                }\n            }\n        };\n        Logger.instances = {};\n        return Logger;\n    }());\n\n    var Context = /** @class */ (function () {\n        function Context(options, windowBounds) {\n            var _a;\n            this.windowBounds = windowBounds;\n            this.instanceName = \"#\" + Context.instanceCount++;\n            this.logger = new Logger({ id: this.instanceName, enabled: options.logging });\n            this.cache = (_a = options.cache) !== null && _a !== void 0 ? _a : new Cache(this, options);\n        }\n        Context.instanceCount = 1;\n        return Context;\n    }());\n\n    var html2canvas = function (element, options) {\n        if (options === void 0) { options = {}; }\n        return renderElement(element, options);\n    };\n    if (typeof window !== 'undefined') {\n        CacheStorage.setContext(window);\n    }\n    var renderElement = function (element, opts) { return __awaiter(void 0, void 0, void 0, function () {\n        var ownerDocument, defaultView, resourceOptions, contextOptions, windowOptions, windowBounds, context, foreignObjectRendering, cloneOptions, documentCloner, clonedElement, container, _a, width, height, left, top, backgroundColor, renderOptions, canvas, renderer, root, renderer;\n        var _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;\n        return __generator(this, function (_u) {\n            switch (_u.label) {\n                case 0:\n                    if (!element || typeof element !== 'object') {\n                        return [2 /*return*/, Promise.reject('Invalid element provided as first argument')];\n                    }\n                    ownerDocument = element.ownerDocument;\n                    if (!ownerDocument) {\n                        throw new Error(\"Element is not attached to a Document\");\n                    }\n                    defaultView = ownerDocument.defaultView;\n                    if (!defaultView) {\n                        throw new Error(\"Document is not attached to a Window\");\n                    }\n                    resourceOptions = {\n                        allowTaint: (_b = opts.allowTaint) !== null && _b !== void 0 ? _b : false,\n                        imageTimeout: (_c = opts.imageTimeout) !== null && _c !== void 0 ? _c : 15000,\n                        proxy: opts.proxy,\n                        useCORS: (_d = opts.useCORS) !== null && _d !== void 0 ? _d : false\n                    };\n                    contextOptions = __assign({ logging: (_e = opts.logging) !== null && _e !== void 0 ? _e : true, cache: opts.cache }, resourceOptions);\n                    windowOptions = {\n                        windowWidth: (_f = opts.windowWidth) !== null && _f !== void 0 ? _f : defaultView.innerWidth,\n                        windowHeight: (_g = opts.windowHeight) !== null && _g !== void 0 ? _g : defaultView.innerHeight,\n                        scrollX: (_h = opts.scrollX) !== null && _h !== void 0 ? _h : defaultView.pageXOffset,\n                        scrollY: (_j = opts.scrollY) !== null && _j !== void 0 ? _j : defaultView.pageYOffset\n                    };\n                    windowBounds = new Bounds(windowOptions.scrollX, windowOptions.scrollY, windowOptions.windowWidth, windowOptions.windowHeight);\n                    context = new Context(contextOptions, windowBounds);\n                    foreignObjectRendering = (_k = opts.foreignObjectRendering) !== null && _k !== void 0 ? _k : false;\n                    cloneOptions = {\n                        allowTaint: (_l = opts.allowTaint) !== null && _l !== void 0 ? _l : false,\n                        onclone: opts.onclone,\n                        ignoreElements: opts.ignoreElements,\n                        inlineImages: foreignObjectRendering,\n                        copyStyles: foreignObjectRendering\n                    };\n                    context.logger.debug(\"Starting document clone with size \" + windowBounds.width + \"x\" + windowBounds.height + \" scrolled to \" + -windowBounds.left + \",\" + -windowBounds.top);\n                    documentCloner = new DocumentCloner(context, element, cloneOptions);\n                    clonedElement = documentCloner.clonedReferenceElement;\n                    if (!clonedElement) {\n                        return [2 /*return*/, Promise.reject(\"Unable to find element in cloned iframe\")];\n                    }\n                    return [4 /*yield*/, documentCloner.toIFrame(ownerDocument, windowBounds)];\n                case 1:\n                    container = _u.sent();\n                    _a = isBodyElement(clonedElement) || isHTMLElement(clonedElement)\n                        ? parseDocumentSize(clonedElement.ownerDocument)\n                        : parseBounds(context, clonedElement), width = _a.width, height = _a.height, left = _a.left, top = _a.top;\n                    backgroundColor = parseBackgroundColor(context, clonedElement, opts.backgroundColor);\n                    renderOptions = {\n                        canvas: opts.canvas,\n                        backgroundColor: backgroundColor,\n                        scale: (_o = (_m = opts.scale) !== null && _m !== void 0 ? _m : defaultView.devicePixelRatio) !== null && _o !== void 0 ? _o : 1,\n                        x: ((_p = opts.x) !== null && _p !== void 0 ? _p : 0) + left,\n                        y: ((_q = opts.y) !== null && _q !== void 0 ? _q : 0) + top,\n                        width: (_r = opts.width) !== null && _r !== void 0 ? _r : Math.ceil(width),\n                        height: (_s = opts.height) !== null && _s !== void 0 ? _s : Math.ceil(height)\n                    };\n                    if (!foreignObjectRendering) return [3 /*break*/, 3];\n                    context.logger.debug(\"Document cloned, using foreign object rendering\");\n                    renderer = new ForeignObjectRenderer(context, renderOptions);\n                    return [4 /*yield*/, renderer.render(clonedElement)];\n                case 2:\n                    canvas = _u.sent();\n                    return [3 /*break*/, 5];\n                case 3:\n                    context.logger.debug(\"Document cloned, element located at \" + left + \",\" + top + \" with size \" + width + \"x\" + height + \" using computed rendering\");\n                    context.logger.debug(\"Starting DOM parsing\");\n                    root = parseTree(context, clonedElement);\n                    if (backgroundColor === root.styles.backgroundColor) {\n                        root.styles.backgroundColor = COLORS.TRANSPARENT;\n                    }\n                    context.logger.debug(\"Starting renderer for element at \" + renderOptions.x + \",\" + renderOptions.y + \" with size \" + renderOptions.width + \"x\" + renderOptions.height);\n                    renderer = new CanvasRenderer(context, renderOptions);\n                    return [4 /*yield*/, renderer.render(root)];\n                case 4:\n                    canvas = _u.sent();\n                    _u.label = 5;\n                case 5:\n                    if ((_t = opts.removeContainer) !== null && _t !== void 0 ? _t : true) {\n                        if (!DocumentCloner.destroy(container)) {\n                            context.logger.error(\"Cannot detach cloned iframe as it is not in the DOM anymore\");\n                        }\n                    }\n                    context.logger.debug(\"Finished rendering\");\n                    return [2 /*return*/, canvas];\n            }\n        });\n    }); };\n    var parseBackgroundColor = function (context, element, backgroundColorOverride) {\n        var ownerDocument = element.ownerDocument;\n        // http://www.w3.org/TR/css3-background/#special-backgrounds\n        var documentBackgroundColor = ownerDocument.documentElement\n            ? parseColor(context, getComputedStyle(ownerDocument.documentElement).backgroundColor)\n            : COLORS.TRANSPARENT;\n        var bodyBackgroundColor = ownerDocument.body\n            ? parseColor(context, getComputedStyle(ownerDocument.body).backgroundColor)\n            : COLORS.TRANSPARENT;\n        var defaultBackgroundColor = typeof backgroundColorOverride === 'string'\n            ? parseColor(context, backgroundColorOverride)\n            : backgroundColorOverride === null\n                ? COLORS.TRANSPARENT\n                : 0xffffffff;\n        return element === ownerDocument.documentElement\n            ? isTransparent(documentBackgroundColor)\n                ? isTransparent(bodyBackgroundColor)\n                    ? defaultBackgroundColor\n                    : bodyBackgroundColor\n                : documentBackgroundColor\n            : defaultBackgroundColor;\n    };\n\n    return html2canvas;\n\n})));\n//# sourceMappingURL=html2canvas.js.map\n\n\n//# sourceURL=webpack:///./node_modules/html2canvas/dist/html2canvas.js?");

/***/ })

}]);