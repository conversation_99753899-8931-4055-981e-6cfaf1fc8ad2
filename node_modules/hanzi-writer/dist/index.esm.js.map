{"version": 3, "file": "index.esm.js", "sources": ["../src/utils.ts", "../src/RenderState.ts", "../src/geometry.ts", "../src/models/Stroke.ts", "../src/models/Character.ts", "../src/parseCharData.ts", "../src/Positioner.ts", "../src/strokeMatches.ts", "../src/models/UserStroke.ts", "../src/Mutation.ts", "../src/characterActions.ts", "../src/quizActions.ts", "../src/Quiz.ts", "../src/renderers/svg/svgUtils.ts", "../src/renderers/StrokeRendererBase.ts", "../src/renderers/svg/StrokeRenderer.ts", "../src/renderers/svg/CharacterRenderer.ts", "../src/renderers/svg/UserStrokeRenderer.ts", "../src/renderers/svg/HanziWriterRenderer.ts", "../src/renderers/RenderTargetBase.ts", "../src/renderers/svg/RenderTarget.ts", "../src/renderers/svg/index.ts", "../src/renderers/canvas/canvasUtils.ts", "../src/renderers/canvas/StrokeRenderer.ts", "../src/renderers/canvas/CharacterRenderer.ts", "../src/renderers/canvas/renderUserStroke.ts", "../src/renderers/canvas/HanziWriterRenderer.ts", "../src/renderers/canvas/RenderTarget.ts", "../src/renderers/canvas/index.ts", "../src/defaultCharDataLoader.ts", "../src/defaultOptions.ts", "../src/LoadingManager.ts", "../src/HanziWriter.ts"], "sourcesContent": ["import { ColorObject, RecursivePartial } from './typings/types';\n\n// hacky way to get around rollup not properly setting `global` to `window` in browser\nconst globalObj = typeof window === 'undefined' ? global : window;\n\nexport const performanceNow =\n  (globalObj.performance && (() => globalObj.performance.now())) || (() => Date.now());\nexport const requestAnimationFrame =\n  globalObj.requestAnimationFrame?.bind(globalObj) ||\n  ((callback) => setTimeout(() => callback(performanceNow()), 1000 / 60));\nexport const cancelAnimationFrame =\n  globalObj.cancelAnimationFrame?.bind(globalObj) || clearTimeout;\n\n// Object.assign polyfill, because IE :/\nexport const _assign = function (target: any, ...overrides: any[]) {\n  const overrideTarget = Object(target);\n  overrides.forEach((override) => {\n    if (override != null) {\n      for (const key in override) {\n        if (Object.prototype.hasOwnProperty.call(override, key)) {\n          overrideTarget[key] = override[key];\n        }\n      }\n    }\n  });\n  return overrideTarget;\n};\n\nexport const assign = Object.assign || _assign;\n\nexport function arrLast<TValue>(arr: Array<TValue>) {\n  return arr[arr.length - 1];\n}\n\nexport const fixIndex = (index: number, length: number) => {\n  // helper to handle negative indexes in array indices\n  if (index < 0) {\n    return length + index;\n  }\n  return index;\n};\n\nexport const selectIndex = <T>(arr: Array<T>, index: number) => {\n  // helper to select item from array at index, supporting negative indexes\n  return arr[fixIndex(index, arr.length)];\n};\n\nexport function copyAndMergeDeep<T>(base: T, override: RecursivePartial<T> | undefined) {\n  const output = { ...base };\n  for (const key in override) {\n    const baseVal = base[key];\n    const overrideVal = override[key];\n    if (baseVal === overrideVal) {\n      continue;\n    }\n    if (\n      baseVal &&\n      overrideVal &&\n      typeof baseVal === 'object' &&\n      typeof overrideVal === 'object' &&\n      !Array.isArray(overrideVal)\n    ) {\n      output[key] = copyAndMergeDeep(baseVal, overrideVal);\n    } else {\n      // @ts-ignore\n      output[key] = overrideVal;\n    }\n  }\n  return output;\n}\n\n/** basically a simplified version of lodash.get, selects a key out of an object like 'a.b' from {a: {b: 7}} */\nexport function inflate(scope: string, obj: any): any {\n  const parts = scope.split('.');\n  const final: any = {};\n  let current = final;\n  for (let i = 0; i < parts.length; i++) {\n    const cap = i === parts.length - 1 ? obj : {};\n    current[parts[i]] = cap;\n    current = cap;\n  }\n  return final;\n}\n\nlet count = 0;\n\nexport function counter() {\n  count++;\n  return count;\n}\n\nexport function average(arr: number[]) {\n  const sum = arr.reduce((acc, val) => val + acc, 0);\n  return sum / arr.length;\n}\n\nexport function timeout(duration = 0) {\n  return new Promise((resolve) => setTimeout(resolve, duration));\n}\n\nexport function colorStringToVals(colorString: string): ColorObject {\n  const normalizedColor = colorString.toUpperCase().trim();\n  // based on https://stackoverflow.com/a/21648508\n  if (/^#([A-F0-9]{3}){1,2}$/.test(normalizedColor)) {\n    let hexParts = normalizedColor.substring(1).split('');\n    if (hexParts.length === 3) {\n      hexParts = [\n        hexParts[0],\n        hexParts[0],\n        hexParts[1],\n        hexParts[1],\n        hexParts[2],\n        hexParts[2],\n      ];\n    }\n    const hexStr = `${hexParts.join('')}`;\n    return {\n      r: parseInt(hexStr.slice(0, 2), 16),\n      g: parseInt(hexStr.slice(2, 4), 16),\n      b: parseInt(hexStr.slice(4, 6), 16),\n      a: 1,\n    };\n  }\n  const rgbMatch = normalizedColor.match(\n    /^RGBA?\\((\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)(?:\\s*,\\s*(\\d*\\.?\\d+))?\\)$/,\n  );\n  if (rgbMatch) {\n    return {\n      r: parseInt(rgbMatch[1], 10),\n      g: parseInt(rgbMatch[2], 10),\n      b: parseInt(rgbMatch[3], 10),\n      // @ts-expect-error ts-migrate(2554) FIXME: Expected 1 arguments, but got 2.\n      a: parseFloat(rgbMatch[4] || 1, 10),\n    };\n  }\n  throw new Error(`Invalid color: ${colorString}`);\n}\n\nexport const trim = (string: string) => string.replace(/^\\s+/, '').replace(/\\s+$/, '');\n\n// return a new array-like object with int keys where each key is item\n// ex: objRepeat({x: 8}, 3) === {0: {x: 8}, 1: {x: 8}, 2: {x: 8}}\nexport function objRepeat<T>(item: T, times: number) {\n  const obj: Record<number, T> = {};\n  for (let i = 0; i < times; i++) {\n    obj[i] = item;\n  }\n  return obj;\n}\n\n// similar to objRepeat, but takes in a callback which is called for each index in the object\nexport function objRepeatCb<T>(times: number, cb: (i: number) => T) {\n  const obj: Record<number, T> = {};\n  for (let i = 0; i < times; i++) {\n    obj[i] = cb(i);\n  }\n  return obj;\n}\n\nconst ua = globalObj.navigator?.userAgent || '';\n\nexport const isMsBrowser =\n  ua.indexOf('MSIE ') > 0 || ua.indexOf('Trident/') > 0 || ua.indexOf('Edge/') > 0;\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport const noop = () => {};\n", "import Character from './models/Character';\nimport { GenericMutation } from './Mutation';\nimport {\n  ColorObject,\n  OnCompleteFunction,\n  Point,\n  RecursivePartial,\n} from './typings/types';\nimport { copyAndMergeDeep, colorStringToVals, noop } from './utils';\n\nexport type StrokeRenderState = {\n  opacity: number;\n  displayPortion: number;\n};\n\nexport type CharacterRenderState = {\n  opacity: number;\n  strokes: Record<number | string, StrokeRenderState>;\n};\n\nexport type RenderStateObject = {\n  options: {\n    drawingFadeDuration: number;\n    drawingWidth: number;\n    drawingColor: ColorObject;\n    strokeColor: ColorObject;\n    outlineColor: ColorObject;\n    radicalColor: ColorObject;\n    highlightColor: ColorObject;\n  };\n  character: {\n    main: CharacterRenderState;\n    outline: CharacterRenderState;\n    highlight: CharacterRenderState;\n  };\n  userStrokes: Record<\n    string,\n    | {\n        points: Point[];\n        opacity: number;\n      }\n    | undefined\n  > | null;\n};\n\nexport type CharacterName = keyof RenderStateObject['character'];\n\ntype OnStateChangeCallback = (\n  nextState: RenderStateObject,\n  currentState: RenderStateObject,\n) => void;\n\ntype MutationChain = {\n  _isActive: boolean;\n  _index: number;\n  _resolve: OnCompleteFunction;\n  _mutations: GenericMutation[];\n  _loop: boolean | undefined;\n  _scopes: string[];\n};\n\nexport type RenderStateOptions = {\n  strokeColor: string;\n  radicalColor: string | null;\n  highlightColor: string;\n  outlineColor: string;\n  drawingColor: string;\n  drawingFadeDuration: number;\n  drawingWidth: number;\n  outlineWidth: number;\n  showCharacter: boolean;\n  showOutline: boolean;\n};\n\nexport default class RenderState {\n  _mutationChains: MutationChain[] = [];\n  _onStateChange: OnStateChangeCallback;\n\n  state: RenderStateObject;\n\n  constructor(\n    character: Character,\n    options: RenderStateOptions,\n    onStateChange: OnStateChangeCallback = noop,\n  ) {\n    this._onStateChange = onStateChange;\n\n    this.state = {\n      options: {\n        drawingFadeDuration: options.drawingFadeDuration,\n        drawingWidth: options.drawingWidth,\n        drawingColor: colorStringToVals(options.drawingColor),\n        strokeColor: colorStringToVals(options.strokeColor),\n        outlineColor: colorStringToVals(options.outlineColor),\n        radicalColor: colorStringToVals(options.radicalColor || options.strokeColor),\n        highlightColor: colorStringToVals(options.highlightColor),\n      },\n      character: {\n        main: {\n          opacity: options.showCharacter ? 1 : 0,\n          strokes: {},\n        },\n        outline: {\n          opacity: options.showOutline ? 1 : 0,\n          strokes: {},\n        },\n        highlight: {\n          opacity: 1,\n          strokes: {},\n        },\n      },\n      userStrokes: null,\n    };\n\n    for (let i = 0; i < character.strokes.length; i++) {\n      this.state.character.main.strokes[i] = {\n        opacity: 1,\n        displayPortion: 1,\n      };\n\n      this.state.character.outline.strokes[i] = {\n        opacity: 1,\n        displayPortion: 1,\n      };\n\n      this.state.character.highlight.strokes[i] = {\n        opacity: 0,\n        displayPortion: 1,\n      };\n    }\n  }\n\n  overwriteOnStateChange(onStateChange: OnStateChangeCallback) {\n    this._onStateChange = onStateChange;\n  }\n\n  updateState(stateChanges: RecursivePartial<RenderStateObject>) {\n    const nextState = copyAndMergeDeep(this.state, stateChanges);\n    this._onStateChange(nextState, this.state);\n    this.state = nextState;\n  }\n\n  run(\n    mutations: GenericMutation[],\n    options: {\n      loop?: boolean;\n    } = {},\n  ) {\n    const scopes = mutations.map((mut) => mut.scope);\n\n    this.cancelMutations(scopes);\n\n    return new Promise((resolve: OnCompleteFunction) => {\n      const mutationChain: MutationChain = {\n        _isActive: true,\n        _index: 0,\n        _resolve: resolve,\n        _mutations: mutations,\n        _loop: options.loop,\n        _scopes: scopes,\n      };\n      this._mutationChains.push(mutationChain);\n      this._run(mutationChain);\n    });\n  }\n\n  _run(mutationChain: MutationChain) {\n    if (!mutationChain._isActive) {\n      return;\n    }\n\n    const mutations = mutationChain._mutations;\n    if (mutationChain._index >= mutations.length) {\n      if (mutationChain._loop) {\n        mutationChain._index = 0; // eslint-disable-line no-param-reassign\n      } else {\n        mutationChain._isActive = false; // eslint-disable-line no-param-reassign\n        this._mutationChains = this._mutationChains.filter(\n          (chain) => chain !== mutationChain,\n        );\n        // The chain is done - resolve the promise to signal it finished successfully\n        mutationChain._resolve({ canceled: false });\n        return;\n      }\n    }\n\n    const activeMutation = mutationChain._mutations[mutationChain._index];\n\n    activeMutation.run(this).then(() => {\n      if (mutationChain._isActive) {\n        mutationChain._index++; // eslint-disable-line no-param-reassign\n        this._run(mutationChain);\n      }\n    });\n  }\n\n  _getActiveMutations() {\n    return this._mutationChains.map((chain) => chain._mutations[chain._index]);\n  }\n\n  pauseAll() {\n    this._getActiveMutations().forEach((mutation) => mutation.pause());\n  }\n\n  resumeAll() {\n    this._getActiveMutations().forEach((mutation) => mutation.resume());\n  }\n\n  cancelMutations(scopesToCancel: string[]) {\n    for (const chain of this._mutationChains) {\n      for (const chainId of chain._scopes) {\n        for (const scopeToCancel of scopesToCancel) {\n          if (chainId.startsWith(scopeToCancel) || scopeToCancel.startsWith(chainId)) {\n            this._cancelMutationChain(chain);\n          }\n        }\n      }\n    }\n  }\n\n  cancelAll() {\n    this.cancelMutations(['']);\n  }\n\n  _cancelMutationChain(mutationChain: MutationChain) {\n    mutationChain._isActive = false;\n    for (let i = mutationChain._index; i < mutationChain._mutations.length; i++) {\n      mutationChain._mutations[i].cancel(this);\n    }\n\n    mutationChain._resolve?.({ canceled: true });\n\n    this._mutationChains = this._mutationChains.filter(\n      (chain) => chain !== mutationChain,\n    );\n  }\n}\n", "import { Point } from './typings/types';\nimport { average, arrLast } from './utils';\n\nexport const subtract = (p1: Point, p2: Point) => ({ x: p1.x - p2.x, y: p1.y - p2.y });\n\nexport const magnitude = (point: Point) =>\n  Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));\n\nexport const distance = (point1: Point, point2: Point) =>\n  magnitude(subtract(point1, point2));\n\nexport const equals = (point1: Point, point2: Point) =>\n  point1.x === point2.x && point1.y === point2.y;\n\nexport const round = (point: Point, precision = 1) => {\n  const multiplier = precision * 10;\n  return {\n    x: Math.round(multiplier * point.x) / multiplier,\n    y: Math.round(multiplier * point.y) / multiplier,\n  };\n};\n\nexport const length = (points: Point[]) => {\n  let lastPoint = points[0];\n  const pointsSansFirst = points.slice(1);\n  return pointsSansFirst.reduce((acc, point) => {\n    const dist = distance(point, lastPoint);\n    lastPoint = point;\n    return acc + dist;\n  }, 0);\n};\n\nexport const cosineSimilarity = (point1: Point, point2: Point) => {\n  const rawDotProduct = point1.x * point2.x + point1.y * point2.y;\n  return rawDotProduct / magnitude(point1) / magnitude(point2);\n};\n\n/**\n * return a new point, p3, which is on the same line as p1 and p2, but distance away\n * from p2. p1, p2, p3 will always lie on the line in that order\n */\nexport const _extendPointOnLine = (p1: Point, p2: Point, dist: number) => {\n  const vect = subtract(p2, p1);\n  const norm = dist / magnitude(vect);\n  return { x: p2.x + norm * vect.x, y: p2.y + norm * vect.y };\n};\n\n/** based on http://www.kr.tuwien.ac.at/staff/eiter/et-archive/cdtr9464.pdf */\nexport const frechetDist = (curve1: Point[], curve2: Point[]) => {\n  const longCurve = curve1.length >= curve2.length ? curve1 : curve2;\n  const shortCurve = curve1.length >= curve2.length ? curve2 : curve1;\n\n  const calcVal = (\n    i: number,\n    j: number,\n    prevResultsCol: number[],\n    curResultsCol: number[],\n  ): number => {\n    if (i === 0 && j === 0) {\n      return distance(longCurve[0], shortCurve[0]);\n    }\n\n    if (i > 0 && j === 0) {\n      return Math.max(prevResultsCol[0], distance(longCurve[i], shortCurve[0]));\n    }\n\n    const lastResult = curResultsCol[curResultsCol.length - 1];\n\n    if (i === 0 && j > 0) {\n      return Math.max(lastResult, distance(longCurve[0], shortCurve[j]));\n    }\n\n    return Math.max(\n      Math.min(prevResultsCol[j], prevResultsCol[j - 1], lastResult),\n      distance(longCurve[i], shortCurve[j]),\n    );\n  };\n\n  let prevResultsCol: number[] = [];\n  for (let i = 0; i < longCurve.length; i++) {\n    const curResultsCol: number[] = [];\n    for (let j = 0; j < shortCurve.length; j++) {\n      // we only need the results from i - 1 and j - 1 to continue the calculation\n      // so we only need to hold onto the last column of calculated results\n      // prevResultsCol is results[i-1][:] in the original algorithm\n      // curResultsCol is results[i][:j-1] in the original algorithm\n      curResultsCol.push(calcVal(i, j, prevResultsCol, curResultsCol));\n    }\n    prevResultsCol = curResultsCol;\n  }\n\n  return prevResultsCol[shortCurve.length - 1];\n};\n\n/** break up long segments in the curve into smaller segments of len maxLen or smaller */\nexport const subdivideCurve = (curve: Point[], maxLen = 0.05) => {\n  const newCurve = curve.slice(0, 1);\n\n  for (const point of curve.slice(1)) {\n    const prevPoint = newCurve[newCurve.length - 1];\n    const segLen = distance(point, prevPoint);\n    if (segLen > maxLen) {\n      const numNewPoints = Math.ceil(segLen / maxLen);\n      const newSegLen = segLen / numNewPoints;\n      for (let i = 0; i < numNewPoints; i++) {\n        newCurve.push(_extendPointOnLine(point, prevPoint, -1 * newSegLen * (i + 1)));\n      }\n    } else {\n      newCurve.push(point);\n    }\n  }\n\n  return newCurve;\n};\n\n/** redraw the curve using numPoints equally spaced out along the length of the curve */\nexport const outlineCurve = (curve: Point[], numPoints = 30) => {\n  const curveLen = length(curve);\n  const segmentLen = curveLen / (numPoints - 1);\n  const outlinePoints = [curve[0]];\n  const endPoint = arrLast(curve);\n  const remainingCurvePoints = curve.slice(1);\n\n  for (let i = 0; i < numPoints - 2; i++) {\n    let lastPoint: Point = arrLast(outlinePoints);\n    let remainingDist = segmentLen;\n    let outlinePointFound = false;\n    while (!outlinePointFound) {\n      const nextPointDist = distance(lastPoint, remainingCurvePoints[0]);\n      if (nextPointDist < remainingDist) {\n        remainingDist -= nextPointDist;\n        lastPoint = remainingCurvePoints.shift()!;\n      } else {\n        const nextPoint = _extendPointOnLine(\n          lastPoint,\n          remainingCurvePoints[0],\n          remainingDist - nextPointDist,\n        );\n        outlinePoints.push(nextPoint);\n        outlinePointFound = true;\n      }\n    }\n  }\n\n  outlinePoints.push(endPoint);\n\n  return outlinePoints;\n};\n\n/** translate and scale from https://en.wikipedia.org/wiki/Procrustes_analysis */\nexport const normalizeCurve = (curve: Point[]) => {\n  const outlinedCurve = outlineCurve(curve);\n  const meanX = average(outlinedCurve.map((point) => point.x));\n  const meanY = average(outlinedCurve.map((point) => point.y));\n  const mean = { x: meanX, y: meanY };\n  const translatedCurve = outlinedCurve.map((point) => subtract(point, mean));\n  const scale = Math.sqrt(\n    average([\n      Math.pow(translatedCurve[0].x, 2) + Math.pow(translatedCurve[0].y, 2),\n      Math.pow(arrLast(translatedCurve).x, 2) + Math.pow(arrLast(translatedCurve).y, 2),\n    ]),\n  );\n  const scaledCurve = translatedCurve.map((point) => ({\n    x: point.x / scale,\n    y: point.y / scale,\n  }));\n  return subdivideCurve(scaledCurve);\n};\n\n// rotate around the origin\nexport const rotate = (curve: Point[], theta: number) => {\n  return curve.map((point) => ({\n    x: Math.cos(theta) * point.x - Math.sin(theta) * point.y,\n    y: Math.sin(theta) * point.x + Math.cos(theta) * point.y,\n  }));\n};\n\n// remove intermediate points that are on the same line as the points to either side\nexport const _filterParallelPoints = (points: Point[]) => {\n  if (points.length < 3) return points;\n  const filteredPoints = [points[0], points[1]];\n  points.slice(2).forEach((point) => {\n    const numFilteredPoints = filteredPoints.length;\n    const curVect = subtract(point, filteredPoints[numFilteredPoints - 1]);\n    const prevVect = subtract(\n      filteredPoints[numFilteredPoints - 1],\n      filteredPoints[numFilteredPoints - 2],\n    );\n    // this is the z coord of the cross-product. If this is 0 then they're parallel\n    const isParallel = curVect.y * prevVect.x - curVect.x * prevVect.y === 0;\n    if (isParallel) {\n      filteredPoints.pop();\n    }\n    filteredPoints.push(point);\n  });\n  return filteredPoints;\n};\n\nexport function getPathString(points: Point[], close = false) {\n  const start = round(points[0]);\n  const remainingPoints = points.slice(1);\n  let pathString = `M ${start.x} ${start.y}`;\n  remainingPoints.forEach((point) => {\n    const roundedPoint = round(point);\n    pathString += ` L ${roundedPoint.x} ${roundedPoint.y}`;\n  });\n  if (close) {\n    pathString += 'Z';\n  }\n  return pathString;\n}\n\n/** take points on a path and move their start point backwards by distance */\nexport const extendStart = (points: Point[], dist: number) => {\n  const filteredPoints = _filterParallelPoints(points);\n  if (filteredPoints.length < 2) return filteredPoints;\n  const p1 = filteredPoints[1];\n  const p2 = filteredPoints[0];\n  const newStart = _extendPointOnLine(p1, p2, dist);\n  const extendedPoints = filteredPoints.slice(1);\n  extendedPoints.unshift(newStart);\n  return extendedPoints;\n};\n", "import { subtract, distance, length } from '../geometry';\nimport { Point } from '../typings/types';\n\nexport default class Stroke {\n  path: string;\n  points: Point[];\n  strokeNum: number;\n  isInRadical: boolean;\n\n  constructor(path: string, points: Point[], strokeNum: number, isInRadical = false) {\n    this.path = path;\n    this.points = points;\n    this.strokeNum = strokeNum;\n    this.isInRadical = isInRadical;\n  }\n\n  getStartingPoint() {\n    return this.points[0];\n  }\n\n  getEndingPoint() {\n    return this.points[this.points.length - 1];\n  }\n\n  getLength(): number {\n    return length(this.points);\n  }\n\n  getVectors() {\n    let lastPoint = this.points[0];\n    const pointsSansFirst = this.points.slice(1);\n    return pointsSansFirst.map((point) => {\n      const vector = subtract(point, lastPoint);\n      lastPoint = point;\n      return vector;\n    });\n  }\n\n  getDistance(point: Point) {\n    const distances = this.points.map((strokePoint) => distance(strokePoint, point));\n    return Math.min(...distances);\n  }\n\n  getAverageDistance(points: Point[]) {\n    const totalDist = points.reduce((acc, point) => acc + this.getDistance(point), 0);\n    return totalDist / points.length;\n  }\n}\n", "import Stroke from './Stroke';\n\nexport default class Character {\n  symbol: string;\n  strokes: Stroke[];\n\n  constructor(symbol: string, strokes: Stroke[]) {\n    this.symbol = symbol;\n    this.strokes = strokes;\n  }\n}\n", "import Stroke from './models/Stroke';\nimport Character from './models/Character';\nimport { CharacterJson } from './typings/types';\n\nfunction generateStrokes({ radStrokes, strokes, medians }: CharacterJson) {\n  const isInRadical = (strokeNum: number) => (radStrokes?.indexOf(strokeNum) ?? -1) >= 0;\n  return strokes.map((path, index) => {\n    const points = medians[index].map((pointData) => {\n      const [x, y] = pointData;\n      return { x, y };\n    });\n    return new Stroke(path, points, index, isInRadical(index));\n  });\n}\n\nexport default function parseCharData(symbol: string, charJson: CharacterJson) {\n  const strokes = generateStrokes(charJson);\n  return new Character(symbol, strokes);\n}\n", "import { Point } from './typings/types';\n\n// All makemeahanzi characters have the same bounding box\nconst CHARACTER_BOUNDS = [\n  { x: 0, y: -124 },\n  { x: 1024, y: 900 },\n];\nconst [from, to] = CHARACTER_BOUNDS;\nconst preScaledWidth = to.x - from.x;\nconst preScaledHeight = to.y - from.y;\n\nexport type PositionerOptions = {\n  /** Default: 0 */\n  width: number;\n  /** Default: 0 */\n  height: number;\n  /** Default: 20 */\n  padding: number;\n};\n\nexport default class Positioner {\n  padding: number;\n  width: number;\n  height: number;\n  xOffset: number;\n  yOffset: number;\n  scale: number;\n\n  constructor(options: PositionerOptions) {\n    const { padding, width, height } = options;\n    this.padding = padding;\n    this.width = width;\n    this.height = height;\n\n    const effectiveWidth = width - 2 * padding;\n    const effectiveHeight = height - 2 * padding;\n    const scaleX = effectiveWidth / preScaledWidth;\n    const scaleY = effectiveHeight / preScaledHeight;\n\n    this.scale = Math.min(scaleX, scaleY);\n\n    const xCenteringBuffer = padding + (effectiveWidth - this.scale * preScaledWidth) / 2;\n    const yCenteringBuffer =\n      padding + (effectiveHeight - this.scale * preScaledHeight) / 2;\n\n    this.xOffset = -1 * from.x * this.scale + xCenteringBuffer;\n    this.yOffset = -1 * from.y * this.scale + yCenteringBuffer;\n  }\n\n  convertExternalPoint(point: Point) {\n    const x = (point.x - this.xOffset) / this.scale;\n    const y = (this.height - this.yOffset - point.y) / this.scale;\n    return { x, y };\n  }\n}\n", "import { average } from './utils';\nimport {\n  cosineSimilarity,\n  equals,\n  frechetDist,\n  distance,\n  subtract,\n  normalizeCurve,\n  rotate,\n  length,\n} from './geometry';\nimport { Point } from './typings/types';\nimport UserStroke from './models/UserStroke';\nimport Stroke from './models/Stroke';\nimport Character from './models/Character';\n\nconst COSINE_SIMILARITY_THRESHOLD = 0; // -1 to 1, smaller = more lenient\nconst START_AND_END_DIST_THRESHOLD = 250; // bigger = more lenient\nconst FRECHET_THRESHOLD = 0.4; // bigger = more lenient\nconst MIN_LEN_THRESHOLD = 0.35; // smaller = more lenient\n\nexport interface StrokeMatchResultMeta {\n  isStrokeBackwards: boolean;\n}\n\nexport interface StrokeMatchResult {\n  isMatch: boolean;\n  meta: StrokeMatchResultMeta;\n}\n\nexport default function strokeMatches(\n  userStroke: UserStroke,\n  character: Character,\n  strokeNum: number,\n  options: {\n    leniency?: number;\n    isOutlineVisible?: boolean;\n    averageDistanceThreshold?: number;\n  } = {},\n): StrokeMatchResult {\n  const strokes = character.strokes;\n  const points = stripDuplicates(userStroke.points);\n\n  if (points.length < 2) {\n    return { isMatch: false, meta: { isStrokeBackwards: false } };\n  }\n\n  const { isMatch, meta, avgDist } = getMatchData(points, strokes[strokeNum], options);\n\n  if (!isMatch) {\n    return { isMatch, meta };\n  }\n\n  // if there is a better match among strokes the user hasn't drawn yet, the user probably drew the wrong stroke\n  const laterStrokes = strokes.slice(strokeNum + 1);\n  let closestMatchDist = avgDist;\n\n  for (let i = 0; i < laterStrokes.length; i++) {\n    const { isMatch, avgDist } = getMatchData(points, laterStrokes[i], {\n      ...options,\n      checkBackwards: false,\n    });\n    if (isMatch && avgDist < closestMatchDist) {\n      closestMatchDist = avgDist;\n    }\n  }\n  // if there's a better match, rather that returning false automatically, try reducing leniency instead\n  // if leniency is already really high we can allow some similar strokes to pass\n  if (closestMatchDist < avgDist) {\n    // adjust leniency between 0.3 and 0.6 depending on how much of a better match the new match is\n    const leniencyAdjustment = (0.6 * (closestMatchDist + avgDist)) / (2 * avgDist);\n    const { isMatch, meta } = getMatchData(points, strokes[strokeNum], {\n      ...options,\n      leniency: (options.leniency || 1) * leniencyAdjustment,\n    });\n    return { isMatch, meta };\n  }\n\n  return { isMatch, meta };\n}\n\nconst startAndEndMatches = (points: Point[], closestStroke: Stroke, leniency: number) => {\n  const startingDist = distance(closestStroke.getStartingPoint(), points[0]);\n  const endingDist = distance(closestStroke.getEndingPoint(), points[points.length - 1]);\n  return (\n    startingDist <= START_AND_END_DIST_THRESHOLD * leniency &&\n    endingDist <= START_AND_END_DIST_THRESHOLD * leniency\n  );\n};\n\n// returns a list of the direction of all segments in the line connecting the points\nconst getEdgeVectors = (points: Point[]) => {\n  const vectors: Point[] = [];\n  let lastPoint = points[0];\n  points.slice(1).forEach((point) => {\n    vectors.push(subtract(point, lastPoint));\n    lastPoint = point;\n  });\n  return vectors;\n};\n\nconst directionMatches = (points: Point[], stroke: Stroke) => {\n  const edgeVectors = getEdgeVectors(points);\n  const strokeVectors = stroke.getVectors();\n  const similarities = edgeVectors.map((edgeVector) => {\n    const strokeSimilarities = strokeVectors.map((strokeVector) =>\n      cosineSimilarity(strokeVector, edgeVector),\n    );\n    return Math.max(...strokeSimilarities);\n  });\n  const avgSimilarity = average(similarities);\n  return avgSimilarity > COSINE_SIMILARITY_THRESHOLD;\n};\n\nconst lengthMatches = (points: Point[], stroke: Stroke, leniency: number) => {\n  return (\n    (leniency * (length(points) + 25)) / (stroke.getLength() + 25) >= MIN_LEN_THRESHOLD\n  );\n};\n\nconst stripDuplicates = (points: Point[]) => {\n  if (points.length < 2) return points;\n  const [firstPoint, ...rest] = points;\n  const dedupedPoints = [firstPoint];\n\n  for (const point of rest) {\n    if (!equals(point, dedupedPoints[dedupedPoints.length - 1])) {\n      dedupedPoints.push(point);\n    }\n  }\n\n  return dedupedPoints;\n};\n\nconst SHAPE_FIT_ROTATIONS = [\n  Math.PI / 16,\n  Math.PI / 32,\n  0,\n  (-1 * Math.PI) / 32,\n  (-1 * Math.PI) / 16,\n];\n\nconst shapeFit = (curve1: Point[], curve2: Point[], leniency: number) => {\n  const normCurve1 = normalizeCurve(curve1);\n  const normCurve2 = normalizeCurve(curve2);\n  let minDist = Infinity;\n  SHAPE_FIT_ROTATIONS.forEach((theta) => {\n    const dist = frechetDist(normCurve1, rotate(normCurve2, theta));\n    if (dist < minDist) {\n      minDist = dist;\n    }\n  });\n  return minDist <= FRECHET_THRESHOLD * leniency;\n};\n\nconst getMatchData = (\n  points: Point[],\n  stroke: Stroke,\n  options: {\n    leniency?: number;\n    isOutlineVisible?: boolean;\n    checkBackwards?: boolean;\n    averageDistanceThreshold?: number;\n  },\n): StrokeMatchResult & { avgDist: number } => {\n  const {\n    leniency = 1,\n    isOutlineVisible = false,\n    checkBackwards = true,\n    averageDistanceThreshold = 350,\n  } = options;\n  const avgDist = stroke.getAverageDistance(points);\n  const distMod = isOutlineVisible || stroke.strokeNum > 0 ? 0.5 : 1;\n  const withinDistThresh = avgDist <= averageDistanceThreshold * distMod * leniency;\n  // short circuit for faster matching\n  if (!withinDistThresh) {\n    return { isMatch: false, avgDist, meta: { isStrokeBackwards: false } };\n  }\n  const startAndEndMatch = startAndEndMatches(points, stroke, leniency);\n  const directionMatch = directionMatches(points, stroke);\n  const shapeMatch = shapeFit(points, stroke.points, leniency);\n  const lengthMatch = lengthMatches(points, stroke, leniency);\n\n  const isMatch =\n    withinDistThresh && startAndEndMatch && directionMatch && shapeMatch && lengthMatch;\n\n  if (checkBackwards && !isMatch) {\n    const backwardsMatchData = getMatchData([...points].reverse(), stroke, {\n      ...options,\n      checkBackwards: false,\n    });\n\n    if (backwardsMatchData.isMatch) {\n      return {\n        isMatch,\n        avgDist,\n        meta: { isStrokeBackwards: true },\n      };\n    }\n  }\n\n  return { isMatch, avgDist, meta: { isStrokeBackwards: false } };\n};\n", "import { Point } from '../typings/types';\n\nexport default class UserStroke {\n  id: number;\n  points: Point[];\n  externalPoints: Point[];\n\n  constructor(id: number, startingPoint: Point, startingExternalPoint: Point) {\n    this.id = id;\n    this.points = [startingPoint];\n    this.externalPoints = [startingExternalPoint];\n  }\n\n  appendPoint(point: Point, externalPoint: Point) {\n    this.points.push(point);\n    this.externalPoints.push(externalPoint);\n  }\n}\n", "import {\n  cancelAnimation<PERSON>rame,\n  requestAnimationFrame,\n  inflate,\n  performanceNow,\n} from './utils';\nimport RenderState from './RenderState';\nimport { RecursivePartial } from './typings/types';\n\n/** Used by `Mutation` & `Delay` */\nexport interface GenericMutation<\n  TRenderStateClass extends GenericRenderStateClass = RenderState\n> {\n  /** Allows mutations starting with the provided string to be cancelled */\n  scope: string;\n  /** Can be useful for checking whether the mutation is running */\n  _runningPromise: Promise<void> | undefined;\n  run(renderState: TRenderStateClass): Promise<void>;\n  pause(): void;\n  resume(): void;\n  cancel(renderState: TRenderStateClass): void;\n}\n\nclass Delay implements GenericMutation {\n  scope: string;\n  _runningPromise: Promise<void> | undefined;\n  _duration: number;\n  _startTime: number | null;\n  _paused: boolean;\n  _timeout!: NodeJS.Timeout;\n  _resolve: (() => void) | undefined;\n\n  constructor(duration: number) {\n    this._duration = duration;\n    this._startTime = null;\n    this._paused = false;\n    this.scope = `delay.${duration}`;\n  }\n\n  run() {\n    this._startTime = performanceNow();\n    this._runningPromise = new Promise((resolve) => {\n      this._resolve = resolve;\n      // @ts-ignore return type of \"setTimeout\" in builds is parsed as `number` instead of `Timeout`\n      this._timeout = setTimeout(() => this.cancel(), this._duration);\n    }) as Promise<void>;\n    return this._runningPromise;\n  }\n\n  pause() {\n    if (this._paused) return;\n    // to pause, clear the timeout and rewrite this._duration with whatever time is remaining\n    const elapsedDelay = performance.now() - (this._startTime || 0);\n    this._duration = Math.max(0, this._duration - elapsedDelay);\n    clearTimeout(this._timeout);\n    this._paused = true;\n  }\n\n  resume() {\n    if (!this._paused) return;\n    this._startTime = performance.now();\n    // @ts-ignore return type of \"setTimeout\" in builds is parsed as `number` instead of `Timeout`\n    this._timeout = setTimeout(() => this.cancel(), this._duration);\n    this._paused = false;\n  }\n\n  cancel() {\n    clearTimeout(this._timeout!);\n    if (this._resolve) {\n      this._resolve();\n    }\n    this._resolve = undefined;\n  }\n}\n\ntype GenericRenderStateClass<T = any> = {\n  state: T;\n  updateState(changes: RecursivePartial<T>): void;\n};\n\nexport default class Mutation<\n  TRenderStateClass extends GenericRenderStateClass,\n  TRenderStateObj = TRenderStateClass['state']\n> implements GenericMutation<TRenderStateClass> {\n  static Delay = Delay;\n\n  scope: string;\n  _runningPromise: Promise<void> | undefined;\n  _valuesOrCallable: any;\n  _duration: number;\n  _force: boolean | undefined;\n  _pausedDuration: number;\n  _startPauseTime: number | null;\n\n  // Only set on .run()\n  _startTime: number | undefined;\n  _startState: RecursivePartial<TRenderStateObj> | undefined;\n  _renderState: TRenderStateClass | undefined;\n  _frameHandle: number | undefined;\n  _values: RecursivePartial<TRenderStateObj> | undefined;\n  _resolve: ((_val?: any) => void) | undefined;\n\n  /**\n   *\n   * @param scope a string representation of what fields this mutation affects from the state. This is used to cancel conflicting mutations\n   * @param valuesOrCallable a thunk containing the value to set, or a callback which will return those values\n   */\n  constructor(\n    scope: string,\n    valuesOrCallable: any,\n    options: {\n      duration?: number;\n      /** Updates render state regardless if cancelled */\n      force?: boolean;\n    } = {},\n  ) {\n    this.scope = scope;\n    this._valuesOrCallable = valuesOrCallable;\n    this._duration = options.duration || 0;\n    this._force = options.force;\n    this._pausedDuration = 0;\n    this._startPauseTime = null;\n  }\n\n  run(renderState: TRenderStateClass): Promise<void> {\n    if (!this._values) this._inflateValues(renderState);\n    if (this._duration === 0) renderState.updateState(this._values!);\n    if (this._duration === 0 || isAlreadyAtEnd(renderState.state, this._values)) {\n      return Promise.resolve();\n    }\n    this._renderState = renderState;\n    this._startState = renderState.state;\n    this._startTime = performance.now();\n    this._frameHandle = requestAnimationFrame(this._tick);\n    return new Promise((resolve) => {\n      this._resolve = resolve;\n    });\n  }\n\n  private _inflateValues(renderState: TRenderStateClass) {\n    let values = this._valuesOrCallable;\n    if (typeof this._valuesOrCallable === 'function') {\n      values = this._valuesOrCallable(renderState.state);\n    }\n    this._values = inflate(this.scope, values);\n  }\n\n  pause() {\n    if (this._startPauseTime !== null) {\n      return;\n    }\n    if (this._frameHandle) {\n      cancelAnimationFrame(this._frameHandle);\n    }\n    this._startPauseTime = performance.now();\n  }\n\n  resume() {\n    if (this._startPauseTime === null) {\n      return;\n    }\n    this._frameHandle = requestAnimationFrame(this._tick);\n    this._pausedDuration += performance.now() - this._startPauseTime;\n    this._startPauseTime = null;\n  }\n\n  private _tick = (timing: number) => {\n    if (this._startPauseTime !== null) {\n      return;\n    }\n\n    const progress = Math.min(\n      1,\n      (timing - this._startTime! - this._pausedDuration) / this._duration,\n    );\n\n    if (progress === 1) {\n      this._renderState!.updateState(this._values!);\n      this._frameHandle = undefined;\n      this.cancel(this._renderState!);\n    } else {\n      const easedProgress = ease(progress);\n      const stateChanges = getPartialValues(\n        this._startState as TRenderStateObj,\n        this._values!,\n        easedProgress,\n      );\n\n      this._renderState!.updateState(stateChanges);\n      this._frameHandle = requestAnimationFrame(this._tick);\n    }\n  };\n\n  cancel(renderState: TRenderStateClass) {\n    this._resolve?.();\n    this._resolve = undefined;\n\n    cancelAnimationFrame(this._frameHandle || -1);\n    this._frameHandle = undefined;\n\n    if (this._force) {\n      if (!this._values) this._inflateValues(renderState);\n      renderState.updateState(this._values!);\n    }\n  }\n}\n\nfunction getPartialValues<T>(\n  startValues: T | undefined,\n  endValues: RecursivePartial<T> | undefined,\n  progress: number,\n) {\n  const target: RecursivePartial<T> = {};\n\n  for (const key in endValues) {\n    const endValue = endValues[key];\n    const startValue = startValues?.[key];\n    if (typeof startValue === 'number' && typeof endValue === 'number' && endValue >= 0) {\n      target[key] = progress * (endValue - startValue) + startValue;\n    } else {\n      target[key] = getPartialValues(startValue, endValue, progress);\n    }\n  }\n  return target;\n}\n\nfunction isAlreadyAtEnd<T>(\n  startValues: T | undefined,\n  endValues: RecursivePartial<T> | undefined,\n) {\n  for (const key in endValues) {\n    const endValue = endValues[key];\n    const startValue = startValues?.[key];\n    if (endValue >= 0) {\n      if (endValue !== startValue) {\n        return false;\n      }\n    } else if (!isAlreadyAtEnd(startValue, endValue)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// from https://github.com/maxwellito/vivus\nconst ease = (x: number) => -Math.cos(x * Math.PI) / 2 + 0.5;\n", "import Stroke from './models/Stroke';\nimport { ColorObject, RecursivePartial } from './typings/types';\nimport Character from './models/Character';\nimport Mutation, { GenericMutation } from './Mutation';\nimport { objRepeat } from './utils';\nimport { CharacterName, CharacterRenderState, RenderStateObject } from './RenderState';\n\nexport const showStrokes = (\n  charName: CharacterName,\n  character: Character,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}.strokes`,\n      objRepeat(\n        { opacity: 1, displayPortion: 1 },\n        character.strokes.length,\n      ) as CharacterRenderState['strokes'],\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const showCharacter = (\n  charName: CharacterName,\n  character: Character,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}`,\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length),\n      },\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const hideCharacter = (\n  charName: CharacterName,\n  character: Character,\n  duration?: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(`character.${charName}.opacity`, 0, { duration, force: true }),\n    ...showStrokes(charName, character, 0),\n  ];\n};\n\nexport const updateColor = (\n  colorName: string,\n  colorVal: ColorObject | null,\n  duration: number,\n) => {\n  return [new Mutation(`options.${colorName}`, colorVal, { duration })];\n};\n\nexport const highlightStroke = (\n  stroke: Stroke,\n  color: ColorObject | null,\n  speed: number,\n): GenericMutation[] => {\n  const strokeNum = stroke.strokeNum;\n  const duration = (stroke.getLength() + 600) / (3 * speed);\n  return [\n    new Mutation('options.highlightColor', color),\n    new Mutation('character.highlight', {\n      opacity: 1,\n      strokes: {\n        [strokeNum]: {\n          displayPortion: 0,\n          opacity: 0,\n        },\n      },\n    }),\n    new Mutation(\n      `character.highlight.strokes.${strokeNum}`,\n      {\n        displayPortion: 1,\n        opacity: 1,\n      },\n      { duration },\n    ),\n    new Mutation(`character.highlight.strokes.${strokeNum}.opacity`, 0, {\n      duration,\n      force: true,\n    }),\n  ];\n};\n\nexport const animateStroke = (\n  charName: CharacterName,\n  stroke: Stroke,\n  speed: number,\n): GenericMutation[] => {\n  const strokeNum = stroke.strokeNum;\n  const duration = (stroke.getLength() + 600) / (3 * speed);\n  return [\n    new Mutation(`character.${charName}`, {\n      opacity: 1,\n      strokes: {\n        [strokeNum]: {\n          displayPortion: 0,\n          opacity: 1,\n        },\n      },\n    }),\n    new Mutation(`character.${charName}.strokes.${strokeNum}.displayPortion`, 1, {\n      duration,\n    }),\n  ];\n};\n\nexport const animateSingleStroke = (\n  charName: CharacterName,\n  character: Character,\n  strokeNum: number,\n  speed: number,\n): GenericMutation[] => {\n  const mutationStateFunc = (state: RenderStateObject) => {\n    const curCharState = state.character[charName];\n    const mutationState: RecursivePartial<CharacterRenderState> = {\n      opacity: 1,\n      strokes: {},\n    };\n    for (let i = 0; i < character.strokes.length; i++) {\n      mutationState.strokes![i] = {\n        opacity: curCharState.opacity * curCharState.strokes[i].opacity,\n      };\n    }\n    return mutationState;\n  };\n  const stroke = character.strokes[strokeNum];\n  return [\n    new Mutation(`character.${charName}`, mutationStateFunc),\n    ...animateStroke(charName, stroke, speed),\n  ];\n};\n\nexport const showStroke = (\n  charName: CharacterName,\n  strokeNum: number,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}.strokes.${strokeNum}`,\n      {\n        displayPortion: 1,\n        opacity: 1,\n      },\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const animateCharacter = (\n  charName: CharacterName,\n  character: Character,\n  fadeDuration: number,\n  speed: number,\n  delayBetweenStrokes: number,\n): GenericMutation[] => {\n  let mutations: GenericMutation[] = hideCharacter(charName, character, fadeDuration);\n  mutations = mutations.concat(showStrokes(charName, character, 0));\n  mutations.push(\n    new Mutation(\n      `character.${charName}`,\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 0 }, character.strokes.length),\n      },\n      { force: true },\n    ),\n  );\n  character.strokes.forEach((stroke, i) => {\n    if (i > 0) mutations.push(new Mutation.Delay(delayBetweenStrokes));\n    mutations = mutations.concat(animateStroke(charName, stroke, speed));\n  });\n  return mutations;\n};\n\nexport const animateCharacterLoop = (\n  charName: CharacterName,\n  character: Character,\n  fadeDuration: number,\n  speed: number,\n  delayBetweenStrokes: number,\n  delayBetweenLoops: number,\n): GenericMutation[] => {\n  const mutations = animateCharacter(\n    charName,\n    character,\n    fadeDuration,\n    speed,\n    delayBetweenStrokes,\n  );\n  mutations.push(new Mutation.Delay(delayBetweenLoops));\n  return mutations;\n};\n", "import Mutation, { GenericMutation } from './Mutation';\nimport * as characterActions from './characterActions';\nimport { objRepeat, objRepeatCb } from './utils';\nimport Character from './models/Character';\nimport { ColorObject, Point } from './typings/types';\n\nexport const startQuiz = (\n  character: Character,\n  fadeDuration: number,\n  startStrokeNum: number,\n): GenericMutation[] => {\n  return [\n    ...characterActions.hideCharacter('main', character, fadeDuration),\n    new Mutation(\n      'character.highlight',\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 0 }, character.strokes.length),\n      },\n      { force: true },\n    ),\n    new Mutation(\n      'character.main',\n      {\n        opacity: 1,\n        strokes: objRepeatCb(character.strokes.length, (i) => ({\n          opacity: i < startStrokeNum ? 1 : 0,\n        })),\n      },\n      { force: true },\n    ),\n  ];\n};\n\nexport const startUserStroke = (id: string | number, point: Point): GenericMutation[] => {\n  return [\n    new Mutation('quiz.activeUserStrokeId', id, { force: true }),\n    new Mutation(\n      `userStrokes.${id}`,\n      {\n        points: [point],\n        opacity: 1,\n      },\n      { force: true },\n    ),\n  ];\n};\n\nexport const updateUserStroke = (\n  userStrokeId: string | number,\n  points: Point[],\n): GenericMutation[] => {\n  return [new Mutation(`userStrokes.${userStrokeId}.points`, points, { force: true })];\n};\n\nexport const hideUserStroke = (\n  userStrokeId: string | number,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(`userStrokes.${userStrokeId}.opacity`, 0, { duration }),\n    // Do not remove the stroke, keep it hidden until quiz ends\n    // This avoids a bug in which touchmove stops being triggered in the middle of a stroke\n    // the only doc i found https://stackoverflow.com/questions/29384973/touchmove-event-stops-triggering-after-any-element-is-removed-from-dom\n    // so if the user on his phone is too quick to start his new stroke, the new stroke may stops in mid air\n    //new Mutation(`userStrokes.${userStrokeId}`, null, { force: true }),\n  ];\n};\n\nexport const removeAllUserStrokes = (userStrokeIds: Array<number>): GenericMutation[] => {\n  return userStrokeIds?.map(userStrokeId =>\n    new Mutation(`userStrokes.${userStrokeId}`, null, { force: true })\n  ) || [];\n};\n\nexport const highlightCompleteChar = (\n  character: Character,\n  color: ColorObject | null,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation('options.highlightColor', color),\n    ...characterActions.hideCharacter('highlight', character),\n    ...characterActions.showCharacter('highlight', character, duration / 2),\n    ...characterActions.hideCharacter('highlight', character, duration / 2),\n  ];\n};\n\nexport const highlightStroke = characterActions.highlightStroke;\n", "import strokeMatches, { StrokeMatchResultMeta } from './strokeMatches';\nimport UserStroke from './models/UserStroke';\nimport Positioner from './Positioner';\nimport { counter, colorStringToVals, fixIndex } from './utils';\nimport * as quizActions from './quizActions';\nimport * as geometry from './geometry';\nimport * as characterActions from './characterActions';\nimport Character from './models/Character';\nimport { ParsedHanziWriterOptions, Point, StrokeData } from './typings/types';\nimport RenderState from './RenderState';\nimport { GenericMutation } from './Mutation';\n\nconst getDrawnPath = (userStroke: UserStroke) => ({\n  pathString: geometry.getPathString(userStroke.externalPoints),\n  points: userStroke.points.map((point) => geometry.round(point)),\n});\n\nexport default class Quiz {\n  _character: Character;\n  _renderState: RenderState;\n  _isActive: boolean;\n  _positioner: Positioner;\n\n  /** Set on startQuiz */\n  _options: ParsedHanziWriterOptions | undefined;\n  _currentStrokeIndex = 0;\n  _mistakesOnStroke = 0;\n  _totalMistakes = 0;\n  _userStroke: UserStroke | undefined;\n  _userStrokesIds: Array<number> | undefined;\n\n  constructor(character: Character, renderState: RenderState, positioner: Positioner) {\n    this._character = character;\n    this._renderState = renderState;\n    this._isActive = false;\n    this._positioner = positioner;\n  }\n\n  startQuiz(options: ParsedHanziWriterOptions) {\n    if (this._userStrokesIds) {\n      this._renderState.run(\n        quizActions.removeAllUserStrokes( this._userStrokesIds ),\n      );\n    }\n    this._userStrokesIds = []\n\n    this._isActive = true;\n    this._options = options;\n    const startIndex = fixIndex(\n      options.quizStartStrokeNum,\n      this._character.strokes.length,\n    );\n    this._currentStrokeIndex = Math.min(startIndex, this._character.strokes.length - 1);\n    this._mistakesOnStroke = 0;\n    this._totalMistakes = 0;\n\n    return this._renderState.run(\n      quizActions.startQuiz(\n        this._character,\n        options.strokeFadeDuration,\n        this._currentStrokeIndex,\n      ),\n    );\n  }\n\n  startUserStroke(externalPoint: Point) {\n    if (!this._isActive) {\n      return null;\n    }\n    if (this._userStroke) {\n      return this.endUserStroke();\n    }\n    const point = this._positioner.convertExternalPoint(externalPoint);\n    const strokeId = counter();\n    this._userStroke = new UserStroke(strokeId, point, externalPoint);\n    this._userStrokesIds?.push(strokeId)\n    return this._renderState.run(quizActions.startUserStroke(strokeId, point));\n  }\n\n  continueUserStroke(externalPoint: Point) {\n    if (!this._userStroke) {\n      return Promise.resolve();\n    }\n    const point = this._positioner.convertExternalPoint(externalPoint);\n    this._userStroke.appendPoint(point, externalPoint);\n    const nextPoints = this._userStroke.points.slice(0);\n    return this._renderState.run(\n      quizActions.updateUserStroke(this._userStroke.id, nextPoints),\n    );\n  }\n\n  setPositioner(positioner: Positioner) {\n    this._positioner = positioner;\n  }\n\n  endUserStroke() {\n    if (!this._userStroke) return;\n\n    this._renderState.run(\n      quizActions.hideUserStroke(\n        this._userStroke.id,\n        this._options!.drawingFadeDuration ?? 300,\n      ),\n    );\n\n    // skip single-point strokes\n    if (this._userStroke.points.length === 1) {\n      this._userStroke = undefined;\n      return;\n    }\n\n    const { acceptBackwardsStrokes, markStrokeCorrectAfterMisses } = this._options!;\n\n    const currentStroke = this._getCurrentStroke();\n    const { isMatch, meta } = strokeMatches(\n      this._userStroke,\n      this._character,\n      this._currentStrokeIndex,\n      {\n        isOutlineVisible: this._renderState.state.character.outline.opacity > 0,\n        leniency: this._options!.leniency,\n        averageDistanceThreshold: this._options!.averageDistanceThreshold,\n      },\n    );\n\n    // if markStrokeCorrectAfterMisses is passed, just force the stroke to count as correct after n tries\n    const isForceAccepted =\n      markStrokeCorrectAfterMisses &&\n      this._mistakesOnStroke + 1 >= markStrokeCorrectAfterMisses;\n\n    const isAccepted =\n      isMatch || isForceAccepted || (meta.isStrokeBackwards && acceptBackwardsStrokes);\n\n    if (isAccepted) {\n      this._handleSuccess(meta);\n    } else {\n      this._handleFailure(meta);\n\n      const {\n        showHintAfterMisses,\n        highlightColor,\n        strokeHighlightSpeed,\n      } = this._options!;\n\n      if (\n        showHintAfterMisses !== false &&\n        this._mistakesOnStroke >= showHintAfterMisses\n      ) {\n        this._renderState.run(\n          characterActions.highlightStroke(\n            currentStroke,\n            colorStringToVals(highlightColor),\n            strokeHighlightSpeed,\n          ),\n        );\n      }\n    }\n\n    this._userStroke = undefined;\n  }\n\n  cancel() {\n    this._isActive = false;\n    if (this._userStrokesIds) {\n      this._renderState.run(\n        quizActions.removeAllUserStrokes( this._userStrokesIds ),\n      );\n    }\n  }\n\n  _getStrokeData({\n    isCorrect,\n    meta,\n  }: {\n    isCorrect: boolean;\n    meta: StrokeMatchResultMeta;\n  }): StrokeData {\n    return {\n      character: this._character.symbol,\n      strokeNum: this._currentStrokeIndex,\n      mistakesOnStroke: this._mistakesOnStroke,\n      totalMistakes: this._totalMistakes,\n      strokesRemaining:\n        this._character.strokes.length - this._currentStrokeIndex - (isCorrect ? 1 : 0),\n      drawnPath: getDrawnPath(this._userStroke!),\n      isBackwards: meta.isStrokeBackwards,\n    };\n  }\n\n  nextStroke() {\n    if (!this._options) return;\n\n    const { strokes, symbol } = this._character;\n\n    const {\n      onComplete,\n      highlightOnComplete,\n      strokeFadeDuration,\n      highlightCompleteColor,\n      highlightColor,\n      strokeHighlightDuration,\n    } = this._options;\n\n    let animation: GenericMutation[] = characterActions.showStroke(\n      'main',\n      this._currentStrokeIndex,\n      strokeFadeDuration,\n    );\n\n    this._mistakesOnStroke = 0;\n    this._currentStrokeIndex += 1;\n\n    const isComplete = this._currentStrokeIndex === strokes.length;\n\n    if (isComplete) {\n      this._isActive = false;\n      onComplete?.({\n        character: symbol,\n        totalMistakes: this._totalMistakes,\n      });\n      if (highlightOnComplete) {\n        animation = animation.concat(\n          quizActions.highlightCompleteChar(\n            this._character,\n            colorStringToVals(highlightCompleteColor || highlightColor),\n            (strokeHighlightDuration || 0) * 2,\n          ),\n        );\n      }\n    }\n\n    this._renderState.run(animation);\n  }\n\n  _handleSuccess(meta: StrokeMatchResultMeta) {\n    if (!this._options) return;\n\n    const { onCorrectStroke } = this._options;\n\n    onCorrectStroke?.({\n      ...this._getStrokeData({ isCorrect: true, meta }),\n    });\n\n    this.nextStroke();\n  }\n\n  _handleFailure(meta: StrokeMatchResultMeta) {\n    this._mistakesOnStroke += 1;\n    this._totalMistakes += 1;\n    this._options!.onMistake?.(this._getStrokeData({ isCorrect: false, meta }));\n  }\n\n  _getCurrentStroke() {\n    return this._character.strokes[this._currentStrokeIndex];\n  }\n}\n", "export function createElm(elmType: string) {\n  return document.createElementNS('http://www.w3.org/2000/svg', elmType);\n}\n\nexport function attr(elm: Element, name: string, value: string) {\n  elm.setAttributeNS(null, name, value);\n}\n\nexport function attrs(elm: Element, attrsMap: Record<string, string>) {\n  Object.keys(attrsMap).forEach((attrName) => attr(elm, attrName, attrsMap[attrName]));\n}\n\n// inspired by https://talk.observablehq.com/t/hanzi-writer-renders-incorrectly-inside-an-observable-notebook-on-a-mobile-browser/1898\nexport function urlIdRef(id: string) {\n  let prefix = '';\n  if (window.location && window.location.href) {\n    prefix = window.location.href.replace(/#[^#]*$/, '').replace(/\"/gi, '%22');\n  }\n  return `url(\"${prefix}#${id}\")`;\n}\n\nexport function removeElm(elm: Element | undefined) {\n  elm?.parentNode?.removeChild(elm);\n}\n", "import Stroke from '../models/Stroke';\nimport { ColorObject } from '../typings/types';\n\nexport default class StrokeRendererBase {\n  _pathLength: number;\n  stroke: Stroke;\n  static STROKE_WIDTH = 200;\n\n  constructor(stroke: Stroke) {\n    this.stroke = stroke;\n    this._pathLength = stroke.getLength() + StrokeRendererBase.STROKE_WIDTH / 2;\n  }\n\n  _getStrokeDashoffset(displayPortion: number) {\n    return this._pathLength * 0.999 * (1 - displayPortion);\n  }\n\n  _getColor({\n    strokeColor,\n    radicalColor,\n  }: {\n    strokeColor: ColorObject;\n    radicalColor?: ColorObject | null;\n  }) {\n    return radicalColor && this.stroke.isInRadical ? radicalColor : strokeColor;\n  }\n}\n", "import { counter } from '../../utils';\nimport * as svg from './svgUtils';\nimport { extendStart, getPathString } from '../../geometry';\nimport StrokeRendererBase from '../StrokeRendererBase';\nimport Stroke from '../../models/Stroke';\nimport SVGRenderTarget from './RenderTarget';\nimport { ColorObject } from '../../typings/types';\n\nconst STROKE_WIDTH = 200;\n\ntype StrokeRenderProps = {\n  strokeColor: ColorObject;\n  radicalColor: ColorObject | null;\n  displayPortion: number;\n  opacity: number;\n};\n\n/** This is a stroke composed of several stroke parts **/\nexport default class StrokeRenderer extends StrokeRendererBase {\n  _oldProps: StrokeRenderProps | undefined = undefined;\n\n  _animationPath: SVGPathElement | undefined;\n  _clip: SVGClipPathElement | undefined;\n  _strokePath: SVGPathElement | undefined;\n\n  constructor(stroke: Stroke) {\n    super(stroke);\n  }\n\n  mount(target: SVGRenderTarget) {\n    this._animationPath = svg.createElm('path') as SVGPathElement;\n    this._clip = svg.createElm('clipPath') as SVGClipPathElement;\n    this._strokePath = svg.createElm('path') as SVGPathElement;\n    const maskId = `mask-${counter()}`;\n    svg.attr(this._clip, 'id', maskId);\n\n    svg.attr(this._strokePath, 'd', this.stroke.path);\n    this._animationPath.style.opacity = '0';\n    svg.attr(this._animationPath, 'clip-path', svg.urlIdRef(maskId));\n\n    const extendedMaskPoints = extendStart(this.stroke.points, STROKE_WIDTH / 2);\n    svg.attr(this._animationPath, 'd', getPathString(extendedMaskPoints));\n    svg.attrs(this._animationPath, {\n      stroke: '#FFFFFF',\n      'stroke-width': STROKE_WIDTH.toString(),\n      fill: 'none',\n      'stroke-linecap': 'round',\n      'stroke-linejoin': 'miter',\n      'stroke-dasharray': `${this._pathLength},${this._pathLength}`,\n    });\n\n    this._clip.appendChild(this._strokePath);\n    target.defs.appendChild(this._clip);\n    target.svg.appendChild(this._animationPath);\n    return this;\n  }\n\n  render(props: StrokeRenderProps) {\n    if (props === this._oldProps || !this._animationPath) {\n      return;\n    }\n\n    if (props.displayPortion !== this._oldProps?.displayPortion) {\n      this._animationPath.style.strokeDashoffset = this._getStrokeDashoffset(\n        props.displayPortion,\n      ).toString();\n    }\n\n    const color = this._getColor(props);\n\n    if (!this._oldProps || color !== this._getColor(this._oldProps)) {\n      const { r, g, b, a } = color;\n      svg.attrs(this._animationPath, { stroke: `rgba(${r},${g},${b},${a})` });\n    }\n\n    if (props.opacity !== this._oldProps?.opacity) {\n      this._animationPath.style.opacity = props.opacity.toString();\n    }\n    this._oldProps = props;\n  }\n}\n", "import { isMs<PERSON>rowser } from '../../utils';\nimport StrokeRenderer from './StrokeRenderer';\nimport SVGRenderTarget from './RenderTarget';\nimport Character from '../../models/Character';\nimport { ColorObject } from '../../typings/types';\nimport { StrokeRenderState } from '../../RenderState';\n\ntype SvgCharacterRenderProps = {\n  opacity: number;\n  strokes: Record<number, StrokeRenderState>;\n  strokeColor: ColorObject;\n  radicalColor?: ColorObject | null;\n};\n\nexport default class CharacterRenderer {\n  _oldProps: SvgCharacterRenderProps | undefined = undefined;\n  _strokeRenderers: StrokeRenderer[];\n\n  // set on mount()\n  _group: SVGElement | SVGSVGElement | undefined;\n\n  constructor(character: Character) {\n    this._strokeRenderers = character.strokes.map((stroke) => new StrokeRenderer(stroke));\n  }\n\n  mount(target: SVGRenderTarget) {\n    const subTarget = target.createSubRenderTarget();\n    this._group = subTarget.svg;\n    this._strokeRenderers.forEach((strokeRenderer) => {\n      strokeRenderer.mount(subTarget);\n    });\n  }\n\n  render(props: SvgCharacterRenderProps) {\n    if (props === this._oldProps || !this._group) {\n      return;\n    }\n    const { opacity, strokes, strokeColor, radicalColor = null } = props;\n    if (opacity !== this._oldProps?.opacity) {\n      this._group.style.opacity = opacity.toString();\n      // MS browsers seem to have a bug where if SVG is set to display:none, it sometimes breaks.\n      // More info: https://github.com/chanind/hanzi-writer/issues/164\n      // this is just a perf improvement, so disable for MS browsers\n      if (!isMsBrowser) {\n        if (opacity === 0) {\n          this._group.style.display = 'none';\n        } else if (this._oldProps?.opacity === 0) {\n          this._group.style.removeProperty('display');\n        }\n      }\n    }\n    const colorsChanged =\n      !this._oldProps ||\n      strokeColor !== this._oldProps.strokeColor ||\n      radicalColor !== this._oldProps.radicalColor;\n\n    if (colorsChanged || strokes !== this._oldProps?.strokes) {\n      for (let i = 0; i < this._strokeRenderers.length; i++) {\n        if (\n          !colorsChanged &&\n          this._oldProps?.strokes &&\n          strokes[i] === this._oldProps.strokes[i]\n        ) {\n          continue;\n        }\n        this._strokeRenderers[i].render({\n          strokeColor,\n          radicalColor,\n          opacity: strokes[i].opacity,\n          displayPortion: strokes[i].displayPortion,\n        });\n      }\n    }\n    this._oldProps = props;\n  }\n}\n", "import * as svg from './svgUtils';\nimport { getPathString } from '../../geometry';\nimport { ColorObject, Point } from '../../typings/types';\nimport SVGRenderTarget from './RenderTarget';\n\nexport type UserStrokeProps = {\n  strokeWidth: number;\n  strokeColor: ColorObject;\n  opacity: number;\n  points: Point[];\n};\n\nexport default class UserStrokeRenderer {\n  _oldProps: UserStrokeProps | undefined = undefined;\n  _path: SVGElement | undefined;\n\n  mount(target: SVGRenderTarget) {\n    this._path = svg.createElm('path');\n    target.svg.appendChild(this._path);\n  }\n\n  render(props: UserStrokeProps) {\n    if (!this._path || props === this._oldProps) {\n      return;\n    }\n    if (\n      props.strokeColor !== this._oldProps?.strokeColor ||\n      props.strokeWidth !== this._oldProps?.strokeWidth\n    ) {\n      const { r, g, b, a } = props.strokeColor;\n      svg.attrs(this._path, {\n        fill: 'none',\n        stroke: `rgba(${r},${g},${b},${a})`,\n        'stroke-width': props.strokeWidth.toString(),\n        'stroke-linecap': 'round',\n        'stroke-linejoin': 'round',\n      });\n    }\n    if (props.opacity !== this._oldProps?.opacity) {\n      svg.attr(this._path, 'opacity', props.opacity.toString());\n    }\n    if (props.points !== this._oldProps?.points) {\n      svg.attr(this._path, 'd', getPathString(props.points));\n    }\n    this._oldProps = props;\n  }\n\n  destroy() {\n    svg.removeElm(this._path);\n  }\n}\n", "import CharacterRenderer from './CharacterRenderer';\nimport UserStrokeRenderer, { UserStrokeProps } from './UserStrokeRenderer';\nimport * as svg from './svgUtils';\nimport Character from '../../models/Character';\nimport Positioner from '../../Positioner';\nimport SVGRenderTarget from './RenderTarget';\nimport HanziWriterRendererBase from '../HanziWriterRendererBase';\nimport { RenderStateObject } from '../../RenderState';\n\nexport default class HanziWriterRenderer\n  implements HanziWriterRendererBase<SVGElement | SVGSVGElement, SVGRenderTarget> {\n  _character: Character;\n  _positioner: Positioner;\n  _mainCharRenderer: CharacterRenderer;\n  _outlineCharRenderer: CharacterRenderer;\n  _highlightCharRenderer: CharacterRenderer;\n  _userStrokeRenderers: Record<string, UserStrokeRenderer | undefined>;\n  _positionedTarget: SVGRenderTarget | undefined;\n\n  constructor(character: Character, positioner: Positioner) {\n    this._character = character;\n    this._positioner = positioner;\n    this._mainCharRenderer = new CharacterRenderer(character);\n    this._outlineCharRenderer = new CharacterRenderer(character);\n    this._highlightCharRenderer = new CharacterRenderer(character);\n    this._userStrokeRenderers = {};\n  }\n\n  mount(target: SVGRenderTarget) {\n    const positionedTarget = target.createSubRenderTarget();\n    const group = positionedTarget.svg;\n    const { xOffset, yOffset, height, scale } = this._positioner;\n\n    svg.attr(\n      group,\n      'transform',\n      `translate(${xOffset}, ${height - yOffset}) scale(${scale}, ${-1 * scale})`,\n    );\n    this._outlineCharRenderer.mount(positionedTarget);\n    this._mainCharRenderer.mount(positionedTarget);\n    this._highlightCharRenderer.mount(positionedTarget);\n    this._positionedTarget = positionedTarget;\n  }\n\n  render(props: RenderStateObject) {\n    const { main, outline, highlight } = props.character;\n    const {\n      outlineColor,\n      radicalColor,\n      highlightColor,\n      strokeColor,\n      drawingWidth,\n      drawingColor,\n    } = props.options;\n\n    this._outlineCharRenderer.render({\n      opacity: outline.opacity,\n      strokes: outline.strokes,\n      strokeColor: outlineColor,\n    });\n\n    this._mainCharRenderer.render({\n      opacity: main.opacity,\n      strokes: main.strokes,\n      strokeColor,\n      radicalColor: radicalColor,\n    });\n\n    this._highlightCharRenderer.render({\n      opacity: highlight.opacity,\n      strokes: highlight.strokes,\n      strokeColor: highlightColor,\n    });\n\n    const userStrokes = props.userStrokes || {};\n\n    for (const userStrokeId in this._userStrokeRenderers) {\n      if (!userStrokes[userStrokeId]) {\n        this._userStrokeRenderers[userStrokeId]?.destroy();\n        delete this._userStrokeRenderers[userStrokeId];\n      }\n    }\n\n    for (const userStrokeId in userStrokes) {\n      const stroke = userStrokes[userStrokeId];\n      if (!stroke) {\n        continue;\n      }\n      const userStrokeProps: UserStrokeProps = {\n        strokeWidth: drawingWidth,\n        strokeColor: drawingColor,\n        ...stroke,\n      };\n\n      const strokeRenderer = (() => {\n        if (this._userStrokeRenderers[userStrokeId]) {\n          return this._userStrokeRenderers[userStrokeId]!;\n        }\n        const newStrokeRenderer = new UserStrokeRenderer();\n        newStrokeRenderer.mount(this._positionedTarget!);\n        this._userStrokeRenderers[userStrokeId] = newStrokeRenderer;\n        return newStrokeRenderer;\n      })();\n\n      strokeRenderer.render(userStrokeProps);\n    }\n  }\n\n  destroy() {\n    svg.removeElm(this._positionedTarget!.svg);\n    this._positionedTarget!.defs.innerHTML = '';\n  }\n}\n", "import { Point } from '../typings/types';\n\ntype BoundEvent = {\n  getPoint(): Point;\n  preventDefault(): void;\n};\n\n/** Generic render target */\nexport default class RenderTargetBase<\n  TElement extends\n    | HTMLElement\n    | SVGElement\n    | SVGSVGElement\n    | HTMLCanvasElement = HTMLElement\n> {\n  node: TElement;\n\n  constructor(node: TElement) {\n    this.node = node;\n  }\n\n  addPointerStartListener(callback: (arg: BoundEvent) => void) {\n    this.node.addEventListener('mousedown', (evt) => {\n      callback(this._eventify(evt as MouseEvent, this._getMousePoint));\n    });\n    this.node.addEventListener('touchstart', (evt) => {\n      callback(this._eventify(evt as TouchEvent, this._getTouchPoint));\n    });\n  }\n\n  addPointerMoveListener(callback: (arg: BoundEvent) => void) {\n    this.node.addEventListener('mousemove', (evt) => {\n      callback(this._eventify(evt as <PERSON><PERSON><PERSON>, this._getMousePoint));\n    });\n    this.node.addEventListener('touchmove', (evt) => {\n      callback(this._eventify(evt as TouchEvent, this._getTouchPoint));\n    });\n  }\n\n  addPointerEndListener(callback: () => void) {\n    // TODO: find a way to not need global listeners\n    document.addEventListener('mouseup', callback);\n    document.addEventListener('touchend', callback);\n  }\n\n  getBoundingClientRect() {\n    return this.node.getBoundingClientRect();\n  }\n\n  updateDimensions(width: string | number, height: string | number) {\n    this.node.setAttribute('width', `${width}`);\n    this.node.setAttribute('height', `${height}`);\n  }\n\n  _eventify<TEvent extends Event>(evt: TEvent, pointFunc: (event: TEvent) => Point) {\n    return {\n      getPoint: () => pointFunc.call(this, evt),\n      preventDefault: () => evt.preventDefault(),\n    };\n  }\n\n  _getMousePoint(evt: MouseEvent): Point {\n    const { left, top } = this.getBoundingClientRect();\n    const x = evt.clientX - left;\n    const y = evt.clientY - top;\n    return { x, y };\n  }\n\n  _getTouchPoint(evt: TouchEvent): Point {\n    const { left, top } = this.getBoundingClientRect();\n    const x = evt.touches[0].clientX - left;\n    const y = evt.touches[0].clientY - top;\n    return { x, y };\n  }\n}\n", "import { createElm, attrs } from './svgUtils';\nimport RenderTargetBase from '../RenderTargetBase';\n\nexport default class RenderTarget extends RenderTargetBase<SVGSVGElement | SVGElement> {\n  static init(elmOrId: Element | string, width = '100%', height = '100%') {\n    const element = (() => {\n      if (typeof elmOrId === 'string') {\n        return document.getElementById(elmOrId);\n      }\n      return elmOrId;\n    })();\n\n    if (!element) {\n      throw new Error(`HanziWriter target element not found: ${elmOrId}`);\n    }\n    const nodeType = element.nodeName.toUpperCase();\n\n    const svg = (() => {\n      if (nodeType === 'SVG' || nodeType === 'G') {\n        return element;\n      } else {\n        const svg = createElm('svg');\n        element.appendChild(svg);\n        return svg;\n      }\n    })() as SVGSVGElement;\n\n    attrs(svg, { width, height });\n    const defs = createElm('defs');\n    svg.appendChild(defs);\n\n    return new RenderTarget(svg, defs);\n  }\n\n  svg: SVGSVGElement | SVGElement;\n  defs: SVGElement;\n  _pt: DOMPoint | undefined;\n\n  constructor(svg: SVGElement | SVGSVGElement, defs: SVGElement) {\n    super(svg);\n\n    this.svg = svg;\n    this.defs = defs;\n\n    if ('createSVGPoint' in svg) {\n      this._pt = svg.createSVGPoint();\n    }\n  }\n\n  createSubRenderTarget() {\n    const group = createElm('g');\n    this.svg.appendChild(group);\n    return new RenderTarget(group, this.defs);\n  }\n\n  _getMousePoint(evt: MouseEvent) {\n    if (this._pt) {\n      this._pt.x = evt.clientX;\n      this._pt.y = evt.clientY;\n      if ('getScreenCTM' in this.node) {\n        const localPt = this._pt.matrixTransform(this.node.getScreenCTM()?.inverse());\n        return { x: localPt.x, y: localPt.y };\n      }\n    }\n    return super._getMousePoint.call(this, evt);\n  }\n\n  _getTouchPoint(evt: TouchEvent) {\n    if (this._pt) {\n      this._pt.x = evt.touches[0].clientX;\n      this._pt.y = evt.touches[0].clientY;\n      if ('getScreenCTM' in this.node) {\n        const localPt = this._pt.matrixTransform(\n          (this.node as SVGSVGElement).getScreenCTM()?.inverse(),\n        );\n        return { x: localPt.x, y: localPt.y };\n      }\n    }\n    return super._getTouchPoint(evt);\n  }\n}\n", "import { RenderTargetInitFunction } from '../../typings/types';\nimport HanziWriterRenderer from './HanziWriterRenderer';\nimport RenderTarget from './RenderTarget';\n\nexport default {\n  HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init as RenderTargetInitFunction<\n    SVGSVGElement | SVGElement\n  >,\n};\n", "import { Point } from '../../typings/types';\n\nexport const drawPath = (ctx: CanvasRenderingContext2D, points: Point[]) => {\n  ctx.beginPath();\n  const start = points[0];\n  const remainingPoints = points.slice(1);\n  ctx.moveTo(start.x, start.y);\n  for (const point of remainingPoints) {\n    ctx.lineTo(point.x, point.y);\n  }\n  ctx.stroke();\n};\n\n/**\n * Break a path string into a series of canvas path commands\n *\n * Note: only works with the subset of SVG paths used by MakeMeAHanzi data\n * @param pathString\n */\nexport const pathStringToCanvas = (pathString: string) => {\n  const pathParts = pathString.split(/(^|\\s+)(?=[A-Z])/).filter((part) => part !== ' ');\n  const commands = [(ctx: CanvasRenderingContext2D) => ctx.beginPath()];\n  for (const part of pathParts) {\n    const [cmd, ...rawParams] = part.split(/\\s+/);\n    const params = rawParams.map((param) => parseFloat(param)) as any[];\n    if (cmd === 'M') {\n      commands.push((ctx) => ctx.moveTo(...(params as [number, number])));\n    } else if (cmd === 'L') {\n      commands.push((ctx) => ctx.lineTo(...(params as [number, number])));\n    } else if (cmd === 'C') {\n      commands.push((ctx) =>\n        ctx.bezierCurveTo(...(params as Parameters<typeof ctx.bezierCurveTo>)),\n      );\n    } else if (cmd === 'Q') {\n      commands.push((ctx) =>\n        ctx.quadraticCurveTo(...(params as Parameters<typeof ctx.quadraticCurveTo>)),\n      );\n    } else if (cmd === 'Z') {\n      // commands.push((ctx) => ctx.closePath());\n    }\n  }\n  return (ctx: CanvasRenderingContext2D) => commands.forEach((cmd) => cmd(ctx));\n};\n", "import { extendStart } from '../../geometry';\nimport { drawPath, pathStringToCanvas } from './canvasUtils';\nimport StrokeRendererBase from '../StrokeRendererBase';\nimport Stroke from '../../models/Stroke';\nimport { ColorObject, Point } from '../../typings/types';\n\n/** this is a stroke composed of several stroke parts */\nexport default class StrokeRenderer extends StrokeRendererBase {\n  _extendedMaskPoints: Point[];\n\n  // Conditionally set on constructor\n  _path2D: Path2D | undefined;\n  _pathCmd: ((ctx: CanvasRenderingContext2D) => void) | undefined;\n\n  constructor(stroke: Stroke, usePath2D = true) {\n    super(stroke);\n\n    if (usePath2D && Path2D) {\n      this._path2D = new Path2D(this.stroke.path);\n    } else {\n      this._pathCmd = pathStringToCanvas(this.stroke.path);\n    }\n    this._extendedMaskPoints = extendStart(\n      this.stroke.points,\n      StrokeRendererBase.STROKE_WIDTH / 2,\n    );\n  }\n\n  render(\n    ctx: CanvasRenderingContext2D,\n    props: {\n      opacity: number;\n      strokeColor: ColorObject;\n      radicalColor?: ColorObject | null;\n      displayPortion: number;\n    },\n  ) {\n    if (props.opacity < 0.05) {\n      return;\n    }\n    ctx.save();\n\n    if (this._path2D) {\n      ctx.clip(this._path2D);\n    } else {\n      this._pathCmd?.(ctx);\n      // wechat bugs out if the clip path isn't stroked or filled\n      ctx.globalAlpha = 0;\n      ctx.stroke();\n      ctx.clip();\n    }\n\n    const { r, g, b, a } = this._getColor(props);\n    const color = a === 1 ? `rgb(${r},${g},${b})` : `rgb(${r},${g},${b},${a})`;\n    const dashOffset = this._getStrokeDashoffset(props.displayPortion);\n    ctx.globalAlpha = props.opacity;\n    ctx.strokeStyle = color;\n    ctx.fillStyle = color;\n    ctx.lineWidth = StrokeRendererBase.STROKE_WIDTH;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    // wechat sets dashOffset as a second param here. Should be harmless for browsers to add here too\n    // @ts-ignore\n    ctx.setLineDash([this._pathLength, this._pathLength], dashOffset);\n    ctx.lineDashOffset = dashOffset;\n    drawPath(ctx, this._extendedMaskPoints);\n\n    ctx.restore();\n  }\n}\n", "import Character from '../../models/Character';\nimport { StrokeRenderState } from '../../RenderState';\nimport { ColorObject } from '../../typings/types';\nimport StrokeRenderer from './StrokeRenderer';\n\nexport default class CharacterRenderer {\n  _strokeRenderers: StrokeRenderer[];\n\n  constructor(character: Character) {\n    this._strokeRenderers = character.strokes.map((stroke) => new StrokeRenderer(stroke));\n  }\n\n  render(\n    ctx: CanvasRenderingContext2D,\n    props: {\n      opacity: number;\n      strokes: Record<number, StrokeRenderState>;\n      strokeColor: ColorObject;\n      radicalColor?: ColorObject | null;\n    },\n  ) {\n    if (props.opacity < 0.05) return;\n\n    const { opacity, strokeColor, radicalColor, strokes } = props;\n\n    for (let i = 0; i < this._strokeRenderers.length; i++) {\n      this._strokeRenderers[i].render(ctx, {\n        strokeColor,\n        radicalColor,\n        opacity: strokes[i].opacity * opacity,\n        displayPortion: strokes[i].displayPortion || 0,\n      });\n    }\n  }\n}\n", "import { ColorObject, Point } from '../../typings/types';\nimport { drawPath } from './canvasUtils';\n\nexport default function renderUserStroke(\n  ctx: CanvasRenderingContext2D,\n  props: {\n    opacity: number;\n    strokeWidth: number;\n    strokeColor: ColorObject;\n    points: Point[];\n  },\n) {\n  if (props.opacity < 0.05) {\n    return;\n  }\n  const { opacity, strokeWidth, strokeColor, points } = props;\n  const { r, g, b, a } = strokeColor;\n\n  ctx.save();\n  ctx.globalAlpha = opacity;\n  ctx.lineWidth = strokeWidth;\n  ctx.strokeStyle = `rgba(${r},${g},${b},${a})`;\n  ctx.lineCap = 'round';\n  ctx.lineJoin = 'round';\n  drawPath(ctx, points);\n  ctx.restore();\n}\n", "import Character from '../../models/Character';\nimport Positioner from '../../Positioner';\nimport HanziWriterRendererBase from '../HanziWriterRendererBase';\nimport CanvasRenderTarget from '../canvas/RenderTarget';\nimport CharacterRenderer from './CharacterRenderer';\nimport renderUserStroke from './renderUserStroke';\nimport { RenderStateObject } from '../../RenderState';\nimport { noop } from '../../utils';\n\nexport default class HanziWriterRenderer\n  implements HanziWriterRendererBase<HTMLCanvasElement, CanvasRenderTarget> {\n  _character: Character;\n  _positioner: Positioner;\n  _mainCharRenderer: CharacterRenderer;\n  _outlineCharRenderer: CharacterRenderer;\n  _highlightCharRenderer: CharacterRenderer;\n  _target: CanvasRenderTarget | undefined;\n\n  constructor(character: Character, positioner: Positioner) {\n    this._character = character;\n    this._positioner = positioner;\n    this._mainCharRenderer = new CharacterRenderer(character);\n    this._outlineCharRenderer = new CharacterRenderer(character);\n    this._highlightCharRenderer = new CharacterRenderer(character);\n  }\n\n  mount(target: CanvasRenderTarget) {\n    this._target = target;\n  }\n\n  destroy = noop;\n\n  _animationFrame(cb: (ctx: CanvasRenderingContext2D) => void) {\n    const { width, height, scale, xOffset, yOffset } = this._positioner;\n    const ctx = this._target!.getContext()!;\n    ctx.clearRect(0, 0, width, height);\n    ctx.save();\n    ctx.translate(xOffset, height - yOffset);\n    ctx.transform(1, 0, 0, -1, 0, 0);\n    ctx.scale(scale, scale);\n    cb(ctx);\n    ctx.restore();\n    // @ts-expect-error Verify if this is still needed for the \"wechat miniprogram\".\n    if (ctx.draw) {\n      // @ts-expect-error\n      ctx.draw();\n    }\n  }\n\n  render(props: RenderStateObject) {\n    const { outline, main, highlight } = props.character;\n    const {\n      outlineColor,\n      strokeColor,\n      radicalColor,\n      highlightColor,\n      drawingColor,\n      drawingWidth,\n    } = props.options;\n\n    this._animationFrame((ctx) => {\n      this._outlineCharRenderer.render(ctx, {\n        opacity: outline.opacity,\n        strokes: outline.strokes,\n        strokeColor: outlineColor,\n      });\n      this._mainCharRenderer.render(ctx, {\n        opacity: main.opacity,\n        strokes: main.strokes,\n        strokeColor: strokeColor,\n        radicalColor: radicalColor,\n      });\n      this._highlightCharRenderer.render(ctx, {\n        opacity: highlight.opacity,\n        strokes: highlight.strokes,\n        strokeColor: highlightColor,\n      });\n\n      const userStrokes = props.userStrokes || {};\n\n      for (const userStrokeId in userStrokes) {\n        const userStroke = userStrokes[userStrokeId];\n        if (userStroke) {\n          const userStrokeProps = {\n            strokeWidth: drawingWidth,\n            strokeColor: drawingColor,\n            ...userStroke,\n          };\n          renderUserStroke(ctx, userStrokeProps);\n        }\n      }\n    });\n  }\n}\n", "import RenderTargetBase from '../RenderTargetBase';\n\nexport default class RenderTarget extends RenderTargetBase<HTMLCanvasElement> {\n  constructor(canvas: HTMLCanvasElement) {\n    super(canvas);\n  }\n\n  static init(elmOrId: string | HTMLCanvasElement, width = '100%', height = '100%') {\n    const element = (() => {\n      if (typeof elmOrId === 'string') {\n        return document.getElementById(elmOrId);\n      }\n      return elmOrId;\n    })();\n\n    if (!element) {\n      throw new Error(`HanziWriter target element not found: ${elmOrId}`);\n    }\n\n    const nodeType = element.nodeName.toUpperCase();\n\n    const canvas = (() => {\n      if (nodeType === 'CANVAS') {\n        return element as HTMLCanvasElement;\n      }\n      const canvas = document.createElement('canvas');\n      element.appendChild(canvas);\n      return canvas;\n    })();\n\n    canvas.setAttribute('width', width);\n    canvas.setAttribute('height', height);\n\n    return new RenderTarget(canvas);\n  }\n\n  getContext() {\n    return this.node.getContext('2d');\n  }\n}\n", "import { RenderTargetInitFunction } from '../../typings/types';\nimport HanziWriterRenderer from './HanziWriterRenderer';\nimport RenderTarget from './RenderTarget';\n\nexport default {\n  HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init as RenderTargetInitFunction<HTMLCanvasElement>,\n};\n", "import { CharacterJson } from './typings/types';\n\nconst VERSION = '2.0';\nconst getCharDataUrl = (char: string) =>\n  `https://cdn.jsdelivr.net/npm/hanzi-writer-data@${VERSION}/${char}.json`;\n\nconst defaultCharDataLoader = (\n  char: string,\n  onLoad: (parsedJson: CharacterJson) => void,\n  onError: (error?: any, context?: any) => void,\n) => {\n  // load char data from hanziwriter cdn (currently hosted on jsdelivr)\n  const xhr = new XMLHttpRequest();\n  if (xhr.overrideMimeType) {\n    // IE 9 and 10 don't seem to support this...\n    xhr.overrideMimeType('application/json');\n  }\n  xhr.open('GET', getCharDataUrl(char), true);\n  xhr.onerror = (event) => {\n    onError(xhr, event);\n  };\n  xhr.onreadystatechange = () => {\n    // TODO: error handling\n    if (xhr.readyState !== 4) return;\n\n    if (xhr.status === 200) {\n      onLoad(JSON.parse(xhr.responseText));\n    } else if (xhr.status !== 0 && onError) {\n      onError(xhr);\n    }\n  };\n  xhr.send(null);\n};\n\nexport default defaultCharDataLoader;\n", "import { HanziWriterOptions } from './typings/types';\nimport defaultCharDataLoader from './defaultCharDataLoader';\n\nconst defaultOptions: HanziWriterOptions = {\n  charDataLoader: defaultCharDataLoader,\n  onLoadCharDataError: null,\n  onLoadCharDataSuccess: null,\n  showOutline: true,\n  showCharacter: true,\n  renderer: 'svg',\n\n  // positioning options\n\n  width: 0,\n  height: 0,\n  padding: 20,\n\n  // animation options\n\n  strokeAnimationSpeed: 1,\n  strokeFadeDuration: 400,\n  strokeHighlightDuration: 200,\n  strokeHighlightSpeed: 2,\n  delayBetweenStrokes: 1000,\n  delayBetweenLoops: 2000,\n\n  // colors\n\n  strokeColor: '#555',\n  radicalColor: null,\n  highlightColor: '#AAF',\n  outlineColor: '#DDD',\n  drawingColor: '#333',\n\n  // quiz options\n\n  leniency: 1,\n  showHintAfterMisses: 3,\n  highlightOnComplete: true,\n  highlightCompleteColor: null,\n  markStrokeCorrectAfterMisses: false,\n  acceptBackwardsStrokes: false,\n  quizStartStrokeNum: 0,\n  averageDistanceThreshold: 350,\n\n  // undocumented obscure options\n\n  drawingFadeDuration: 300,\n  drawingWidth: 4,\n  strokeWidth: 2,\n  outlineWidth: 2,\n  rendererOverride: {},\n};\n\nexport default defaultOptions;\n", "import { CharacterJson, LoadingManagerOptions } from './typings/types';\n\ntype CustomError = Error & { reason: string };\n\nexport default class LoadingManager {\n  _loadCounter = 0;\n  _isLoading = false;\n  _resolve: ((data: CharacterJson) => void) | undefined;\n  _reject: ((error?: Error | CustomError | string) => void) | undefined;\n  _options: LoadingManagerOptions;\n\n  /** Set when calling LoadingManager.loadCharData  */\n  _loadingChar: string | undefined;\n  /** use this to attribute to determine if there was a problem with loading */\n  loadingFailed = false;\n\n  constructor(options: LoadingManagerOptions) {\n    this._options = options;\n  }\n\n  _debouncedLoad(char: string, count: number) {\n    // these wrappers ignore all responses except the most recent.\n    const wrappedResolve = (data: CharacterJson) => {\n      if (count === this._loadCounter) {\n        this._resolve?.(data);\n      }\n    };\n    const wrappedReject = (reason?: Error | string) => {\n      if (count === this._loadCounter) {\n        this._reject?.(reason);\n      }\n    };\n\n    const returnedData = this._options.charDataLoader(\n      char,\n      wrappedResolve,\n      wrappedReject,\n    );\n\n    if (returnedData) {\n      if ('then' in returnedData) {\n        returnedData.then(wrappedResolve).catch(wrappedReject);\n      } else {\n        wrappedResolve(returnedData);\n      }\n    }\n  }\n\n  _setupLoadingPromise() {\n    return new Promise(\n      (\n        resolve: (data: CharacterJson) => void,\n        reject: (err?: Error | CustomError | string) => void,\n      ) => {\n        this._resolve = resolve;\n        this._reject = reject;\n      },\n    )\n      .then((data: CharacterJson) => {\n        this._isLoading = false;\n        this._options.onLoadCharDataSuccess?.(data);\n        return data;\n      })\n      .catch((reason) => {\n        this._isLoading = false;\n        this.loadingFailed = true;\n\n        // If the user has provided an \"onLoadCharDataError\", call this function\n        // Otherwise, throw the promise\n        if (this._options.onLoadCharDataError) {\n          this._options.onLoadCharDataError(reason);\n          return;\n        }\n\n        // If error callback wasn't provided, throw an error so the developer will be aware something went wrong\n        if (reason instanceof Error) {\n          throw reason;\n        }\n\n        const err = new Error(\n          `Failed to load char data for ${this._loadingChar}`,\n        ) as CustomError;\n\n        err.reason = reason;\n\n        throw err;\n      });\n  }\n\n  loadCharData(char: string) {\n    this._loadingChar = char;\n    const promise = this._setupLoadingPromise();\n    this.loadingFailed = false;\n    this._isLoading = true;\n    this._loadCounter++;\n    this._debouncedLoad(char, this._loadCounter);\n    return promise;\n  }\n}\n", "import RenderState from './RenderState';\nimport parseCharData from './parseCharData';\nimport Positioner from './Positioner';\nimport Quiz from './Quiz';\nimport svgRenderer from './renderers/svg';\nimport canvasRenderer from './renderers/canvas';\nimport defaultOptions from './defaultOptions';\nimport LoadingManager from './LoadingManager';\nimport * as characterActions from './characterActions';\nimport { trim, colorStringToVals, selectIndex, fixIndex } from './utils';\nimport Character from './models/Character';\nimport HanziWriterRendererBase, {\n  HanziWriterRendererConstructor,\n} from './renderers/HanziWriterRendererBase';\nimport RenderTargetBase from './renderers/RenderTargetBase';\nimport { GenericMutation } from './Mutation';\n\n// Typings\nimport {\n  ColorOptions,\n  DimensionOptions,\n  HanziWriterOptions,\n  LoadingManagerOptions,\n  OnCompleteFunction,\n  ParsedHanziWriterOptions,\n  QuizOptions,\n  RenderTargetInitFunction,\n} from './typings/types';\n\n// Export type interfaces\nexport * from './typings/types';\n\nexport default class HanziWriter {\n  _options: ParsedHanziWriterOptions;\n  _loadingManager: LoadingManager;\n  /** Only set when calling .setCharacter() */\n  _char: string | undefined;\n  /** Only set when calling .setCharacter() */\n  _renderState: RenderState | undefined;\n  /** Only set when calling .setCharacter() */\n  _character: Character | undefined;\n  /** Only set when calling .setCharacter() */\n  _positioner: Positioner | undefined;\n  /** Only set when calling .setCharacter() */\n  _hanziWriterRenderer: HanziWriterRendererBase<HTMLElement, any> | null | undefined;\n  /** Only set when calling .setCharacter() */\n  _withDataPromise: Promise<void> | undefined;\n\n  _quiz: Quiz | undefined;\n  _renderer: {\n    HanziWriterRenderer: HanziWriterRendererConstructor;\n    createRenderTarget: RenderTargetInitFunction<any>;\n  };\n\n  target: RenderTargetBase;\n\n  /** Main entry point */\n  static create(\n    element: string | HTMLElement,\n    character: string,\n    options?: Partial<HanziWriterOptions>,\n  ) {\n    const writer = new HanziWriter(element, options);\n    writer.setCharacter(character);\n\n    return writer;\n  }\n\n  /** Singleton instance of LoadingManager. Only set in `loadCharacterData` */\n  static _loadingManager: LoadingManager | null = null;\n  /** Singleton loading options. Only set in `loadCharacterData` */\n  static _loadingOptions: Partial<HanziWriterOptions> | null = null;\n\n  static loadCharacterData(\n    character: string,\n    options: Partial<LoadingManagerOptions> = {},\n  ) {\n    const loadingManager = (() => {\n      const { _loadingManager, _loadingOptions } = HanziWriter;\n      if (_loadingManager?._loadingChar === character && _loadingOptions === options) {\n        return _loadingManager;\n      }\n      return new LoadingManager({ ...defaultOptions, ...options });\n    })();\n\n    HanziWriter._loadingManager = loadingManager;\n    HanziWriter._loadingOptions = options;\n    return loadingManager.loadCharData(character);\n  }\n\n  static getScalingTransform(width: number, height: number, padding = 0) {\n    const positioner = new Positioner({ width, height, padding });\n    return {\n      x: positioner.xOffset,\n      y: positioner.yOffset,\n      scale: positioner.scale,\n      transform: trim(`\n        translate(${positioner.xOffset}, ${positioner.height - positioner.yOffset})\n        scale(${positioner.scale}, ${-1 * positioner.scale})\n      `).replace(/\\s+/g, ' '),\n    };\n  }\n\n  constructor(element: string | HTMLElement, options: Partial<HanziWriterOptions> = {}) {\n    const { HanziWriterRenderer, createRenderTarget } =\n      options.renderer === 'canvas' ? canvasRenderer : svgRenderer;\n    const rendererOverride = options.rendererOverride || {};\n\n    this._renderer = {\n      HanziWriterRenderer: rendererOverride.HanziWriterRenderer || HanziWriterRenderer,\n      createRenderTarget: rendererOverride.createRenderTarget || createRenderTarget,\n    };\n    // wechat miniprogram component needs direct access to the render target, so this is public\n    this.target = this._renderer.createRenderTarget(\n      element,\n      options.width,\n      options.height,\n    );\n    this._options = this._assignOptions(options);\n    this._loadingManager = new LoadingManager(this._options);\n    this._setupListeners();\n  }\n\n  showCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n      duration?: number;\n    } = {},\n  ) {\n    this._options.showCharacter = true;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.showCharacter(\n            'main',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  hideCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n      duration?: number;\n    } = {},\n  ) {\n    this._options.showCharacter = false;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.hideCharacter(\n            'main',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  animateCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this.cancelQuiz();\n\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.animateCharacter(\n            'main',\n            this._character!,\n            this._options.strokeFadeDuration,\n            this._options.strokeAnimationSpeed,\n            this._options.delayBetweenStrokes,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  animateStroke(\n    strokeNum: number,\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this.cancelQuiz();\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.animateSingleStroke(\n            'main',\n            this._character!,\n            fixIndex(strokeNum, this._character!.strokes.length),\n            this._options.strokeAnimationSpeed,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  highlightStroke(\n    strokeNum: number,\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    const promise = () => {\n      if (!this._character || !this._renderState) {\n        return;\n      }\n\n      return this._renderState\n        .run(\n          characterActions.highlightStroke(\n            selectIndex(this._character.strokes, strokeNum),\n            colorStringToVals(this._options.highlightColor),\n            this._options.strokeHighlightSpeed,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        });\n    };\n\n    return this._withData(promise);\n  }\n\n  async loopCharacterAnimation() {\n    this.cancelQuiz();\n    return this._withData(() =>\n      this._renderState!.run(\n        characterActions.animateCharacterLoop(\n          'main',\n          this._character!,\n          this._options.strokeFadeDuration,\n          this._options.strokeAnimationSpeed,\n          this._options.delayBetweenStrokes,\n          this._options.delayBetweenLoops,\n        ),\n        { loop: true },\n      ),\n    );\n  }\n\n  pauseAnimation() {\n    return this._withData(() => this._renderState?.pauseAll());\n  }\n\n  resumeAnimation() {\n    return this._withData(() => this._renderState?.resumeAll());\n  }\n\n  showOutline(\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this._options.showOutline = true;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.showCharacter(\n            'outline',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  hideOutline(\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this._options.showOutline = false;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.hideCharacter(\n            'outline',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  /** Updates the size of the writer instance without resetting render state */\n  updateDimensions({ width, height, padding }: Partial<DimensionOptions>) {\n    if (width !== undefined) this._options.width = width;\n    if (height !== undefined) this._options.height = height;\n    if (padding !== undefined) this._options.padding = padding;\n    this.target.updateDimensions(this._options.width, this._options.height);\n    // if there's already a character drawn, destroy and recreate the renderer in the same state\n    if (\n      this._character &&\n      this._renderState &&\n      this._hanziWriterRenderer &&\n      this._positioner\n    ) {\n      this._hanziWriterRenderer.destroy();\n      const hanziWriterRenderer = this._initAndMountHanziWriterRenderer(this._character);\n      // TODO: this should probably implement EventEmitter instead of manually tracking updates like this\n      this._renderState.overwriteOnStateChange((nextState) =>\n        hanziWriterRenderer.render(nextState),\n      );\n      hanziWriterRenderer.render(this._renderState.state);\n      // update the current quiz as well, if one is active\n      if (this._quiz) {\n        this._quiz.setPositioner(this._positioner);\n      }\n    }\n  }\n\n  updateColor(\n    colorName: keyof ColorOptions,\n    colorVal: string | null,\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    let mutations: GenericMutation[] = [];\n\n    const fixedColorVal = (() => {\n      // If we're removing radical color, tween it to the stroke color\n      if (colorName === 'radicalColor' && !colorVal) {\n        return this._options.strokeColor;\n      }\n      return colorVal;\n    })();\n\n    const mappedColor = colorStringToVals(fixedColorVal as string);\n\n    this._options[colorName] = colorVal as any;\n\n    const duration = options.duration ?? this._options.strokeFadeDuration;\n\n    mutations = mutations.concat(\n      characterActions.updateColor(colorName, mappedColor, duration),\n    );\n\n    // make sure to set radicalColor back to null after the transition finishes if val == null\n    if (colorName === 'radicalColor' && !colorVal) {\n      mutations = mutations.concat(characterActions.updateColor(colorName, null, 0));\n    }\n\n    return this._withData(() =>\n      this._renderState?.run(mutations).then((res) => {\n        options.onComplete?.(res);\n        return res;\n      }),\n    );\n  }\n\n  quiz(quizOptions: Partial<QuizOptions> = {}) {\n    return this._withData(async () => {\n      if (this._character && this._renderState && this._positioner) {\n        this.cancelQuiz();\n        this._quiz = new Quiz(this._character, this._renderState, this._positioner);\n        this._options = {\n          ...this._options,\n          ...quizOptions,\n        };\n        this._quiz.startQuiz(this._options);\n      }\n    });\n  }\n\n  skipQuizStroke() {\n    if (this._quiz) {\n      this._quiz.nextStroke();\n    }\n  }\n\n  cancelQuiz() {\n    if (this._quiz) {\n      this._quiz.cancel();\n      this._quiz = undefined;\n    }\n  }\n\n  setCharacter(char: string) {\n    this.cancelQuiz();\n    this._char = char;\n    if (this._hanziWriterRenderer) {\n      this._hanziWriterRenderer.destroy();\n    }\n    if (this._renderState) {\n      this._renderState.cancelAll();\n    }\n    this._hanziWriterRenderer = null;\n    this._withDataPromise = this._loadingManager\n      .loadCharData(char)\n      .then((pathStrings) => {\n        // if \"pathStrings\" isn't set, \".catch()\"\" was probably called and loading likely failed\n        if (!pathStrings || this._loadingManager.loadingFailed) {\n          return;\n        }\n\n        this._character = parseCharData(char, pathStrings);\n        this._renderState = new RenderState(this._character, this._options, (nextState) =>\n          hanziWriterRenderer.render(nextState),\n        );\n\n        const hanziWriterRenderer = this._initAndMountHanziWriterRenderer(\n          this._character,\n        );\n        hanziWriterRenderer.render(this._renderState.state);\n      });\n    return this._withDataPromise;\n  }\n\n  _initAndMountHanziWriterRenderer(character: Character) {\n    const { width, height, padding } = this._options;\n    this._positioner = new Positioner({ width, height, padding });\n    const hanziWriterRenderer = new this._renderer.HanziWriterRenderer(\n      character,\n      this._positioner,\n    );\n    hanziWriterRenderer.mount(this.target);\n    this._hanziWriterRenderer = hanziWriterRenderer;\n    return hanziWriterRenderer;\n  }\n\n  async getCharacterData(): Promise<Character> {\n    if (!this._char) {\n      throw new Error('setCharacter() must be called before calling getCharacterData()');\n    }\n    const character = await this._withData(() => this._character);\n    return character!;\n  }\n\n  _assignOptions(options: Partial<HanziWriterOptions>): ParsedHanziWriterOptions {\n    const mergedOptions = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    // backfill strokeAnimationSpeed if deprecated strokeAnimationDuration is provided instead\n    if (options.strokeAnimationDuration && !options.strokeAnimationSpeed) {\n      mergedOptions.strokeAnimationSpeed = 500 / options.strokeAnimationDuration;\n    }\n    if (options.strokeHighlightDuration && !options.strokeHighlightSpeed) {\n      mergedOptions.strokeHighlightSpeed = 500 / mergedOptions.strokeHighlightDuration;\n    }\n\n    if (!options.highlightCompleteColor) {\n      mergedOptions.highlightCompleteColor = mergedOptions.highlightColor;\n    }\n\n    return this._fillWidthAndHeight(mergedOptions);\n  }\n\n  /** returns a new options object with width and height filled in if missing */\n  _fillWidthAndHeight(options: HanziWriterOptions): ParsedHanziWriterOptions {\n    const filledOpts = { ...options };\n    if (filledOpts.width && !filledOpts.height) {\n      filledOpts.height = filledOpts.width;\n    } else if (filledOpts.height && !filledOpts.width) {\n      filledOpts.width = filledOpts.height;\n    } else if (!filledOpts.width && !filledOpts.height) {\n      const { width, height } = this.target.getBoundingClientRect();\n      const minDim = Math.min(width, height);\n      filledOpts.width = minDim;\n      filledOpts.height = minDim;\n    }\n    return filledOpts as ParsedHanziWriterOptions;\n  }\n\n  _withData<T>(func: () => T) {\n    // if this._loadingManager.loadingFailed, then loading failed before this method was called\n    if (this._loadingManager.loadingFailed) {\n      throw Error('Failed to load character data. Call setCharacter and try again.');\n    }\n\n    if (this._withDataPromise) {\n      return this._withDataPromise.then(() => {\n        if (!this._loadingManager.loadingFailed) {\n          return func();\n        }\n      });\n    }\n    return Promise.resolve().then(func);\n  }\n\n  _setupListeners() {\n    this.target.addPointerStartListener((evt) => {\n      if (this._quiz) {\n        evt.preventDefault();\n        this._quiz.startUserStroke(evt.getPoint());\n      }\n    });\n    this.target.addPointerMoveListener((evt) => {\n      if (this._quiz) {\n        evt.preventDefault();\n        this._quiz.continueUserStroke(evt.getPoint());\n      }\n    });\n    this.target.addPointerEndListener(() => {\n      this._quiz?.endUserStroke();\n    });\n  }\n}\n"], "names": ["globalObj", "window", "global", "performanceNow", "performance", "now", "Date", "requestAnimationFrame", "bind", "callback", "setTimeout", "cancelAnimationFrame", "clearTimeout", "arrLast", "arr", "length", "fixIndex", "index", "selectIndex", "copyAndMergeDeep", "base", "override", "output", "key", "baseVal", "overrideVal", "Array", "isArray", "inflate", "scope", "obj", "parts", "split", "final", "current", "i", "cap", "count", "counter", "average", "sum", "reduce", "acc", "val", "colorStringToVals", "colorString", "normalizedColor", "toUpperCase", "trim", "test", "hexParts", "substring", "hexStr", "join", "r", "parseInt", "slice", "g", "b", "a", "rgbMatch", "match", "parseFloat", "Error", "string", "replace", "objRepeat", "item", "times", "objRepeatCb", "cb", "ua", "navigator", "userAgent", "isMs<PERSON><PERSON>er", "indexOf", "noop", "RenderState", "constructor", "character", "options", "onStateChange", "_<PERSON><PERSON><PERSON><PERSON>", "_onStateChange", "state", "drawingFadeDuration", "drawing<PERSON><PERSON><PERSON>", "drawingColor", "strokeColor", "outlineColor", "radicalColor", "highlightColor", "main", "opacity", "showCharacter", "strokes", "outline", "showOutline", "highlight", "userStrokes", "displayPortion", "overwriteOnStateChange", "updateState", "stateChanges", "nextState", "run", "mutations", "scopes", "map", "mut", "cancelMutations", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "_isActive", "_index", "_resolve", "_mutations", "_loop", "loop", "_scopes", "push", "_run", "filter", "chain", "canceled", "activeMutation", "then", "_getActiveMutations", "pauseAll", "for<PERSON>ach", "mutation", "pause", "resumeAll", "resume", "scopesToCancel", "chainId", "scopeToCancel", "startsWith", "_cancelMutationChain", "cancelAll", "cancel", "subtract", "p1", "p2", "x", "y", "magnitude", "point", "Math", "sqrt", "pow", "distance", "point1", "point2", "equals", "round", "precision", "multiplier", "points", "lastPoint", "pointsSansFirst", "dist", "cosineSimilarity", "rawDotProduct", "_extendPointOnLine", "vect", "norm", "frechetDist", "curve1", "curve2", "longCurve", "shortCurve", "calcVal", "j", "prevResultsCol", "curResultsCol", "max", "lastResult", "min", "subdivideCurve", "curve", "maxLen", "newCurve", "prevPoint", "segLen", "numNewPoints", "ceil", "newSegLen", "outlineCurve", "numPoints", "curveLen", "segmentLen", "outlinePoints", "endPoint", "remainingCurvePoints", "remainingDist", "outlinePointFound", "nextPointDist", "shift", "nextPoint", "normalizeCurve", "outlinedCurve", "meanX", "meanY", "mean", "translatedCurve", "scale", "scaledCurve", "rotate", "theta", "cos", "sin", "_filterParallelPoints", "filteredPoints", "numFilteredPoints", "curVect", "prevVect", "isParallel", "pop", "getPathString", "close", "start", "remainingPoints", "pathString", "roundedPoint", "extendStart", "newStart", "extendedPoints", "unshift", "Stroke", "path", "strokeNum", "isInRadical", "getStartingPoint", "getEndingPoint", "<PERSON><PERSON><PERSON><PERSON>", "getVectors", "vector", "getDistance", "distances", "strokePoint", "getAverageDistance", "totalDist", "Character", "symbol", "generateStrokes", "radStrokes", "medians", "pointData", "parseCharData", "char<PERSON><PERSON>", "CHARACTER_BOUNDS", "from", "to", "preScaledWidth", "preScaledHeight", "Positioner", "padding", "width", "height", "effectiveWidth", "effectiveHeight", "scaleX", "scaleY", "xCenteringBuffer", "yCenteringBuffer", "xOffset", "yOffset", "convertExternalPoint", "COSINE_SIMILARITY_THRESHOLD", "START_AND_END_DIST_THRESHOLD", "FRECHET_THRESHOLD", "MIN_LEN_THRESHOLD", "stroke<PERSON><PERSON><PERSON>", "userStroke", "stripDuplicates", "isMatch", "meta", "isStrokeBackwards", "avgDist", "getMatchData", "laterStrokes", "closestMatchDist", "checkBackwards", "leniencyAdjustment", "leniency", "startAndEndMatches", "closestStroke", "startingDist", "endingDist", "getEdgeVectors", "vectors", "directionMatches", "stroke", "edgeVectors", "strokeVectors", "similarities", "edgeVector", "strokeSimilarities", "strokeVector", "avgSimilarity", "lengthMatches", "firstPoint", "rest", "dedupedPoints", "SHAPE_FIT_ROTATIONS", "PI", "shapeFit", "normCurve1", "normCurve2", "minDist", "Infinity", "isOutlineVisible", "averageDistanceThreshold", "distMod", "withinDistThresh", "startAndEndMatch", "directionMatch", "shapeMatch", "lengthMatch", "backwardsMatchData", "reverse", "UserStroke", "id", "startingPoint", "startingExternalPoint", "externalPoints", "appendPoint", "externalPoint", "Delay", "duration", "_duration", "_startTime", "_paused", "_runningPromise", "_timeout", "elapsed<PERSON><PERSON><PERSON>", "undefined", "Mutation", "valuesOrCallable", "_tick", "timing", "_startPauseTime", "progress", "_pausedDuration", "_renderState", "_values", "_frame<PERSON>andle", "easedProgress", "ease", "getPartialV<PERSON>ues", "_startState", "_valuesOrCallable", "_force", "force", "renderState", "_inflateValues", "isAlreadyAtEnd", "values", "startValues", "endValues", "target", "endValue", "startValue", "showStrokes", "char<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "updateColor", "colorName", "colorVal", "highlightStroke", "color", "speed", "animateStroke", "animateSingleStroke", "mutationStateFunc", "curCharState", "mutationState", "showStroke", "animateCharacter", "fadeDuration", "delayBetweenStrokes", "concat", "animateCharacterLoop", "delayBetweenLoops", "startQuiz", "startStrokeNum", "characterActions", "startUserStroke", "updateUserStroke", "userStrokeId", "hideUserStroke", "removeAllUserStrokes", "userStrokeIds", "highlightCompleteChar", "getDrawnPath", "geometry", "Quiz", "positioner", "_currentStrokeIndex", "_mistakesOnStroke", "_totalMistakes", "_character", "_positioner", "_userStrokesIds", "quizActions", "_options", "startIndex", "quizStartStrokeNum", "strokeFadeDuration", "_userStroke", "endUserStroke", "strokeId", "continueUserStroke", "nextPoints", "setPositioner", "acceptBackwardsStrokes", "markStrokeCorrectAfterMisses", "currentStroke", "_getCurrentStroke", "isForceAccepted", "isAccepted", "_handleSuccess", "_handleFailure", "showHintAfterMisses", "strokeHighlightSpeed", "_getStrokeData", "isCorrect", "mistakesOnStroke", "totalMistakes", "strokesRemaining", "<PERSON><PERSON><PERSON>", "isBackwards", "nextStroke", "onComplete", "highlightOnComplete", "highlightCompleteColor", "strokeHighlightDuration", "animation", "isComplete", "onCorrectStroke", "onMistake", "createElm", "elmType", "document", "createElementNS", "attr", "elm", "name", "value", "setAttributeNS", "attrs", "attrsMap", "Object", "keys", "attrName", "urlIdRef", "prefix", "location", "href", "removeElm", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "StrokeRendererBase", "_pathLength", "STROKE_WIDTH", "_getStrokeDashoffset", "_getColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_oldProps", "mount", "_animationPath", "svg", "_clip", "_strokePath", "maskId", "style", "extendedMaskPoints", "toString", "fill", "append<PERSON><PERSON><PERSON>", "defs", "render", "props", "_this$_oldProps", "strokeDashoffset", "_this$_oldProps2", "<PERSON><PERSON><PERSON><PERSON>", "_strokeRenderers", "subTarget", "createSubRenderTarget", "_group", "<PERSON><PERSON><PERSON><PERSON>", "display", "removeProperty", "colorsChanged", "_this$_oldProps3", "_this$_oldProps4", "User<PERSON><PERSON><PERSON><PERSON><PERSON>", "_path", "strokeWidth", "destroy", "HanziWriter<PERSON><PERSON><PERSON>", "_main<PERSON><PERSON><PERSON><PERSON><PERSON>", "_outline<PERSON><PERSON><PERSON><PERSON><PERSON>", "_highlight<PERSON><PERSON><PERSON><PERSON><PERSON>", "_userStrokeRenderers", "<PERSON><PERSON><PERSON><PERSON>", "group", "_positioned<PERSON><PERSON><PERSON>", "userStrokeProps", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "RenderTargetBase", "node", "addPointerStartListener", "addEventListener", "evt", "_eventify", "_getMousePoint", "_getTouchPoint", "addPointerMoveListener", "addPointerEndListener", "getBoundingClientRect", "updateDimensions", "setAttribute", "pointFunc", "getPoint", "call", "preventDefault", "left", "top", "clientX", "clientY", "touches", "RenderTarget", "_pt", "createSVGPoint", "init", "elmOrId", "element", "getElementById", "nodeType", "nodeName", "localPt", "matrixTransform", "getScreenCTM", "_this$node$getScreenC", "inverse", "_this$node$getScreenC2", "createRenderTarget", "drawPath", "ctx", "beginPath", "moveTo", "lineTo", "pathStringToCanvas", "pathParts", "part", "commands", "cmd", "rawParams", "params", "param", "bezierCurveTo", "quadraticCurveTo", "usePath2D", "Path2D", "_path2D", "_pathCmd", "_extendedMaskPoints", "save", "clip", "globalAlpha", "dashOffset", "strokeStyle", "fillStyle", "lineWidth", "lineCap", "lineJoin", "setLineDash", "lineDashOffset", "restore", "renderUserStroke", "_target", "_animationFrame", "getContext", "clearRect", "translate", "transform", "draw", "canvas", "createElement", "VERSION", "getCharDataUrl", "char", "defaultCharDataLoader", "onLoad", "onError", "xhr", "XMLHttpRequest", "overrideMimeType", "open", "onerror", "event", "onreadystatechange", "readyState", "status", "JSON", "parse", "responseText", "send", "defaultOptions", "<PERSON>ar<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoadCharDataError", "onLoadCharDataSuccess", "renderer", "strokeAnimationSpeed", "outlineWidth", "rendererOverride", "LoadingManager", "_loadCounter", "_isLoading", "loadingFailed", "_debouncedLoad", "wrappedResolve", "data", "wrappedReject", "reason", "_reject", "returnedData", "catch", "_setupLoadingPromise", "reject", "err", "_loadingChar", "loadCharData", "promise", "HanziWriter", "canvas<PERSON><PERSON><PERSON>", "svg<PERSON><PERSON><PERSON>", "_renderer", "_assignOptions", "_loadingManager", "_setupListeners", "create", "writer", "<PERSON><PERSON><PERSON><PERSON>", "loadCharacterData", "loadingManager", "_loadingOptions", "getScalingTransform", "_withData", "_this$_renderState", "res", "_this$_renderState2", "cancelQuiz", "_this$_renderState3", "_this$_renderState4", "loopCharacterAnimation", "pauseAnimation", "_this$_renderState5", "resumeAnimation", "_this$_renderState6", "_this$_renderState7", "hideOutline", "_this$_renderState8", "_hanziWriter<PERSON><PERSON><PERSON>", "hanziWriter<PERSON><PERSON><PERSON>", "_initAndMountHanziWriterRenderer", "_quiz", "fixedColorVal", "mappedColor", "_this$_renderState9", "quiz", "quizOptions", "skipQuizStroke", "_char", "_withDataPromise", "pathStrings", "getCharacterData", "mergedOptions", "strokeAnimationDuration", "_fillWidthAndHeight", "filledOpts", "minDim", "func"], "mappings": ";;;;;;;AAGA,MAAMA,SAAS,GAAG,OAAOC,MAAP,KAAkB,WAAlB,GAAgCC,MAAhC,GAAyCD,MAA3D;AAEO,MAAME,cAAc,GACxBH,SAAS,CAACI,WAAV,KAA0B,MAAMJ,SAAS,CAACI,WAAV,CAAsBC,GAAtB,EAAhC,MAAkE,MAAMC,IAAI,CAACD,GAAL,EAAxE,CADI;AAEA,MAAME,qBAAqB,GAChC,0BAAAP,SAAS,CAACO,qGAAuBC,KAAKR,UAAtC,MACES,QAAD,IAAcC,UAAU,CAAC,MAAMD,QAAQ,CAACN,cAAc,EAAf,CAAf,EAAmC,OAAO,EAA1C,CADzB,CADK;AAGA,MAAMQ,oBAAoB,GAC/B,0BAAAX,SAAS,CAACW,oGAAsBH,KAAKR,UAArC,KAAmDY,YAD9C;AAoBD,SAAUC,OAAV,CAA0BC,GAA1B,EAA4C;SACzCA,GAAG,CAACA,GAAG,CAACC,MAAJ,GAAa,CAAd;;AAGL,MAAMC,QAAQ,GAAG,CAACC,KAAD,EAAgBF,MAAhB,KAAkC;;MAEpDE,KAAK,GAAG,GAAG;WACNF,MAAM,GAAGE;;;SAEXA;AALF,CAAA;AAQA,MAAMC,WAAW,GAAG,CAAIJ,GAAJ,EAAmBG,KAAnB,KAAoC;;SAEtDH,GAAG,CAACE,QAAQ,CAACC,KAAD,EAAQH,GAAG,CAACC,MAAZ,CAAT;AAFL,CAAA;AAKD,SAAUI,gBAAV,CAA8BC,IAA9B,EAAuCC,QAAvC,EAAgF;QAC9EC,MAAM,GAAG,EAAE,GAAGF;AAAL;;OACV,MAAMG,OAAOF,UAAU;UACpBG,OAAO,GAAGJ,IAAI,CAACG,GAAD;UACdE,WAAW,GAAGJ,QAAQ,CAACE,GAAD;;QACxBC,OAAO,KAAKC,aAAa;;;;QAI3BD,OAAO,IACPC,WADA,IAEA,OAAOD,OAAP,KAAmB,QAFnB,IAGA,OAAOC,WAAP,KAAuB,QAHvB,IAIA,CAACC,KAAK,CAACC,OAAN,CAAcF,WAAd,GACD;AACAH,MAAAA,MAAM,CAACC,GAAD,CAAN,GAAcJ,gBAAgB,CAACK,OAAD,EAAUC,WAAV,CAA9B;AAPF,WAQO;;AAELH,MAAAA,MAAM,CAACC,GAAD,CAAN,GAAcE,WAAd;;;;SAGGH;;AAGT;;AACM,SAAUM,OAAV,CAAkBC,KAAlB,EAAiCC,GAAjC,EAAyC;QACvCC,KAAK,GAAGF,KAAK,CAACG,KAAN,CAAY,GAAZ;QACRC,KAAK,GAAQ;MACfC,OAAO,GAAGD;;OACT,IAAIE,CAAC,GAAG,GAAGA,CAAC,GAAGJ,KAAK,CAAChB,QAAQoB,CAAC,IAAI;UAC/BC,GAAG,GAAGD,CAAC,KAAKJ,KAAK,CAAChB,MAAN,GAAe,CAArB,GAAyBe,GAAzB,GAA+B;AAC3CI,IAAAA,OAAO,CAACH,KAAK,CAACI,CAAD,CAAN,CAAP,GAAoBC,GAApB;AACAF,IAAAA,OAAO,GAAGE,GAAV;;;SAEKH;;AAGT,IAAII,KAAK,GAAG,CAAZ;AAEM,SAAUC,OAAV,GAAiB;AACrBD,EAAAA,KAAK;SACEA;;AAGH,SAAUE,OAAV,CAAkBzB,GAAlB,EAA+B;QAC7B0B,GAAG,GAAG1B,GAAG,CAAC2B,MAAJ,CAAW,CAACC,GAAD,EAAMC,GAAN,KAAcA,GAAG,GAAGD,GAA/B,EAAoC,CAApC;SACLF,GAAG,GAAG1B,GAAG,CAACC;;AAOb,SAAU6B,iBAAV,CAA4BC,WAA5B,EAA+C;QAC7CC,eAAe,GAAGD,WAAW,CAACE,WAAZ,GAA0BC,IAA1B,GAD2B;;MAG/C,wBAAwBC,IAAxB,CAA6BH,eAA7B,GAA+C;QAC7CI,QAAQ,GAAGJ,eAAe,CAACK,SAAhB,CAA0B,CAA1B,EAA6BnB,KAA7B,CAAmC,EAAnC;;QACXkB,QAAQ,CAACnC,MAAT,KAAoB,GAAG;AACzBmC,MAAAA,QAAQ,GAAG,CACTA,QAAQ,CAAC,CAAD,CADC,EAETA,QAAQ,CAAC,CAAD,CAFC,EAGTA,QAAQ,CAAC,CAAD,CAHC,EAITA,QAAQ,CAAC,CAAD,CAJC,EAKTA,QAAQ,CAAC,CAAD,CALC,EAMTA,QAAQ,CAAC,CAAD,CANC,CAAX;;;UASIE,MAAM,MAAMF,QAAQ,CAACG,IAAT,CAAc,EAAd;WACX;AACLC,MAAAA,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAACI,KAAP,CAAa,CAAb,EAAgB,CAAhB,CAAD,EAAqB,EAArB,CADN;AAELC,MAAAA,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAACI,KAAP,CAAa,CAAb,EAAgB,CAAhB,CAAD,EAAqB,EAArB,CAFN;AAGLE,MAAAA,CAAC,EAAEH,QAAQ,CAACH,MAAM,CAACI,KAAP,CAAa,CAAb,EAAgB,CAAhB,CAAD,EAAqB,EAArB,CAHN;AAILG,MAAAA,CAAC,EAAE;AAJE;;;QAOHC,QAAQ,GAAGd,eAAe,CAACe,KAAhB,CACf,iEADe;;MAGbD,UAAU;WACL;AACLN,MAAAA,CAAC,EAAEC,QAAQ,CAACK,QAAQ,CAAC,CAAD,CAAT,EAAc,EAAd,CADN;AAELH,MAAAA,CAAC,EAAEF,QAAQ,CAACK,QAAQ,CAAC,CAAD,CAAT,EAAc,EAAd,CAFN;AAGLF,MAAAA,CAAC,EAAEH,QAAQ,CAACK,QAAQ,CAAC,CAAD,CAAT,EAAc,EAAd,CAHN;;AAKLD,MAAAA,CAAC,EAAEG,UAAU,CAACF,QAAQ,CAAC,CAAD,CAAR,IAAe,CAAhB,EAAmB,EAAnB;AALR;;;QAQH,IAAIG,KAAJ,mBAA4BlB,aAA5B;;AAGD,MAAMG,IAAI,GAAIgB,MAAD,IAAoBA,MAAM,CAACC,OAAP,CAAe,MAAf,EAAuB,EAAvB,EAA2BA,OAA3B,CAAmC,MAAnC,EAA2C,EAA3C,CAAjC;AAGP;;AACM,SAAUC,SAAV,CAAuBC,IAAvB,EAAgCC,KAAhC,EAA6C;QAC3CtC,GAAG,GAAsB;;OAC1B,IAAIK,CAAC,GAAG,GAAGA,CAAC,GAAGiC,OAAOjC,CAAC,IAAI;AAC9BL,IAAAA,GAAG,CAACK,CAAD,CAAH,GAASgC,IAAT;;;SAEKrC;;;AAIH,SAAUuC,WAAV,CAAyBD,KAAzB,EAAwCE,EAAxC,EAA4D;QAC1DxC,GAAG,GAAsB;;OAC1B,IAAIK,CAAC,GAAG,GAAGA,CAAC,GAAGiC,OAAOjC,CAAC,IAAI;AAC9BL,IAAAA,GAAG,CAACK,CAAD,CAAH,GAASmC,EAAE,CAACnC,CAAD,CAAX;;;SAEKL;;AAGT,MAAMyC,EAAE,GAAG,yBAAAvE,SAAS,CAACwE,uFAAWC,SAArB,KAAkC,EAA7C;AAEO,MAAMC,WAAW,GACtBH,EAAE,CAACI,OAAH,CAAW,OAAX,IAAsB,CAAtB,IAA2BJ,EAAE,CAACI,OAAH,CAAW,UAAX,IAAyB,CAApD,IAAyDJ,EAAE,CAACI,OAAH,CAAW,OAAX,IAAsB,CAD1E;;AAIA,MAAMC,IAAI,GAAG,MAAK,EAAlB;;AC3FO,MAAOC,WAAP,CAAkB;AAM9BC,EAAAA,WAAA,CACEC,SADF,EAEEC,OAFF,EAGEC,aAAA,GAAuCL,IAHzC,EAG6C;SAR7CM,kBAAmC;SAU5BC,iBAAiBF;SAEjBG,QAAQ;AACXJ,MAAAA,OAAO,EAAE;AACPK,QAAAA,mBAAmB,EAAEL,OAAO,CAACK,mBADtB;AAEPC,QAAAA,YAAY,EAAEN,OAAO,CAACM,YAFf;AAGPC,QAAAA,YAAY,EAAE3C,iBAAiB,CAACoC,OAAO,CAACO,YAAT,CAHxB;AAIPC,QAAAA,WAAW,EAAE5C,iBAAiB,CAACoC,OAAO,CAACQ,WAAT,CAJvB;AAKPC,QAAAA,YAAY,EAAE7C,iBAAiB,CAACoC,OAAO,CAACS,YAAT,CALxB;AAMPC,QAAAA,YAAY,EAAE9C,iBAAiB,CAACoC,OAAO,CAACU,YAAR,IAAwBV,OAAO,CAACQ,WAAjC,CANxB;AAOPG,QAAAA,cAAc,EAAE/C,iBAAiB,CAACoC,OAAO,CAACW,cAAT;AAP1B,OADE;AAUXZ,MAAAA,SAAS,EAAE;AACTa,QAAAA,IAAI,EAAE;AACJC,UAAAA,OAAO,EAAEb,OAAO,CAACc,aAAR,GAAwB,CAAxB,GAA4B,CADjC;AAEJC,UAAAA,OAAO,EAAE;AAFL,SADG;AAKTC,QAAAA,OAAO,EAAE;AACPH,UAAAA,OAAO,EAAEb,OAAO,CAACiB,WAAR,GAAsB,CAAtB,GAA0B,CAD5B;AAEPF,UAAAA,OAAO,EAAE;AAFF,SALA;AASTG,QAAAA,SAAS,EAAE;AACTL,UAAAA,OAAO,EAAE,CADA;AAETE,UAAAA,OAAO,EAAE;AAFA;AATF,OAVA;AAwBXI,MAAAA,WAAW,EAAE;AAxBF;;SA2BR,IAAIhE,CAAC,GAAG,GAAGA,CAAC,GAAG4C,SAAS,CAACgB,OAAV,CAAkBhF,QAAQoB,CAAC,IAAI;WAC5CiD,MAAML,UAAUa,KAAKG,QAAQ5D,KAAK;AACrC0D,QAAAA,OAAO,EAAE,CAD4B;AAErCO,QAAAA,cAAc,EAAE;AAFqB;WAKlChB,MAAML,UAAUiB,QAAQD,QAAQ5D,KAAK;AACxC0D,QAAAA,OAAO,EAAE,CAD+B;AAExCO,QAAAA,cAAc,EAAE;AAFwB;WAKrChB,MAAML,UAAUmB,UAAUH,QAAQ5D,KAAK;AAC1C0D,QAAAA,OAAO,EAAE,CADiC;AAE1CO,QAAAA,cAAc,EAAE;AAF0B;;;;AAOhDC,EAAAA,sBAAsB,CAACpB,aAAD,EAAqC;SACpDE,iBAAiBF;;;AAGxBqB,EAAAA,WAAW,CAACC,YAAD,EAAkD;UACrDC,SAAS,GAAGrF,gBAAgB,CAAC,KAAKiE,KAAN,EAAamB,YAAb;;SAC7BpB,eAAeqB,WAAW,KAAKpB;;SAC/BA,QAAQoB;;;AAGfC,EAAAA,GAAG,CACDC,SADC,EAED1B,OAAA,GAEI,EAJH,EAIK;UAEA2B,MAAM,GAAGD,SAAS,CAACE,GAAV,CAAeC,GAAD,IAASA,GAAG,CAAChF,KAA3B;SAEViF,gBAAgBH;WAEd,IAAII,OAAJ,CAAaC,OAAD,IAAgC;YAC3CC,aAAa,GAAkB;AACnCC,QAAAA,SAAS,EAAE,IADwB;AAEnCC,QAAAA,MAAM,EAAE,CAF2B;AAGnCC,QAAAA,QAAQ,EAAEJ,OAHyB;AAInCK,QAAAA,UAAU,EAAEX,SAJuB;AAKnCY,QAAAA,KAAK,EAAEtC,OAAO,CAACuC,IALoB;AAMnCC,QAAAA,OAAO,EAAEb;AAN0B;;WAQhCzB,gBAAgBuC,KAAKR;;WACrBS,KAAKT;AAVL,KAAA;;;AAcTS,EAAAA,IAAI,CAACT,aAAD,EAA6B;QAC3B,CAACA,aAAa,CAACC,WAAW;;;;UAIxBR,SAAS,GAAGO,aAAa,CAACI;;QAC5BJ,aAAa,CAACE,MAAd,IAAwBT,SAAS,CAAC3F,QAAQ;UACxCkG,aAAa,CAACK,OAAO;AACvBL,QAAAA,aAAa,CAACE,MAAd,GAAuB,CAAvB,CADuB;AAAzB,aAEO;AACLF,QAAAA,aAAa,CAACC,SAAd,GAA0B,KAA1B,CADK;;aAEAhC,kBAAkB,KAAKA,eAAL,CAAqByC,MAArB,CACpBC,KAAD,IAAWA,KAAK,KAAKX,aADA,EAFlB;;AAMLA,QAAAA,aAAa,CAACG,QAAd,CAAuB;AAAES,UAAAA,QAAQ,EAAE;AAAZ,SAAvB;;;;;;UAKEC,cAAc,GAAGb,aAAa,CAACI,UAAd,CAAyBJ,aAAa,CAACE,MAAvC;AAEvBW,IAAAA,cAAc,CAACrB,GAAf,CAAmB,IAAnB,EAAyBsB,IAAzB,CAA8B,MAAK;UAC7Bd,aAAa,CAACC,WAAW;AAC3BD,QAAAA,aAAa,CAACE,MAAd,GAD2B;;aAEtBO,KAAKT;;AAHd,KAAA;;;AAQFe,EAAAA,mBAAmB,GAAA;WACV,KAAK9C,eAAL,CAAqB0B,GAArB,CAA0BgB,KAAD,IAAWA,KAAK,CAACP,UAAN,CAAiBO,KAAK,CAACT,MAAvB,CAApC;;;AAGTc,EAAAA,QAAQ,GAAA;SACDD,sBAAsBE,QAASC,QAAD,IAAcA,QAAQ,CAACC,KAAT;;;AAGnDC,EAAAA,SAAS,GAAA;SACFL,sBAAsBE,QAASC,QAAD,IAAcA,QAAQ,CAACG,MAAT;;;AAGnDxB,EAAAA,eAAe,CAACyB,cAAD,EAAyB;SACjC,MAAMX,SAAS,KAAK1C,iBAAiB;WACnC,MAAMsD,WAAWZ,KAAK,CAACJ,SAAS;aAC9B,MAAMiB,iBAAiBF,gBAAgB;cACtCC,OAAO,CAACE,UAAR,CAAmBD,aAAnB,KAAqCA,aAAa,CAACC,UAAd,CAAyBF,OAAzB,GAAmC;iBACrEG,qBAAqBf;;;;;;;AAOpCgB,EAAAA,SAAS,GAAA;SACF9B,gBAAgB,CAAC,EAAD;;;AAGvB6B,EAAAA,oBAAoB,CAAC1B,aAAD,EAA6B;;;AAC/CA,IAAAA,aAAa,CAACC,SAAd,GAA0B,KAA1B;;SACK,IAAI/E,CAAC,GAAG8E,aAAa,CAACE,QAAQhF,CAAC,GAAG8E,aAAa,CAACI,UAAd,CAAyBtG,QAAQoB,CAAC,IAAI;AAC3E8E,MAAAA,aAAa,CAACI,UAAd,CAAyBlF,CAAzB,EAA4B0G,MAA5B,CAAmC,IAAnC;;;6BAGF5B,aAAa,CAACG,6FAAdH,eAAyB;AAAEY,MAAAA,QAAQ,EAAE;AAAZ;SAEpB3C,kBAAkB,KAAKA,eAAL,CAAqByC,MAArB,CACpBC,KAAD,IAAWA,KAAK,KAAKX,aADA;;;AA9JK;;ACvEzB,MAAM6B,QAAQ,GAAG,CAACC,EAAD,EAAYC,EAAZ,MAA2B;AAAEC,EAAAA,CAAC,EAAEF,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAAf;AAAkBC,EAAAA,CAAC,EAAEH,EAAE,CAACG,CAAH,GAAOF,EAAE,CAACE;AAA/B,CAA3B,CAAjB;AAEA,MAAMC,SAAS,GAAIC,KAAD,IACvBC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASH,KAAK,CAACH,CAAf,EAAkB,CAAlB,IAAuBI,IAAI,CAACE,GAAL,CAASH,KAAK,CAACF,CAAf,EAAkB,CAAlB,CAAjC,CADK;AAGA,MAAMM,QAAQ,GAAG,CAACC,MAAD,EAAgBC,MAAhB,KACtBP,SAAS,CAACL,QAAQ,CAACW,MAAD,EAASC,MAAT,CAAT,CADJ;AAGA,MAAMC,MAAM,GAAG,CAACF,MAAD,EAAgBC,MAAhB,KACpBD,MAAM,CAACR,CAAP,KAAaS,MAAM,CAACT,CAApB,IAAyBQ,MAAM,CAACP,CAAP,KAAaQ,MAAM,CAACR,CADxC;AAGA,MAAMU,KAAK,GAAG,CAACR,KAAD,EAAeS,SAAS,GAAG,CAA3B,KAAgC;QAC7CC,UAAU,GAAGD,SAAS,GAAG;SACxB;AACLZ,IAAAA,CAAC,EAAEI,IAAI,CAACO,KAAL,CAAWE,UAAU,GAAGV,KAAK,CAACH,CAA9B,IAAmCa,UADjC;AAELZ,IAAAA,CAAC,EAAEG,IAAI,CAACO,KAAL,CAAWE,UAAU,GAAGV,KAAK,CAACF,CAA9B,IAAmCY;AAFjC;AAFF,CAAA;AAQA,MAAM/I,MAAM,GAAIgJ,MAAD,IAAoB;MACpCC,SAAS,GAAGD,MAAM,CAAC,CAAD;QAChBE,eAAe,GAAGF,MAAM,CAACvG,KAAP,CAAa,CAAb;SACjByG,eAAe,CAACxH,MAAhB,CAAuB,CAACC,GAAD,EAAM0G,KAAN,KAAe;UACrCc,IAAI,GAAGV,QAAQ,CAACJ,KAAD,EAAQY,SAAR;AACrBA,IAAAA,SAAS,GAAGZ,KAAZ;WACO1G,GAAG,GAAGwH;AAHR,GAAA,EAIJ,CAJI;AAHF,CAAA;AAUA,MAAMC,gBAAgB,GAAG,CAACV,MAAD,EAAgBC,MAAhB,KAAiC;QACzDU,aAAa,GAAGX,MAAM,CAACR,CAAP,GAAWS,MAAM,CAACT,CAAlB,GAAsBQ,MAAM,CAACP,CAAP,GAAWQ,MAAM,CAACR;SACvDkB,aAAa,GAAGjB,SAAS,CAACM,MAAD,CAAzB,GAAoCN,SAAS,CAACO,MAAD;AAF/C,CAAA;AAKP;;;AAGG;;AACI,MAAMW,kBAAkB,GAAG,CAACtB,EAAD,EAAYC,EAAZ,EAAuBkB,IAAvB,KAAuC;QACjEI,IAAI,GAAGxB,QAAQ,CAACE,EAAD,EAAKD,EAAL;QACfwB,IAAI,GAAGL,IAAI,GAAGf,SAAS,CAACmB,IAAD;SACtB;AAAErB,IAAAA,CAAC,EAAED,EAAE,CAACC,CAAH,GAAOsB,IAAI,GAAGD,IAAI,CAACrB,CAAxB;AAA2BC,IAAAA,CAAC,EAAEF,EAAE,CAACE,CAAH,GAAOqB,IAAI,GAAGD,IAAI,CAACpB;AAAjD;AAHF,CAAA;AAMP;;AACO,MAAMsB,WAAW,GAAG,CAACC,MAAD,EAAkBC,MAAlB,KAAqC;QACxDC,SAAS,GAAGF,MAAM,CAAC1J,MAAP,IAAiB2J,MAAM,CAAC3J,MAAxB,GAAiC0J,MAAjC,GAA0CC;QACtDE,UAAU,GAAGH,MAAM,CAAC1J,MAAP,IAAiB2J,MAAM,CAAC3J,MAAxB,GAAiC2J,MAAjC,GAA0CD;;QAEvDI,OAAO,GAAG,CACd1I,CADc,EAEd2I,CAFc,EAGdC,cAHc,EAIdC,aAJc,KAKJ;QACN7I,CAAC,KAAK,CAAN,IAAW2I,CAAC,KAAK,GAAG;aACftB,QAAQ,CAACmB,SAAS,CAAC,CAAD,CAAV,EAAeC,UAAU,CAAC,CAAD,CAAzB;;;QAGbzI,CAAC,GAAG,CAAJ,IAAS2I,CAAC,KAAK,GAAG;aACbzB,IAAI,CAAC4B,GAAL,CAASF,cAAc,CAAC,CAAD,CAAvB,EAA4BvB,QAAQ,CAACmB,SAAS,CAACxI,CAAD,CAAV,EAAeyI,UAAU,CAAC,CAAD,CAAzB,CAApC;;;UAGHM,UAAU,GAAGF,aAAa,CAACA,aAAa,CAACjK,MAAd,GAAuB,CAAxB;;QAE5BoB,CAAC,KAAK,CAAN,IAAW2I,CAAC,GAAG,GAAG;aACbzB,IAAI,CAAC4B,GAAL,CAASC,UAAT,EAAqB1B,QAAQ,CAACmB,SAAS,CAAC,CAAD,CAAV,EAAeC,UAAU,CAACE,CAAD,CAAzB,CAA7B;;;WAGFzB,IAAI,CAAC4B,GAAL,CACL5B,IAAI,CAAC8B,GAAL,CAASJ,cAAc,CAACD,CAAD,CAAvB,EAA4BC,cAAc,CAACD,CAAC,GAAG,CAAL,CAA1C,EAAmDI,UAAnD,CADK,EAEL1B,QAAQ,CAACmB,SAAS,CAACxI,CAAD,CAAV,EAAeyI,UAAU,CAACE,CAAD,CAAzB,CAFH;AApBT;;MA0BIC,cAAc,GAAa;;OAC1B,IAAI5I,CAAC,GAAG,GAAGA,CAAC,GAAGwI,SAAS,CAAC5J,QAAQoB,CAAC,IAAI;UACnC6I,aAAa,GAAa;;SAC3B,IAAIF,CAAC,GAAG,GAAGA,CAAC,GAAGF,UAAU,CAAC7J,QAAQ+J,CAAC,IAAI;;;;;AAK1CE,MAAAA,aAAa,CAACvD,IAAd,CAAmBoD,OAAO,CAAC1I,CAAD,EAAI2I,CAAJ,EAAOC,cAAP,EAAuBC,aAAvB,CAA1B;;;AAEFD,IAAAA,cAAc,GAAGC,aAAjB;;;SAGKD,cAAc,CAACH,UAAU,CAAC7J,MAAX,GAAoB,CAArB;AA3ChB,CAAA;AA8CP;;AACO,MAAMqK,cAAc,GAAG,CAACC,KAAD,EAAiBC,MAAM,GAAG,IAA1B,KAAkC;QACxDC,QAAQ,GAAGF,KAAK,CAAC7H,KAAN,CAAY,CAAZ,EAAe,CAAf;;OAEZ,MAAM4F,SAASiC,KAAK,CAAC7H,KAAN,CAAY,CAAZ,GAAgB;UAC5BgI,SAAS,GAAGD,QAAQ,CAACA,QAAQ,CAACxK,MAAT,GAAkB,CAAnB;UACpB0K,MAAM,GAAGjC,QAAQ,CAACJ,KAAD,EAAQoC,SAAR;;QACnBC,MAAM,GAAGH,QAAQ;YACbI,YAAY,GAAGrC,IAAI,CAACsC,IAAL,CAAUF,MAAM,GAAGH,MAAnB;YACfM,SAAS,GAAGH,MAAM,GAAGC;;WACtB,IAAIvJ,CAAC,GAAG,GAAGA,CAAC,GAAGuJ,cAAcvJ,CAAC,IAAI;AACrCoJ,QAAAA,QAAQ,CAAC9D,IAAT,CAAc4C,kBAAkB,CAACjB,KAAD,EAAQoC,SAAR,EAAmB,CAAC,CAAD,GAAKI,SAAL,IAAkBzJ,CAAC,GAAG,CAAtB,CAAnB,CAAhC;;AAJJ,WAMO;AACLoJ,MAAAA,QAAQ,CAAC9D,IAAT,CAAc2B,KAAd;;;;SAIGmC;AAjBF,CAAA;AAoBP;;AACO,MAAMM,YAAY,GAAG,CAACR,KAAD,EAAiBS,SAAS,GAAG,EAA7B,KAAmC;QACvDC,QAAQ,GAAGhL,MAAM,CAACsK,KAAD;QACjBW,UAAU,GAAGD,QAAQ,IAAID,SAAS,GAAG,CAAhB;QACrBG,aAAa,GAAG,CAACZ,KAAK,CAAC,CAAD,CAAN;QAChBa,QAAQ,GAAGrL,OAAO,CAACwK,KAAD;QAClBc,oBAAoB,GAAGd,KAAK,CAAC7H,KAAN,CAAY,CAAZ;;OAExB,IAAIrB,CAAC,GAAG,GAAGA,CAAC,GAAG2J,SAAS,GAAG,GAAG3J,CAAC,IAAI;QAClC6H,SAAS,GAAUnJ,OAAO,CAACoL,aAAD;QAC1BG,aAAa,GAAGJ;QAChBK,iBAAiB,GAAG;;WACjB,CAACA,mBAAmB;YACnBC,aAAa,GAAG9C,QAAQ,CAACQ,SAAD,EAAYmC,oBAAoB,CAAC,CAAD,CAAhC;;UAC1BG,aAAa,GAAGF,eAAe;AACjCA,QAAAA,aAAa,IAAIE,aAAjB;AACAtC,QAAAA,SAAS,GAAGmC,oBAAoB,CAACI,KAArB,EAAZ;AAFF,aAGO;cACCC,SAAS,GAAGnC,kBAAkB,CAClCL,SADkC,EAElCmC,oBAAoB,CAAC,CAAD,CAFc,EAGlCC,aAAa,GAAGE,aAHkB;;AAKpCL,QAAAA,aAAa,CAACxE,IAAd,CAAmB+E,SAAnB;AACAH,QAAAA,iBAAiB,GAAG,IAApB;;;;;AAKNJ,EAAAA,aAAa,CAACxE,IAAd,CAAmByE,QAAnB;SAEOD;AA9BF,CAAA;AAiCP;;AACO,MAAMQ,cAAc,GAAIpB,KAAD,IAAmB;QACzCqB,aAAa,GAAGb,YAAY,CAACR,KAAD;QAC5BsB,KAAK,GAAGpK,OAAO,CAACmK,aAAa,CAAC9F,GAAd,CAAmBwC,KAAD,IAAWA,KAAK,CAACH,CAAnC,CAAD;QACf2D,KAAK,GAAGrK,OAAO,CAACmK,aAAa,CAAC9F,GAAd,CAAmBwC,KAAD,IAAWA,KAAK,CAACF,CAAnC,CAAD;QACf2D,IAAI,GAAG;AAAE5D,IAAAA,CAAC,EAAE0D,KAAL;AAAYzD,IAAAA,CAAC,EAAE0D;AAAf;QACPE,eAAe,GAAGJ,aAAa,CAAC9F,GAAd,CAAmBwC,KAAD,IAAWN,QAAQ,CAACM,KAAD,EAAQyD,IAAR,CAArC;QAClBE,KAAK,GAAG1D,IAAI,CAACC,IAAL,CACZ/G,OAAO,CAAC,CACN8G,IAAI,CAACE,GAAL,CAASuD,eAAe,CAAC,CAAD,CAAf,CAAmB7D,CAA5B,EAA+B,CAA/B,IAAoCI,IAAI,CAACE,GAAL,CAASuD,eAAe,CAAC,CAAD,CAAf,CAAmB5D,CAA5B,EAA+B,CAA/B,CAD9B,EAENG,IAAI,CAACE,GAAL,CAAS1I,OAAO,CAACiM,eAAD,CAAP,CAAyB7D,CAAlC,EAAqC,CAArC,IAA0CI,IAAI,CAACE,GAAL,CAAS1I,OAAO,CAACiM,eAAD,CAAP,CAAyB5D,CAAlC,EAAqC,CAArC,CAFpC,CAAD,CADK;QAMR8D,WAAW,GAAGF,eAAe,CAAClG,GAAhB,CAAqBwC,KAAD,KAAY;AAClDH,IAAAA,CAAC,EAAEG,KAAK,CAACH,CAAN,GAAU8D,KADqC;AAElD7D,IAAAA,CAAC,EAAEE,KAAK,CAACF,CAAN,GAAU6D;AAFqC,GAAZ,CAApB;SAIb3B,cAAc,CAAC4B,WAAD;AAhBhB,CAAA;;AAoBA,MAAMC,MAAM,GAAG,CAAC5B,KAAD,EAAiB6B,KAAjB,KAAkC;SAC/C7B,KAAK,CAACzE,GAAN,CAAWwC,KAAD,KAAY;AAC3BH,IAAAA,CAAC,EAAEI,IAAI,CAAC8D,GAAL,CAASD,KAAT,IAAkB9D,KAAK,CAACH,CAAxB,GAA4BI,IAAI,CAAC+D,GAAL,CAASF,KAAT,IAAkB9D,KAAK,CAACF,CAD5B;AAE3BA,IAAAA,CAAC,EAAEG,IAAI,CAAC+D,GAAL,CAASF,KAAT,IAAkB9D,KAAK,CAACH,CAAxB,GAA4BI,IAAI,CAAC8D,GAAL,CAASD,KAAT,IAAkB9D,KAAK,CAACF;AAF5B,GAAZ,CAAV;AADF,CAAA;;AAQA,MAAMmE,qBAAqB,GAAItD,MAAD,IAAoB;MACnDA,MAAM,CAAChJ,MAAP,GAAgB,GAAG,OAAOgJ,MAAP;QACjBuD,cAAc,GAAG,CAACvD,MAAM,CAAC,CAAD,CAAP,EAAYA,MAAM,CAAC,CAAD,CAAlB;AACvBA,EAAAA,MAAM,CAACvG,KAAP,CAAa,CAAb,EAAgB0E,OAAhB,CAAyBkB,KAAD,IAAU;UAC1BmE,iBAAiB,GAAGD,cAAc,CAACvM;UACnCyM,OAAO,GAAG1E,QAAQ,CAACM,KAAD,EAAQkE,cAAc,CAACC,iBAAiB,GAAG,CAArB,CAAtB;UAClBE,QAAQ,GAAG3E,QAAQ,CACvBwE,cAAc,CAACC,iBAAiB,GAAG,CAArB,CADS,EAEvBD,cAAc,CAACC,iBAAiB,GAAG,CAArB,CAFS,EAHO;;UAQ1BG,UAAU,GAAGF,OAAO,CAACtE,CAAR,GAAYuE,QAAQ,CAACxE,CAArB,GAAyBuE,OAAO,CAACvE,CAAR,GAAYwE,QAAQ,CAACvE,CAA9C,KAAoD;;QACnEwE,YAAY;AACdJ,MAAAA,cAAc,CAACK,GAAf;;;AAEFL,IAAAA,cAAc,CAAC7F,IAAf,CAAoB2B,KAApB;AAZF,GAAA;SAcOkE;AAjBF,CAAA;AAoBD,SAAUM,aAAV,CAAwB7D,MAAxB,EAAyC8D,KAAK,GAAG,KAAjD,EAAsD;QACpDC,KAAK,GAAGlE,KAAK,CAACG,MAAM,CAAC,CAAD,CAAP;QACbgE,eAAe,GAAGhE,MAAM,CAACvG,KAAP,CAAa,CAAb;MACpBwK,UAAU,QAAQF,KAAK,CAAC7E,KAAK6E,KAAK,CAAC5E;AACvC6E,EAAAA,eAAe,CAAC7F,OAAhB,CAAyBkB,KAAD,IAAU;UAC1B6E,YAAY,GAAGrE,KAAK,CAACR,KAAD;AAC1B4E,IAAAA,UAAU,UAAUC,YAAY,CAAChF,KAAKgF,YAAY,CAAC/E,GAAnD;AAFF,GAAA;;MAII2E,OAAO;AACTG,IAAAA,UAAU,IAAI,GAAd;;;SAEKA;;AAGT;;AACO,MAAME,WAAW,GAAG,CAACnE,MAAD,EAAkBG,IAAlB,KAAkC;QACrDoD,cAAc,GAAGD,qBAAqB,CAACtD,MAAD;;MACxCuD,cAAc,CAACvM,MAAf,GAAwB,GAAG,OAAOuM,cAAP;QACzBvE,EAAE,GAAGuE,cAAc,CAAC,CAAD;QACnBtE,EAAE,GAAGsE,cAAc,CAAC,CAAD;;QACnBa,QAAQ,GAAG9D,kBAAkB,CAACtB,EAAD,EAAKC,EAAL,EAASkB,IAAT;;QAC7BkE,cAAc,GAAGd,cAAc,CAAC9J,KAAf,CAAqB,CAArB;AACvB4K,EAAAA,cAAc,CAACC,OAAf,CAAuBF,QAAvB;SACOC;AARF,CAAA;;AClNO,MAAOE,MAAP,CAAa;AAMzBxJ,EAAAA,WAAA,CAAYyJ,IAAZ,EAA0BxE,MAA1B,EAA2CyE,SAA3C,EAA8DC,WAAW,GAAG,KAA5E,EAAiF;SAC1EF,OAAOA;SACPxE,SAASA;SACTyE,YAAYA;SACZC,cAAcA;;;AAGrBC,EAAAA,gBAAgB,GAAA;WACP,KAAK3E,MAAL,CAAY,CAAZ;;;AAGT4E,EAAAA,cAAc,GAAA;WACL,KAAK5E,MAAL,CAAY,KAAKA,MAAL,CAAYhJ,MAAZ,GAAqB,CAAjC;;;AAGT6N,EAAAA,SAAS,GAAA;WACA7N,MAAM,CAAC,KAAKgJ,MAAN;;;AAGf8E,EAAAA,UAAU,GAAA;QACJ7E,SAAS,GAAG,KAAKD,MAAL,CAAY,CAAZ;UACVE,eAAe,GAAG,KAAKF,MAAL,CAAYvG,KAAZ,CAAkB,CAAlB;WACjByG,eAAe,CAACrD,GAAhB,CAAqBwC,KAAD,IAAU;YAC7B0F,MAAM,GAAGhG,QAAQ,CAACM,KAAD,EAAQY,SAAR;AACvBA,MAAAA,SAAS,GAAGZ,KAAZ;aACO0F;AAHF,KAAA;;;AAOTC,EAAAA,WAAW,CAAC3F,KAAD,EAAa;UAChB4F,SAAS,GAAG,KAAKjF,MAAL,CAAYnD,GAAZ,CAAiBqI,WAAD,IAAiBzF,QAAQ,CAACyF,WAAD,EAAc7F,KAAd,CAAzC;WACXC,IAAI,CAAC8B,GAAL,CAAS,GAAG6D,SAAZ;;;AAGTE,EAAAA,kBAAkB,CAACnF,MAAD,EAAgB;UAC1BoF,SAAS,GAAGpF,MAAM,CAACtH,MAAP,CAAc,CAACC,GAAD,EAAM0G,KAAN,KAAgB1G,GAAG,GAAG,KAAKqM,WAAL,CAAiB3F,KAAjB,CAApC,EAA6D,CAA7D;WACX+F,SAAS,GAAGpF,MAAM,CAAChJ;;;AA1CH;;ACDb,MAAOqO,SAAP,CAAgB;AAI5BtK,EAAAA,WAAA,CAAYuK,MAAZ,EAA4BtJ,OAA5B,EAA6C;SACtCsJ,SAASA;SACTtJ,UAAUA;;;AANW;;ACE9B,SAASuJ,eAAT,CAAyB;AAAEC,EAAAA,UAAF;AAAcxJ,EAAAA,OAAd;AAAuByJ,EAAAA;AAAvB,CAAzB,EAAwE;QAChEf,WAAW,GAAID,SAAD;;;WAAuB,wBAACe,UAAD,SAAC,IAAAA,UAAD,WAAC,SAAA,GAAAA,UAAU,CAAE5K,OAAZ,CAAoB6J,SAApB,sEAAkC,CAAC,CAApC,KAA0C;AAArF;;SACOzI,OAAO,CAACa,GAAR,CAAY,CAAC2H,IAAD,EAAOtN,KAAP,KAAgB;UAC3B8I,MAAM,GAAGyF,OAAO,CAACvO,KAAD,CAAP,CAAe2F,GAAf,CAAoB6I,SAAD,IAAc;YACxC,CAACxG,CAAD,EAAIC,CAAJ,IAASuG;aACR;AAAExG,QAAAA,CAAF;AAAKC,QAAAA;AAAL;AAFM,KAAA;WAIR,IAAIoF,MAAJ,CAAWC,IAAX,EAAiBxE,MAAjB,EAAyB9I,KAAzB,EAAgCwN,WAAW,CAACxN,KAAD,CAA3C;AALF,GAAA;;;AASK,SAAUyO,aAAV,CAAwBL,MAAxB,EAAwCM,QAAxC,EAA+D;QACrE5J,OAAO,GAAGuJ,eAAe,CAACK,QAAD;SACxB,IAAIP,SAAJ,CAAcC,MAAd,EAAsBtJ,OAAtB;;;ACfT;AACA,MAAM6J,gBAAgB,GAAG,CACvB;AAAE3G,EAAAA,CAAC,EAAE,CAAL;AAAQC,EAAAA,CAAC,EAAE,CAAC;AAAZ,CADuB,EAEvB;AAAED,EAAAA,CAAC,EAAE,IAAL;AAAWC,EAAAA,CAAC,EAAE;AAAd,CAFuB,CAAzB;AAIA,MAAM,CAAC2G,IAAD,EAAOC,EAAP,IAAaF,gBAAnB;AACA,MAAMG,cAAc,GAAGD,EAAE,CAAC7G,CAAH,GAAO4G,IAAI,CAAC5G,CAAnC;AACA,MAAM+G,eAAe,GAAGF,EAAE,CAAC5G,CAAH,GAAO2G,IAAI,CAAC3G,CAApC;AAWc,MAAO+G,UAAP,CAAiB;AAQ7BnL,EAAAA,WAAA,CAAYE,OAAZ,EAAsC;UAC9B;AAAEkL,MAAAA,OAAF;AAAWC,MAAAA,KAAX;AAAkBC,MAAAA;AAAlB,QAA6BpL;SAC9BkL,UAAUA;SACVC,QAAQA;SACRC,SAASA;UAERC,cAAc,GAAGF,KAAK,GAAG,IAAID;UAC7BI,eAAe,GAAGF,MAAM,GAAG,IAAIF;UAC/BK,MAAM,GAAGF,cAAc,GAAGN;UAC1BS,MAAM,GAAGF,eAAe,GAAGN;SAE5BjD,QAAQ1D,IAAI,CAAC8B,GAAL,CAASoF,MAAT,EAAiBC,MAAjB;UAEPC,gBAAgB,GAAGP,OAAO,GAAG,CAACG,cAAc,GAAG,KAAKtD,KAAL,GAAagD,cAA/B,IAAiD;UAC9EW,gBAAgB,GACpBR,OAAO,GAAG,CAACI,eAAe,GAAG,KAAKvD,KAAL,GAAaiD,eAAhC,IAAmD;SAE1DW,UAAU,CAAC,CAAD,GAAKd,IAAI,CAAC5G,CAAV,GAAc,KAAK8D,KAAnB,GAA2B0D;SACrCG,UAAU,CAAC,CAAD,GAAKf,IAAI,CAAC3G,CAAV,GAAc,KAAK6D,KAAnB,GAA2B2D;;;AAG5CG,EAAAA,oBAAoB,CAACzH,KAAD,EAAa;UACzBH,CAAC,GAAG,CAACG,KAAK,CAACH,CAAN,GAAU,KAAK0H,OAAhB,IAA2B,KAAK5D;UACpC7D,CAAC,GAAG,CAAC,KAAKkH,MAAL,GAAc,KAAKQ,OAAnB,GAA6BxH,KAAK,CAACF,CAApC,IAAyC,KAAK6D;WACjD;AAAE9D,MAAAA,CAAF;AAAKC,MAAAA;AAAL;;;AAhCoB;;ACJ/B,MAAM4H,2BAA2B,GAAG,CAApC;;AACA,MAAMC,4BAA4B,GAAG,GAArC;;AACA,MAAMC,iBAAiB,GAAG,GAA1B;;AACA,MAAMC,iBAAiB,GAAG,IAA1B;;AAWc,SAAUC,aAAV,CACZC,UADY,EAEZpM,SAFY,EAGZyJ,SAHY,EAIZxJ,OAAA,GAII,EARQ,EAQN;QAEAe,OAAO,GAAGhB,SAAS,CAACgB;QACpBgE,MAAM,GAAGqH,eAAe,CAACD,UAAU,CAACpH,MAAZ;;MAE1BA,MAAM,CAAChJ,MAAP,GAAgB,GAAG;WACd;AAAEsQ,MAAAA,OAAO,EAAE,KAAX;AAAkBC,MAAAA,IAAI,EAAE;AAAEC,QAAAA,iBAAiB,EAAE;AAArB;AAAxB;;;QAGH;AAAEF,IAAAA,OAAF;AAAWC,IAAAA,IAAX;AAAiBE,IAAAA;AAAjB,MAA6BC,YAAY,CAAC1H,MAAD,EAAShE,OAAO,CAACyI,SAAD,CAAhB,EAA6BxJ,OAA7B;;MAE3C,CAACqM,SAAS;WACL;AAAEA,MAAAA,OAAF;AAAWC,MAAAA;AAAX;AAZH,GAAA;;;QAgBAI,YAAY,GAAG3L,OAAO,CAACvC,KAAR,CAAcgL,SAAS,GAAG,CAA1B;MACjBmD,gBAAgB,GAAGH;;OAElB,IAAIrP,CAAC,GAAG,GAAGA,CAAC,GAAGuP,YAAY,CAAC3Q,QAAQoB,CAAC,IAAI;UACtC;AAAEkP,MAAAA,OAAF;AAAWG,MAAAA;AAAX,QAAuBC,YAAY,CAAC1H,MAAD,EAAS2H,YAAY,CAACvP,CAAD,CAArB,EAA0B,EACjE,GAAG6C,OAD8D;AAEjE4M,MAAAA,cAAc,EAAE;AAFiD,KAA1B;;QAIrCP,OAAO,IAAIG,OAAO,GAAGG,kBAAkB;AACzCA,MAAAA,gBAAgB,GAAGH,OAAnB;;AAzBE,GAAA;;;;MA8BFG,gBAAgB,GAAGH,SAAS;;UAExBK,kBAAkB,GAAI,OAAOF,gBAAgB,GAAGH,OAA1B,KAAuC,IAAIA,OAA3C;UACtB;AAAEH,MAAAA,OAAF;AAAWC,MAAAA;AAAX,QAAoBG,YAAY,CAAC1H,MAAD,EAAShE,OAAO,CAACyI,SAAD,CAAhB,EAA6B,EACjE,GAAGxJ,OAD8D;AAEjE8M,MAAAA,QAAQ,EAAE,CAAC9M,OAAO,CAAC8M,QAAR,IAAoB,CAArB,IAA0BD;AAF6B,KAA7B;WAI/B;AAAER,MAAAA,OAAF;AAAWC,MAAAA;AAAX;;;SAGF;AAAED,IAAAA,OAAF;AAAWC,IAAAA;AAAX;;;AAGT,MAAMS,kBAAkB,GAAG,CAAChI,MAAD,EAAkBiI,aAAlB,EAAyCF,QAAzC,KAA6D;QAChFG,YAAY,GAAGzI,QAAQ,CAACwI,aAAa,CAACtD,gBAAd,EAAD,EAAmC3E,MAAM,CAAC,CAAD,CAAzC;QACvBmI,UAAU,GAAG1I,QAAQ,CAACwI,aAAa,CAACrD,cAAd,EAAD,EAAiC5E,MAAM,CAACA,MAAM,CAAChJ,MAAP,GAAgB,CAAjB,CAAvC;SAEzBkR,YAAY,IAAIlB,4BAA4B,GAAGe,QAA/C,IACAI,UAAU,IAAInB,4BAA4B,GAAGe;AALjD,CAAA;;;AAUA,MAAMK,cAAc,GAAIpI,MAAD,IAAoB;QACnCqI,OAAO,GAAY;MACrBpI,SAAS,GAAGD,MAAM,CAAC,CAAD;AACtBA,EAAAA,MAAM,CAACvG,KAAP,CAAa,CAAb,EAAgB0E,OAAhB,CAAyBkB,KAAD,IAAU;AAChCgJ,IAAAA,OAAO,CAAC3K,IAAR,CAAaqB,QAAQ,CAACM,KAAD,EAAQY,SAAR,CAArB;AACAA,IAAAA,SAAS,GAAGZ,KAAZ;AAFF,GAAA;SAIOgJ;AAPT,CAAA;;AAUA,MAAMC,gBAAgB,GAAG,CAACtI,MAAD,EAAkBuI,MAAlB,KAAoC;QACrDC,WAAW,GAAGJ,cAAc,CAACpI,MAAD;QAC5ByI,aAAa,GAAGF,MAAM,CAACzD,UAAP;QAChB4D,YAAY,GAAGF,WAAW,CAAC3L,GAAZ,CAAiB8L,UAAD,IAAe;UAC5CC,kBAAkB,GAAGH,aAAa,CAAC5L,GAAd,CAAmBgM,YAAD,IAC3CzI,gBAAgB,CAACyI,YAAD,EAAeF,UAAf,CADS;WAGpBrJ,IAAI,CAAC4B,GAAL,CAAS,GAAG0H,kBAAZ;AAJY,GAAA;QAMfE,aAAa,GAAGtQ,OAAO,CAACkQ,YAAD;SACtBI,aAAa,GAAG/B;AAVzB,CAAA;;AAaA,MAAMgC,aAAa,GAAG,CAAC/I,MAAD,EAAkBuI,MAAlB,EAAkCR,QAAlC,KAAsD;SAEvEA,QAAQ,IAAI/Q,MAAM,CAACgJ,MAAD,CAAN,GAAiB,EAArB,CAAR,IAAqCuI,MAAM,CAAC1D,SAAP,KAAqB,EAA1D,KAAiEqC;AAFtE,CAAA;;AAMA,MAAMG,eAAe,GAAIrH,MAAD,IAAoB;MACtCA,MAAM,CAAChJ,MAAP,GAAgB,GAAG,OAAOgJ,MAAP;QACjB,CAACgJ,UAAD,EAAa,GAAGC,IAAhB,IAAwBjJ;QACxBkJ,aAAa,GAAG,CAACF,UAAD;;OAEjB,MAAM3J,SAAS4J,MAAM;QACpB,CAACrJ,MAAM,CAACP,KAAD,EAAQ6J,aAAa,CAACA,aAAa,CAAClS,MAAd,GAAuB,CAAxB,CAArB,GAAkD;AAC3DkS,MAAAA,aAAa,CAACxL,IAAd,CAAmB2B,KAAnB;;;;SAIG6J;AAXT,CAAA;;AAcA,MAAMC,mBAAmB,GAAG,CAC1B7J,IAAI,CAAC8J,EAAL,GAAU,EADgB,EAE1B9J,IAAI,CAAC8J,EAAL,GAAU,EAFgB,EAG1B,CAH0B,EAIzB,CAAC,CAAD,GAAK9J,IAAI,CAAC8J,EAAV,GAAgB,EAJS,EAKzB,CAAC,CAAD,GAAK9J,IAAI,CAAC8J,EAAV,GAAgB,EALS,CAA5B;;AAQA,MAAMC,QAAQ,GAAG,CAAC3I,MAAD,EAAkBC,MAAlB,EAAmCoH,QAAnC,KAAuD;QAChEuB,UAAU,GAAG5G,cAAc,CAAChC,MAAD;QAC3B6I,UAAU,GAAG7G,cAAc,CAAC/B,MAAD;MAC7B6I,OAAO,GAAGC;AACdN,EAAAA,mBAAmB,CAAChL,OAApB,CAA6BgF,KAAD,IAAU;UAC9BhD,IAAI,GAAGM,WAAW,CAAC6I,UAAD,EAAapG,MAAM,CAACqG,UAAD,EAAapG,KAAb,CAAnB;;QACpBhD,IAAI,GAAGqJ,SAAS;AAClBA,MAAAA,OAAO,GAAGrJ,IAAV;;AAHJ,GAAA;SAMOqJ,OAAO,IAAIvC,iBAAiB,GAAGc;AAVxC,CAAA;;AAaA,MAAML,YAAY,GAAG,CACnB1H,MADmB,EAEnBuI,MAFmB,EAGnBtN,OAHmB,KASwB;QACrC;AACJ8M,IAAAA,QAAQ,GAAG,CADP;AAEJ2B,IAAAA,gBAAgB,GAAG,KAFf;AAGJ7B,IAAAA,cAAc,GAAG,IAHb;AAIJ8B,IAAAA,wBAAwB,GAAG;AAJvB,MAKF1O;QACEwM,OAAO,GAAGc,MAAM,CAACpD,kBAAP,CAA0BnF,MAA1B;QACV4J,OAAO,GAAGF,gBAAgB,IAAInB,MAAM,CAAC9D,SAAP,GAAmB,CAAvC,GAA2C,GAA3C,GAAiD;QAC3DoF,gBAAgB,GAAGpC,OAAO,IAAIkC,wBAAwB,GAAGC,OAA3B,GAAqC7B,SAT9B;;MAWvC,CAAC8B,kBAAkB;WACd;AAAEvC,MAAAA,OAAO,EAAE,KAAX;AAAkBG,MAAAA,OAAlB;AAA2BF,MAAAA,IAAI,EAAE;AAAEC,QAAAA,iBAAiB,EAAE;AAArB;AAAjC;;;QAEHsC,gBAAgB,GAAG9B,kBAAkB,CAAChI,MAAD,EAASuI,MAAT,EAAiBR,QAAjB;QACrCgC,cAAc,GAAGzB,gBAAgB,CAACtI,MAAD,EAASuI,MAAT;QACjCyB,UAAU,GAAGX,QAAQ,CAACrJ,MAAD,EAASuI,MAAM,CAACvI,MAAhB,EAAwB+H,QAAxB;QACrBkC,WAAW,GAAGlB,aAAa,CAAC/I,MAAD,EAASuI,MAAT,EAAiBR,QAAjB;QAE3BT,OAAO,GACXuC,gBAAgB,IAAIC,gBAApB,IAAwCC,cAAxC,IAA0DC,UAA1D,IAAwEC;;MAEtEpC,cAAc,IAAI,CAACP,SAAS;UACxB4C,kBAAkB,GAAGxC,YAAY,CAAC,CAAC,GAAG1H,MAAJ,EAAYmK,OAAZ,EAAD,EAAwB5B,MAAxB,EAAgC,EACrE,GAAGtN,OADkE;AAErE4M,MAAAA,cAAc,EAAE;AAFqD,KAAhC;;QAKnCqC,kBAAkB,CAAC5C,SAAS;aACvB;AACLA,QAAAA,OADK;AAELG,QAAAA,OAFK;AAGLF,QAAAA,IAAI,EAAE;AAAEC,UAAAA,iBAAiB,EAAE;AAArB;AAHD;;;;SAQJ;AAAEF,IAAAA,OAAF;AAAWG,IAAAA,OAAX;AAAoBF,IAAAA,IAAI,EAAE;AAAEC,MAAAA,iBAAiB,EAAE;AAArB;AAA1B;AA9CT,CAAA;;ACzJc,MAAO4C,UAAP,CAAiB;AAK7BrP,EAAAA,WAAA,CAAYsP,EAAZ,EAAwBC,aAAxB,EAA8CC,qBAA9C,EAA0E;SACnEF,KAAKA;SACLrK,SAAS,CAACsK,aAAD;SACTE,iBAAiB,CAACD,qBAAD;;;AAGxBE,EAAAA,WAAW,CAACpL,KAAD,EAAeqL,aAAf,EAAmC;SACvC1K,OAAOtC,KAAK2B;SACZmL,eAAe9M,KAAKgN;;;AAbE;;ACqB/B,MAAMC,KAAN,CAAW;AAST5P,EAAAA,WAAA,CAAY6P,QAAZ,EAA4B;SACrBC,YAAYD;SACZE,aAAa;SACbC,UAAU;SACVjT,iBAAiB8S;;;AAGxBlO,EAAAA,GAAG,GAAA;SACIoO,aAAa1U,cAAc;SAC3B4U,kBAAkB,IAAIhO,OAAJ,CAAaC,OAAD,IAAY;WACxCI,WAAWJ,QAD6B;;WAGxCgO,WAAWtU,UAAU,CAAC,MAAM,KAAKmI,MAAL,EAAP,EAAsB,KAAK+L,SAA3B;AAHL,KAAA;WAKhB,KAAKG;;;AAGd3M,EAAAA,KAAK,GAAA;QACC,KAAK0M,SAAS,OADf;;UAGGG,YAAY,GAAG7U,WAAW,CAACC,GAAZ,MAAqB,KAAKwU,UAAL,IAAmB,CAAxC;SAChBD,YAAYvL,IAAI,CAAC4B,GAAL,CAAS,CAAT,EAAY,KAAK2J,SAAL,GAAiBK,YAA7B;AACjBrU,IAAAA,YAAY,CAAC,KAAKoU,QAAN,CAAZ;SACKF,UAAU;;;AAGjBxM,EAAAA,MAAM,GAAA;QACA,CAAC,KAAKwM,SAAS;SACdD,aAAazU,WAAW,CAACC,GAAZ,GAFd;;SAIC2U,WAAWtU,UAAU,CAAC,MAAM,KAAKmI,MAAL,EAAP,EAAsB,KAAK+L,SAA3B;SACrBE,UAAU;;;AAGjBjM,EAAAA,MAAM,GAAA;AACJjI,IAAAA,YAAY,CAAC,KAAKoU,QAAN,CAAZ;;QACI,KAAK5N,UAAU;WACZA;;;SAEFA,WAAW8N;;;AAhDT;;AAyDG,MAAOC,QAAP,CAAe;;;;;AA0BxB;AACHrQ,EAAAA,WAAA,CACEjD,KADF,EAEEuT,gBAFF,EAGEpQ,OAAA,GAII,EAPN,EAOQ;SAoDAqQ,QAASC,MAAD,IAAmB;UAC7B,KAAKC,eAAL,KAAyB,MAAM;;;;YAI7BC,QAAQ,GAAGnM,IAAI,CAAC8B,GAAL,CACf,CADe,EAEf,CAACmK,MAAM,GAAG,KAAKT,UAAd,GAA4B,KAAKY,eAAlC,IAAqD,KAAKb,SAF3C;;UAKbY,QAAQ,KAAK,GAAG;aACbE,aAAcpP,YAAY,KAAKqP;;aAC/BC,eAAeV;aACfrM,OAAO,KAAK6M;AAHnB,aAIO;cACCG,aAAa,GAAGC,IAAI,CAACN,QAAD;cACpBjP,YAAY,GAAGwP,gBAAgB,CACnC,KAAKC,WAD8B,EAEnC,KAAKL,OAF8B,EAGnCE,aAHmC;;aAMhCH,aAAcpP,YAAYC;;aAC1BqP,eAAerV,qBAAqB,CAAC,KAAK8U,KAAN;;AAvBrC;;SAlDDxT,QAAQA;SACRoU,oBAAoBb;SACpBR,YAAY5P,OAAO,CAAC2P,QAAR,IAAoB;SAChCuB,SAASlR,OAAO,CAACmR;SACjBV,kBAAkB;SAClBF,kBAAkB;;;AAGzB9O,EAAAA,GAAG,CAAC2P,WAAD,EAA+B;QAC5B,CAAC,KAAKT,SAAS,KAAKU,cAAL,CAAoBD,WAApB;QACf,KAAKxB,SAAL,KAAmB,GAAGwB,WAAW,CAAC9P,WAAZ,CAAwB,KAAKqP,OAA7B;;QACtB,KAAKf,SAAL,KAAmB,CAAnB,IAAwB0B,cAAc,CAACF,WAAW,CAAChR,KAAb,EAAoB,KAAKuQ,OAAzB,GAAmC;aACpE5O,OAAO,CAACC,OAAR;;;SAEJ0O,eAAeU;SACfJ,cAAcI,WAAW,CAAChR;SAC1ByP,aAAazU,WAAW,CAACC,GAAZ;SACbuV,eAAerV,qBAAqB,CAAC,KAAK8U,KAAN;WAClC,IAAItO,OAAJ,CAAaC,OAAD,IAAY;WACxBI,WAAWJ;AADX,KAAA;;;AAKDqP,EAAAA,cAAc,CAACD,WAAD,EAA+B;QAC/CG,MAAM,GAAG,KAAKN;;QACd,OAAO,KAAKA,iBAAZ,KAAkC,YAAY;AAChDM,MAAAA,MAAM,GAAG,KAAKN,iBAAL,CAAuBG,WAAW,CAAChR,KAAnC,CAAT;;;SAEGuQ,UAAU/T,OAAO,CAAC,KAAKC,KAAN,EAAa0U,MAAb;;;AAGxBnO,EAAAA,KAAK,GAAA;QACC,KAAKmN,eAAL,KAAyB,MAAM;;;;QAG/B,KAAKK,cAAc;AACrBjV,MAAAA,oBAAoB,CAAC,KAAKiV,YAAN,CAApB;;;SAEGL,kBAAkBnV,WAAW,CAACC,GAAZ;;;AAGzBiI,EAAAA,MAAM,GAAA;QACA,KAAKiN,eAAL,KAAyB,MAAM;;;;SAG9BK,eAAerV,qBAAqB,CAAC,KAAK8U,KAAN;SACpCI,mBAAmBrV,WAAW,CAACC,GAAZ,KAAoB,KAAKkV;SAC5CA,kBAAkB;;;AA8BzB1M,EAAAA,MAAM,CAACuN,WAAD,EAA+B;;;2BAC9BhP;SACAA,WAAW8N;AAEhBvU,IAAAA,oBAAoB,CAAC,KAAKiV,YAAL,IAAqB,CAAC,CAAvB,CAApB;SACKA,eAAeV;;QAEhB,KAAKgB,QAAQ;UACX,CAAC,KAAKP,SAAS,KAAKU,cAAL,CAAoBD,WAApB;AACnBA,MAAAA,WAAW,CAAC9P,WAAZ,CAAwB,KAAKqP,OAA7B;;;;AA1HuB;AAIpBR,QAAA,CAAAT,KAAA,GAAQA,KAAR;;AA2HT,SAASqB,gBAAT,CACES,WADF,EAEEC,SAFF,EAGEjB,QAHF,EAGkB;QAEVkB,MAAM,GAAwB;;OAE/B,MAAMnV,OAAOkV,WAAW;UACrBE,QAAQ,GAAGF,SAAS,CAAClV,GAAD;UACpBqV,UAAU,GAAGJ,WAAH,SAAG,IAAAA,WAAH,WAAG,SAAA,GAAAA,WAAW,CAAGjV,GAAH;;QAC1B,OAAOqV,UAAP,KAAsB,QAAtB,IAAkC,OAAOD,QAAP,KAAoB,QAAtD,IAAkEA,QAAQ,IAAI,GAAG;AACnFD,MAAAA,MAAM,CAACnV,GAAD,CAAN,GAAciU,QAAQ,IAAImB,QAAQ,GAAGC,UAAf,CAAR,GAAqCA,UAAnD;AADF,WAEO;AACLF,MAAAA,MAAM,CAACnV,GAAD,CAAN,GAAcwU,gBAAgB,CAACa,UAAD,EAAaD,QAAb,EAAuBnB,QAAvB,CAA9B;;;;SAGGkB;;;AAGT,SAASJ,cAAT,CACEE,WADF,EAEEC,SAFF,EAE4C;OAErC,MAAMlV,OAAOkV,WAAW;UACrBE,QAAQ,GAAGF,SAAS,CAAClV,GAAD;UACpBqV,UAAU,GAAGJ,WAAH,SAAG,IAAAA,WAAH,WAAG,SAAA,GAAAA,WAAW,CAAGjV,GAAH;;QAC1BoV,QAAQ,IAAI,GAAG;UACbA,QAAQ,KAAKC,YAAY;eACpB;;AAFX,WAIO,IAAI,CAACN,cAAc,CAACM,UAAD,EAAaD,QAAb,CAAnB,EAA2C;aACzC;;;;SAGJ;;;;AAIT,MAAMb,IAAI,GAAI7M,CAAD,IAAe,CAACI,IAAI,CAAC8D,GAAL,CAASlE,CAAC,GAAGI,IAAI,CAAC8J,EAAlB,CAAD,GAAyB,CAAzB,GAA6B,GAAzD;;AC9OO,MAAM0D,WAAW,GAAG,CACzBC,QADyB,EAEzB/R,SAFyB,EAGzB4P,QAHyB,KAIJ;SACd,CACL,IAAIQ,QAAJ,cACe2B,kBADf,EAEE5S,SAAS,CACP;AAAE2B,IAAAA,OAAO,EAAE,CAAX;AAAcO,IAAAA,cAAc,EAAE;AAA9B,GADO,EAEPrB,SAAS,CAACgB,OAAV,CAAkBhF,MAFX,CAFX,EAME;AAAE4T,IAAAA,QAAF;AAAYwB,IAAAA,KAAK,EAAE;AAAnB,GANF,CADK;AALF,CAAA;AAiBA,MAAMrQ,aAAa,GAAG,CAC3BgR,QAD2B,EAE3B/R,SAF2B,EAG3B4P,QAH2B,KAIN;SACd,CACL,IAAIQ,QAAJ,cACe2B,UADf,EAEE;AACEjR,IAAAA,OAAO,EAAE,CADX;AAEEE,IAAAA,OAAO,EAAE7B,SAAS,CAAC;AAAE2B,MAAAA,OAAO,EAAE,CAAX;AAAcO,MAAAA,cAAc,EAAE;AAA9B,KAAD,EAAoCrB,SAAS,CAACgB,OAAV,CAAkBhF,MAAtD;AAFpB,GAFF,EAME;AAAE4T,IAAAA,QAAF;AAAYwB,IAAAA,KAAK,EAAE;AAAnB,GANF,CADK;AALF,CAAA;AAiBA,MAAMY,aAAa,GAAG,CAC3BD,QAD2B,EAE3B/R,SAF2B,EAG3B4P,QAH2B,KAIN;SACd,CACL,IAAIQ,QAAJ,cAA0B2B,kBAA1B,EAA8C,CAA9C,EAAiD;AAAEnC,IAAAA,QAAF;AAAYwB,IAAAA,KAAK,EAAE;AAAnB,GAAjD,CADK,EAEL,GAAGU,WAAW,CAACC,QAAD,EAAW/R,SAAX,EAAsB,CAAtB,CAFT;AALF,CAAA;AAWA,MAAMiS,WAAW,GAAG,CACzBC,SADyB,EAEzBC,QAFyB,EAGzBvC,QAHyB,KAIvB;SACK,CAAC,IAAIQ,QAAJ,YAAwB8B,WAAxB,EAAqCC,QAArC,EAA+C;AAAEvC,IAAAA;AAAF,GAA/C,CAAD;AALF,CAAA;AAQA,MAAMwC,eAAe,GAAG,CAC7B7E,MAD6B,EAE7B8E,KAF6B,EAG7BC,KAH6B,KAIR;QACf7I,SAAS,GAAG8D,MAAM,CAAC9D;QACnBmG,QAAQ,GAAG,CAACrC,MAAM,CAAC1D,SAAP,KAAqB,GAAtB,KAA8B,IAAIyI,KAAlC;SACV,CACL,IAAIlC,QAAJ,CAAa,wBAAb,EAAuCiC,KAAvC,CADK,EAEL,IAAIjC,QAAJ,CAAa,qBAAb,EAAoC;AAClCtP,IAAAA,OAAO,EAAE,CADyB;AAElCE,IAAAA,OAAO,EAAE;OACNyI,YAAY;AACXpI,QAAAA,cAAc,EAAE,CADL;AAEXP,QAAAA,OAAO,EAAE;AAFE;AADN;AAFyB,GAApC,CAFK,EAWL,IAAIsP,QAAJ,gCACiC3G,WADjC,EAEE;AACEpI,IAAAA,cAAc,EAAE,CADlB;AAEEP,IAAAA,OAAO,EAAE;AAFX,GAFF,EAME;AAAE8O,IAAAA;AAAF,GANF,CAXK,EAmBL,IAAIQ,QAAJ,gCAA4C3G,mBAA5C,EAAiE,CAAjE,EAAoE;AAClEmG,IAAAA,QADkE;AAElEwB,IAAAA,KAAK,EAAE;AAF2D,GAApE,CAnBK;AAPF,CAAA;AAiCA,MAAMmB,aAAa,GAAG,CAC3BR,QAD2B,EAE3BxE,MAF2B,EAG3B+E,KAH2B,KAIN;QACf7I,SAAS,GAAG8D,MAAM,CAAC9D;QACnBmG,QAAQ,GAAG,CAACrC,MAAM,CAAC1D,SAAP,KAAqB,GAAtB,KAA8B,IAAIyI,KAAlC;SACV,CACL,IAAIlC,QAAJ,cAA0B2B,UAA1B,EAAsC;AACpCjR,IAAAA,OAAO,EAAE,CAD2B;AAEpCE,IAAAA,OAAO,EAAE;OACNyI,YAAY;AACXpI,QAAAA,cAAc,EAAE,CADL;AAEXP,QAAAA,OAAO,EAAE;AAFE;AADN;AAF2B,GAAtC,CADK,EAUL,IAAIsP,QAAJ,cAA0B2B,oBAAoBtI,0BAA9C,EAA0E,CAA1E,EAA6E;AAC3EmG,IAAAA;AAD2E,GAA7E,CAVK;AAPF,CAAA;AAuBA,MAAM4C,mBAAmB,GAAG,CACjCT,QADiC,EAEjC/R,SAFiC,EAGjCyJ,SAHiC,EAIjC6I,KAJiC,KAKZ;QACfG,iBAAiB,GAAIpS,KAAD,IAA6B;UAC/CqS,YAAY,GAAGrS,KAAK,CAACL,SAAN,CAAgB+R,QAAhB;UACfY,aAAa,GAA2C;AAC5D7R,MAAAA,OAAO,EAAE,CADmD;AAE5DE,MAAAA,OAAO,EAAE;AAFmD;;SAIzD,IAAI5D,CAAC,GAAG,GAAGA,CAAC,GAAG4C,SAAS,CAACgB,OAAV,CAAkBhF,QAAQoB,CAAC,IAAI;AACjDuV,MAAAA,aAAa,CAAC3R,OAAd,CAAuB5D,CAAvB,IAA4B;AAC1B0D,QAAAA,OAAO,EAAE4R,YAAY,CAAC5R,OAAb,GAAuB4R,YAAY,CAAC1R,OAAb,CAAqB5D,CAArB,EAAwB0D;AAD9B,OAA5B;;;WAIK6R;AAXT;;QAaMpF,MAAM,GAAGvN,SAAS,CAACgB,OAAV,CAAkByI,SAAlB;SACR,CACL,IAAI2G,QAAJ,cAA0B2B,UAA1B,EAAsCU,iBAAtC,CADK,EAEL,GAAGF,aAAa,CAACR,QAAD,EAAWxE,MAAX,EAAmB+E,KAAnB,CAFX;AApBF,CAAA;AA0BA,MAAMM,UAAU,GAAG,CACxBb,QADwB,EAExBtI,SAFwB,EAGxBmG,QAHwB,KAIH;SACd,CACL,IAAIQ,QAAJ,cACe2B,oBAAoBtI,WADnC,EAEE;AACEpI,IAAAA,cAAc,EAAE,CADlB;AAEEP,IAAAA,OAAO,EAAE;AAFX,GAFF,EAME;AAAE8O,IAAAA,QAAF;AAAYwB,IAAAA,KAAK,EAAE;AAAnB,GANF,CADK;AALF,CAAA;AAiBA,MAAMyB,gBAAgB,GAAG,CAC9Bd,QAD8B,EAE9B/R,SAF8B,EAG9B8S,YAH8B,EAI9BR,KAJ8B,EAK9BS,mBAL8B,KAMT;MACjBpR,SAAS,GAAsBqQ,aAAa,CAACD,QAAD,EAAW/R,SAAX,EAAsB8S,YAAtB;AAChDnR,EAAAA,SAAS,GAAGA,SAAS,CAACqR,MAAV,CAAiBlB,WAAW,CAACC,QAAD,EAAW/R,SAAX,EAAsB,CAAtB,CAA5B,CAAZ;AACA2B,EAAAA,SAAS,CAACe,IAAV,CACE,IAAI0N,QAAJ,cACe2B,UADf,EAEE;AACEjR,IAAAA,OAAO,EAAE,CADX;AAEEE,IAAAA,OAAO,EAAE7B,SAAS,CAAC;AAAE2B,MAAAA,OAAO,EAAE;AAAX,KAAD,EAAiBd,SAAS,CAACgB,OAAV,CAAkBhF,MAAnC;AAFpB,GAFF,EAME;AAAEoV,IAAAA,KAAK,EAAE;AAAT,GANF,CADF;AAUApR,EAAAA,SAAS,CAACgB,OAAV,CAAkBmC,OAAlB,CAA0B,CAACoK,MAAD,EAASnQ,CAAT,KAAc;QAClCA,CAAC,GAAG,GAAGuE,SAAS,CAACe,IAAV,CAAe,IAAI0N,QAAQ,CAACT,KAAb,CAAmBoD,mBAAnB,CAAf;AACXpR,IAAAA,SAAS,GAAGA,SAAS,CAACqR,MAAV,CAAiBT,aAAa,CAACR,QAAD,EAAWxE,MAAX,EAAmB+E,KAAnB,CAA9B,CAAZ;AAFF,GAAA;SAIO3Q;AAvBF,CAAA;AA0BA,MAAMsR,oBAAoB,GAAG,CAClClB,QADkC,EAElC/R,SAFkC,EAGlC8S,YAHkC,EAIlCR,KAJkC,EAKlCS,mBALkC,EAMlCG,iBANkC,KAOb;QACfvR,SAAS,GAAGkR,gBAAgB,CAChCd,QADgC,EAEhC/R,SAFgC,EAGhC8S,YAHgC,EAIhCR,KAJgC,EAKhCS,mBALgC;AAOlCpR,EAAAA,SAAS,CAACe,IAAV,CAAe,IAAI0N,QAAQ,CAACT,KAAb,CAAmBuD,iBAAnB,CAAf;SACOvR;AAhBF,CAAA;;ACnLA,MAAMwR,SAAS,GAAG,CACvBnT,SADuB,EAEvB8S,YAFuB,EAGvBM,cAHuB,KAIF;SACd,CACL,GAAGC,aAAA,CAA+B,MAA/B,EAAuCrT,SAAvC,EAAkD8S,YAAlD,CADE,EAEL,IAAI1C,QAAJ,CACE,qBADF,EAEE;AACEtP,IAAAA,OAAO,EAAE,CADX;AAEEE,IAAAA,OAAO,EAAE7B,SAAS,CAAC;AAAE2B,MAAAA,OAAO,EAAE;AAAX,KAAD,EAAiBd,SAAS,CAACgB,OAAV,CAAkBhF,MAAnC;AAFpB,GAFF,EAME;AAAEoV,IAAAA,KAAK,EAAE;AAAT,GANF,CAFK,EAUL,IAAIhB,QAAJ,CACE,gBADF,EAEE;AACEtP,IAAAA,OAAO,EAAE,CADX;AAEEE,IAAAA,OAAO,EAAE1B,WAAW,CAACU,SAAS,CAACgB,OAAV,CAAkBhF,MAAnB,EAA4BoB,CAAD,KAAQ;AACrD0D,MAAAA,OAAO,EAAE1D,CAAC,GAAGgW,cAAJ,GAAqB,CAArB,GAAyB;AADmB,KAAR,CAA3B;AAFtB,GAFF,EAQE;AAAEhC,IAAAA,KAAK,EAAE;AAAT,GARF,CAVK;AALF,CAAA;AA4BA,MAAMkC,eAAe,GAAG,CAACjE,EAAD,EAAsBhL,KAAtB,KAAyD;SAC/E,CACL,IAAI+L,QAAJ,CAAa,yBAAb,EAAwCf,EAAxC,EAA4C;AAAE+B,IAAAA,KAAK,EAAE;AAAT,GAA5C,CADK,EAEL,IAAIhB,QAAJ,gBACiBf,IADjB,EAEE;AACErK,IAAAA,MAAM,EAAE,CAACX,KAAD,CADV;AAEEvD,IAAAA,OAAO,EAAE;AAFX,GAFF,EAME;AAAEsQ,IAAAA,KAAK,EAAE;AAAT,GANF,CAFK;AADF,CAAA;AAcA,MAAMmC,gBAAgB,GAAG,CAC9BC,YAD8B,EAE9BxO,MAF8B,KAGT;SACd,CAAC,IAAIoL,QAAJ,gBAA4BoD,qBAA5B,EAAmDxO,MAAnD,EAA2D;AAAEoM,IAAAA,KAAK,EAAE;AAAT,GAA3D,CAAD;AAJF,CAAA;AAOA,MAAMqC,cAAc,GAAG,CAC5BD,YAD4B,EAE5B5D,QAF4B,KAGP;SACd,CACL,IAAIQ,QAAJ,gBAA4BoD,sBAA5B,EAAoD,CAApD,EAAuD;AAAE5D,IAAAA;AAAF,GAAvD,CADK;AAJF,CAAA;AAcA,MAAM8D,oBAAoB,GAAIC,aAAD,IAAoD;SAC/E,CAAAA,aAAa,SAAb,IAAAA,aAAa,WAAb,SAAA,GAAAA,aAAa,CAAE9R,GAAf,CAAmB2R,YAAY,IACpC,IAAIpD,QAAJ,gBAA4BoD,cAA5B,EAA4C,IAA5C,EAAkD;AAAEpC,IAAAA,KAAK,EAAE;AAAT,GAAlD,CADK,CAAA,KAEF;AAHA,CAAA;AAMA,MAAMwC,qBAAqB,GAAG,CACnC5T,SADmC,EAEnCqS,KAFmC,EAGnCzC,QAHmC,KAId;SACd,CACL,IAAIQ,QAAJ,CAAa,wBAAb,EAAuCiC,KAAvC,CADK,EAEL,GAAGgB,aAAA,CAA+B,WAA/B,EAA4CrT,SAA5C,CAFE,EAGL,GAAGqT,aAAA,CAA+B,WAA/B,EAA4CrT,SAA5C,EAAuD4P,QAAQ,GAAG,CAAlE,CAHE,EAIL,GAAGyD,aAAA,CAA+B,WAA/B,EAA4CrT,SAA5C,EAAuD4P,QAAQ,GAAG,CAAlE,CAJE;AALF,CAAA;;AC/DP,MAAMiE,YAAY,GAAIzH,UAAD,KAA6B;AAChDnD,EAAAA,UAAU,EAAE6K,aAAA,CAAuB1H,UAAU,CAACoD,cAAlC,CADoC;AAEhDxK,EAAAA,MAAM,EAAEoH,UAAU,CAACpH,MAAX,CAAkBnD,GAAlB,CAAuBwC,KAAD,IAAWyP,KAAA,CAAezP,KAAf,CAAjC;AAFwC,CAA7B,CAArB;;AAKc,MAAO0P,IAAP,CAAW;AAcvBhU,EAAAA,WAAA,CAAYC,SAAZ,EAAkCqR,WAAlC,EAA4D2C,UAA5D,EAAkF;SANlFC,sBAAsB;SACtBC,oBAAoB;SACpBC,iBAAiB;SAKVC,aAAapU;SACb2Q,eAAeU;SACflP,YAAY;SACZkS,cAAcL;;;AAGrBb,EAAAA,SAAS,CAAClT,OAAD,EAAkC;QACrC,KAAKqU,iBAAiB;WACnB3D,aAAajP,IAChB6S,oBAAA,CAAkC,KAAKD,eAAvC;;;SAGCA,kBAAkB;SAElBnS,YAAY;SACZqS,WAAWvU;UACVwU,UAAU,GAAGxY,QAAQ,CACzBgE,OAAO,CAACyU,kBADiB,EAEzB,KAAKN,UAAL,CAAgBpT,OAAhB,CAAwBhF,MAFC;SAItBiY,sBAAsB3P,IAAI,CAAC8B,GAAL,CAASqO,UAAT,EAAqB,KAAKL,UAAL,CAAgBpT,OAAhB,CAAwBhF,MAAxB,GAAiC,CAAtD;SACtBkY,oBAAoB;SACpBC,iBAAiB;WAEf,KAAKxD,YAAL,CAAkBjP,GAAlB,CACL6S,SAAA,CACE,KAAKH,UADP,EAEEnU,OAAO,CAAC0U,kBAFV,EAGE,KAAKV,mBAHP,CADK;;;AASTX,EAAAA,eAAe,CAAC5D,aAAD,EAAqB;;;QAC9B,CAAC,KAAKvN,WAAW;aACZ;;;QAEL,KAAKyS,aAAa;aACb,KAAKC,aAAL;;;UAEHxQ,KAAK,GAAG,KAAKgQ,WAAL,CAAiBvI,oBAAjB,CAAsC4D,aAAtC;;UACRoF,QAAQ,GAAGvX,OAAO;SACnBqX,cAAc,IAAIxF,UAAJ,CAAe0F,QAAf,EAAyBzQ,KAAzB,EAAgCqL,aAAhC;kCACd4E,+FAAiB5R,KAAKoS;WACpB,KAAKnE,YAAL,CAAkBjP,GAAlB,CAAsB6S,eAAA,CAA4BO,QAA5B,EAAsCzQ,KAAtC,CAAtB;;;AAGT0Q,EAAAA,kBAAkB,CAACrF,aAAD,EAAqB;QACjC,CAAC,KAAKkF,aAAa;aACd5S,OAAO,CAACC,OAAR;;;UAEHoC,KAAK,GAAG,KAAKgQ,WAAL,CAAiBvI,oBAAjB,CAAsC4D,aAAtC;;SACTkF,YAAYnF,YAAYpL,OAAOqL;;UAC9BsF,UAAU,GAAG,KAAKJ,WAAL,CAAiB5P,MAAjB,CAAwBvG,KAAxB,CAA8B,CAA9B;;WACZ,KAAKkS,YAAL,CAAkBjP,GAAlB,CACL6S,gBAAA,CAA6B,KAAKK,WAAL,CAAiBvF,EAA9C,EAAkD2F,UAAlD,CADK;;;AAKTC,EAAAA,aAAa,CAACjB,UAAD,EAAuB;SAC7BK,cAAcL;;;AAGrBa,EAAAA,aAAa,GAAA;;;QACP,CAAC,KAAKD,aAAa;;SAElBjE,aAAajP,IAChB6S,cAAA,CACE,KAAKK,WAAL,CAAiBvF,EADnB,2BAEE,KAAKmF,QAAL,CAAelU,4FAAuB,GAFxC,GAJS;;;QAWP,KAAKsU,WAAL,CAAiB5P,MAAjB,CAAwBhJ,MAAxB,KAAmC,GAAG;WACnC4Y,cAAczE;;;;UAIf;AAAE+E,MAAAA,sBAAF;AAA0BC,MAAAA;AAA1B,QAA2D,KAAKX;;UAEhEY,aAAa,GAAG,KAAKC,iBAAL;;UAChB;AAAE/I,MAAAA,OAAF;AAAWC,MAAAA;AAAX,QAAoBJ,aAAa,CACrC,KAAKyI,WADgC,EAErC,KAAKR,UAFgC,EAGrC,KAAKH,mBAHgC,EAIrC;AACEvF,MAAAA,gBAAgB,EAAE,KAAKiC,YAAL,CAAkBtQ,KAAlB,CAAwBL,SAAxB,CAAkCiB,OAAlC,CAA0CH,OAA1C,GAAoD,CADxE;AAEEiM,MAAAA,QAAQ,EAAE,KAAKyH,QAAL,CAAezH,QAF3B;AAGE4B,MAAAA,wBAAwB,EAAE,KAAK6F,QAAL,CAAe7F;AAH3C,KAJqC,EAnB5B;;UA+BL2G,eAAe,GACnBH,4BAA4B,IAC5B,KAAKjB,iBAAL,GAAyB,CAAzB,IAA8BiB;UAE1BI,UAAU,GACdjJ,OAAO,IAAIgJ,eAAX,IAA+B/I,IAAI,CAACC,iBAAL,IAA0B0I;;QAEvDK,YAAY;WACTC,eAAejJ;AADtB,WAEO;WACAkJ,eAAelJ;;YAEd;AACJmJ,QAAAA,mBADI;AAEJ9U,QAAAA,cAFI;AAGJ+U,QAAAA;AAHI,UAIF,KAAKnB;;UAGPkB,mBAAmB,KAAK,KAAxB,IACA,KAAKxB,iBAAL,IAA0BwB,qBAC1B;aACK/E,aAAajP,IAChB2R,eAAA,CACE+B,aADF,EAEEvX,iBAAiB,CAAC+C,cAAD,CAFnB,EAGE+U,oBAHF;;;;SASDf,cAAczE;;;AAGrBrM,EAAAA,MAAM,GAAA;SACC3B,YAAY;;QACb,KAAKmS,iBAAiB;WACnB3D,aAAajP,IAChB6S,oBAAA,CAAkC,KAAKD,eAAvC;;;;AAKNsB,EAAAA,cAAc,CAAC;AACbC,IAAAA,SADa;AAEbtJ,IAAAA;AAFa,GAAD,EAMb;WACQ;AACLvM,MAAAA,SAAS,EAAE,KAAKoU,UAAL,CAAgB9J,MADtB;AAELb,MAAAA,SAAS,EAAE,KAAKwK,mBAFX;AAGL6B,MAAAA,gBAAgB,EAAE,KAAK5B,iBAHlB;AAIL6B,MAAAA,aAAa,EAAE,KAAK5B,cAJf;AAKL6B,MAAAA,gBAAgB,EACd,KAAK5B,UAAL,CAAgBpT,OAAhB,CAAwBhF,MAAxB,GAAiC,KAAKiY,mBAAtC,IAA6D4B,SAAS,GAAG,CAAH,GAAO,CAA7E,CANG;AAOLI,MAAAA,SAAS,EAAEpC,YAAY,CAAC,KAAKe,WAAN,CAPlB;AAQLsB,MAAAA,WAAW,EAAE3J,IAAI,CAACC;AARb;;;AAYT2J,EAAAA,UAAU,GAAA;QACJ,CAAC,KAAK3B,UAAU;UAEd;AAAExT,MAAAA,OAAF;AAAWsJ,MAAAA;AAAX,QAAsB,KAAK8J;UAE3B;AACJgC,MAAAA,UADI;AAEJC,MAAAA,mBAFI;AAGJ1B,MAAAA,kBAHI;AAIJ2B,MAAAA,sBAJI;AAKJ1V,MAAAA,cALI;AAMJ2V,MAAAA;AANI,QAOF,KAAK/B;QAELgC,SAAS,GAAsBnD,UAAA,CACjC,MADiC,EAEjC,KAAKY,mBAF4B,EAGjCU,kBAHiC;SAM9BT,oBAAoB;SACpBD,uBAAuB;UAEtBwC,UAAU,GAAG,KAAKxC,mBAAL,KAA6BjT,OAAO,CAAChF;;QAEpDya,YAAY;WACTtU,YAAY;AACjBiU,MAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,SAAA,GAAAA,UAAU,CAAG;AACXpW,QAAAA,SAAS,EAAEsK,MADA;AAEXyL,QAAAA,aAAa,EAAE,KAAK5B;AAFT,OAAH,CAAV;;UAIIkC,qBAAqB;AACvBG,QAAAA,SAAS,GAAGA,SAAS,CAACxD,MAAV,CACVuB,qBAAA,CACE,KAAKH,UADP,EAEEvW,iBAAiB,CAACyY,sBAAsB,IAAI1V,cAA3B,CAFnB,EAGE,CAAC2V,uBAAuB,IAAI,CAA5B,IAAiC,CAHnC,CADU,CAAZ;;;;SAUC5F,aAAajP,IAAI8U;;;AAGxBhB,EAAAA,cAAc,CAACjJ,IAAD,EAA4B;QACpC,CAAC,KAAKiI,UAAU;UAEd;AAAEkC,MAAAA;AAAF,QAAsB,KAAKlC;AAEjCkC,IAAAA,eAAe,SAAf,IAAAA,eAAe,WAAf,SAAA,GAAAA,eAAe,CAAG,EAChB,GAAG,KAAKd,cAAL,CAAoB;AAAEC,QAAAA,SAAS,EAAE,IAAb;AAAmBtJ,QAAAA;AAAnB,OAApB;AADa,KAAH,CAAf;SAIK4J;;;AAGPV,EAAAA,cAAc,CAAClJ,IAAD,EAA4B;;;SACnC2H,qBAAqB;SACrBC,kBAAkB;oDAClBK,UAAUmC,8GAAY,KAAKf,cAAL,CAAoB;AAAEC,MAAAA,SAAS,EAAE,KAAb;AAAoBtJ,MAAAA;AAApB,KAApB;;;AAG7B8I,EAAAA,iBAAiB,GAAA;WACR,KAAKjB,UAAL,CAAgBpT,OAAhB,CAAwB,KAAKiT,mBAA7B;;;AA5Oc;;ACjBnB,SAAU2C,SAAV,CAAoBC,OAApB,EAAmC;SAChCC,QAAQ,CAACC,eAAT,CAAyB,4BAAzB,EAAuDF,OAAvD;;AAGH,SAAUG,IAAV,CAAeC,GAAf,EAA6BC,IAA7B,EAA2CC,KAA3C,EAAwD;AAC5DF,EAAAA,GAAG,CAACG,cAAJ,CAAmB,IAAnB,EAAyBF,IAAzB,EAA+BC,KAA/B;;AAGI,SAAUE,KAAV,CAAgBJ,GAAhB,EAA8BK,QAA9B,EAA8D;AAClEC,EAAAA,MAAM,CAACC,IAAP,CAAYF,QAAZ,EAAsBnU,OAAtB,CAA+BsU,QAAD,IAAcT,IAAI,CAACC,GAAD,EAAMQ,QAAN,EAAgBH,QAAQ,CAACG,QAAD,CAAxB,CAAhD;;;AAII,SAAUC,QAAV,CAAmBrI,EAAnB,EAA6B;MAC7BsI,MAAM,GAAG;;MACTzc,MAAM,CAAC0c,QAAP,IAAmB1c,MAAM,CAAC0c,QAAP,CAAgBC,MAAM;AAC3CF,IAAAA,MAAM,GAAGzc,MAAM,CAAC0c,QAAP,CAAgBC,IAAhB,CAAqB3Y,OAArB,CAA6B,SAA7B,EAAwC,EAAxC,EAA4CA,OAA5C,CAAoD,KAApD,EAA2D,KAA3D,CAAT;;;iBAEayY,UAAUtI;;AAGrB,SAAUyI,SAAV,CAAoBb,GAApB,EAA4C;;;AAChDA,EAAAA,GAAG,SAAH,IAAAA,GAAG,WAAH,SAAA,sBAAAA,GAAG,CAAEc,8EAAYC,YAAYf,IAA7B;;;ACnBY,MAAOgB,kBAAP,CAAyB;AAKrClY,EAAAA,WAAA,CAAYwN,MAAZ,EAA0B;SACnBA,SAASA;SACT2K,cAAc3K,MAAM,CAAC1D,SAAP,KAAqBoO,kBAAkB,CAACE,YAAnB,GAAkC;;;AAG5EC,EAAAA,oBAAoB,CAAC/W,cAAD,EAAuB;WAClC,KAAK6W,WAAL,GAAmB,KAAnB,IAA4B,IAAI7W,cAAhC;;;AAGTgX,EAAAA,SAAS,CAAC;AACR5X,IAAAA,WADQ;AAERE,IAAAA;AAFQ,GAAD,EAMR;WACQA,YAAY,IAAI,KAAK4M,MAAL,CAAY7D,WAA5B,GAA0C/I,YAA1C,GAAyDF;;;AArB7B;AAG9BwX,kBAAA,CAAAE,YAAA,GAAe,GAAf;;ACET,MAAMA,YAAY,GAAG,GAArB;AASA;;AACc,MAAOG,cAAP,SAA8BL,kBAA9B,CAAgD;AAO5DlY,EAAAA,WAAA,CAAYwN,MAAZ,EAA0B;UAClBA;SAPRgL,YAA2CpI;;;AAU3CqI,EAAAA,KAAK,CAAC7G,MAAD,EAAwB;SACtB8G,iBAAiBC,SAAA,CAAc,MAAd;SACjBC,QAAQD,SAAA,CAAc,UAAd;SACRE,cAAcF,SAAA,CAAc,MAAd;UACbG,MAAM,WAAWtb,OAAO;AAC9Bmb,IAAAA,IAAA,CAAS,KAAKC,KAAd,EAAqB,IAArB,EAA2BE,MAA3B;AAEAH,IAAAA,IAAA,CAAS,KAAKE,WAAd,EAA2B,GAA3B,EAAgC,KAAKrL,MAAL,CAAY/D,IAA5C;SACKiP,eAAeK,MAAMhY,UAAU;AACpC4X,IAAAA,IAAA,CAAS,KAAKD,cAAd,EAA8B,WAA9B,EAA2CC,QAAA,CAAaG,MAAb,CAA3C;UAEME,kBAAkB,GAAG5P,WAAW,CAAC,KAAKoE,MAAL,CAAYvI,MAAb,EAAqBmT,YAAY,GAAG,CAApC;AACtCO,IAAAA,IAAA,CAAS,KAAKD,cAAd,EAA8B,GAA9B,EAAmC5P,aAAa,CAACkQ,kBAAD,CAAhD;AACAL,IAAAA,KAAA,CAAU,KAAKD,cAAf,EAA+B;AAC7BlL,MAAAA,MAAM,EAAE,SADqB;sBAEb4K,YAAY,CAACa,QAAb,EAFa;AAG7BC,MAAAA,IAAI,EAAE,MAHuB;wBAIX,OAJW;yBAKV,OALU;6BAMN,KAAKf,eAAe,KAAKA;AANnB,KAA/B;;SASKS,MAAMO,YAAY,KAAKN;;AAC5BjH,IAAAA,MAAM,CAACwH,IAAP,CAAYD,WAAZ,CAAwB,KAAKP,KAA7B;AACAhH,IAAAA,MAAM,CAAC+G,GAAP,CAAWQ,WAAX,CAAuB,KAAKT,cAA5B;WACO;;;AAGTW,EAAAA,MAAM,CAACC,KAAD,EAAyB;;;QACzBA,KAAK,KAAK,KAAKd,SAAf,IAA4B,CAAC,KAAKE,gBAAgB;;;;QAIlDY,KAAK,CAAChY,cAAN,yBAAyB,KAAKkX,6DAALe,gBAAgBjY,cAAzC,GAAyD;WACtDoX,eAAeK,MAAMS,mBAAmB,KAAKnB,oBAAL,CAC3CiB,KAAK,CAAChY,cADqC,EAE3C2X,QAF2C;;;UAKzC3G,KAAK,GAAG,KAAKgG,SAAL,CAAegB,KAAf;;QAEV,CAAC,KAAKd,SAAN,IAAmBlG,KAAK,KAAK,KAAKgG,SAAL,CAAe,KAAKE,SAApB,GAAgC;YACzD;AAAEha,QAAAA,CAAF;AAAKG,QAAAA,CAAL;AAAQC,QAAAA,CAAR;AAAWC,QAAAA;AAAX,UAAiByT;AACvBqG,MAAAA,KAAA,CAAU,KAAKD,cAAf,EAA+B;AAAElL,QAAAA,MAAM,UAAUhP,KAAKG,KAAKC,KAAKC;AAAjC,OAA/B;;;QAGEya,KAAK,CAACvY,OAAN,0BAAkB,KAAKyX,8DAALiB,iBAAgB1Y,OAAlC,GAA2C;WACxC2X,eAAeK,MAAMhY,UAAUuY,KAAK,CAACvY,OAAN,CAAckY,QAAd;;;SAEjCT,YAAYc;;;AA5DyC;;ACJhD,MAAOI,iBAAP,CAAwB;AAOpC1Z,EAAAA,WAAA,CAAYC,SAAZ,EAAgC;SANhCuY,YAAiDpI;SAO1CuJ,mBAAmB1Z,SAAS,CAACgB,OAAV,CAAkBa,GAAlB,CAAuB0L,MAAD,IAAY,IAAI+K,cAAJ,CAAmB/K,MAAnB,CAAlC;;;AAG1BiL,EAAAA,KAAK,CAAC7G,MAAD,EAAwB;UACrBgI,SAAS,GAAGhI,MAAM,CAACiI,qBAAP;SACbC,SAASF,SAAS,CAACjB;;SACnBgB,iBAAiBvW,QAAS2W,cAAD,IAAmB;AAC/CA,MAAAA,cAAc,CAACtB,KAAf,CAAqBmB,SAArB;AADF;;;AAKFP,EAAAA,MAAM,CAACC,KAAD,EAA+B;;;QAC/BA,KAAK,KAAK,KAAKd,SAAf,IAA4B,CAAC,KAAKsB,QAAQ;;;;UAGxC;AAAE/Y,MAAAA,OAAF;AAAWE,MAAAA,OAAX;AAAoBP,MAAAA,WAApB;AAAiCE,MAAAA,YAAY,GAAG;AAAhD,QAAyD0Y;;QAC3DvY,OAAO,yBAAK,KAAKyX,6DAALe,gBAAgBxY,OAArB,GAA8B;WAClC+Y,OAAOf,MAAMhY,UAAUA,OAAO,CAACkY,QAAR,GADW;;;;UAKnC,CAACrZ,aAAa;;;YACZmB,OAAO,KAAK,GAAG;eACZ+Y,OAAOf,MAAMiB,UAAU;AAD9B,eAEO,IAAI,0BAAKxB,+EAAWzX,OAAhB,MAA4B,CAAhC,EAAmC;eACnC+Y,OAAOf,MAAMkB,eAAe;;;;;UAIjCC,aAAa,GACjB,CAAC,KAAK1B,SAAN,IACA9X,WAAW,KAAK,KAAK8X,SAAL,CAAe9X,WAD/B,IAEAE,YAAY,KAAK,KAAK4X,SAAL,CAAe5X;;QAE9BsZ,aAAa,IAAIjZ,OAAO,0BAAK,KAAKuX,8DAAL2B,iBAAgBlZ,OAArB,GAA8B;WACnD,IAAI5D,CAAC,GAAG,GAAGA,CAAC,GAAG,KAAKsc,gBAAL,CAAsB1d,QAAQoB,CAAC,IAAI;;;YAEnD,CAAC6c,aAAD,wBACA,KAAK1B,mBADL,+BAAA,IACA4B,iBAAgBnZ,OADhB,IAEAA,OAAO,CAAC5D,CAAD,CAAP,KAAe,KAAKmb,SAAL,CAAevX,OAAf,CAAuB5D,CAAvB,GACf;;;;aAGGsc,iBAAiBtc,GAAGgc,OAAO;AAC9B3Y,UAAAA,WAD8B;AAE9BE,UAAAA,YAF8B;AAG9BG,UAAAA,OAAO,EAAEE,OAAO,CAAC5D,CAAD,CAAP,CAAW0D,OAHU;AAI9BO,UAAAA,cAAc,EAAEL,OAAO,CAAC5D,CAAD,CAAP,CAAWiE;AAJG;;;;SAQ/BkX,YAAYc;;;AA3DiB;;ACFxB,MAAOe,kBAAP,CAAyB;AAAvCra,EAAAA,WAAA,GAAA;SACEwY,YAAyCpI;;;AAGzCqI,EAAAA,KAAK,CAAC7G,MAAD,EAAwB;SACtB0I,QAAQ3B,SAAA,CAAc,MAAd;AACb/G,IAAAA,MAAM,CAAC+G,GAAP,CAAWQ,WAAX,CAAuB,KAAKmB,KAA5B;;;AAGFjB,EAAAA,MAAM,CAACC,KAAD,EAAuB;;;QACvB,CAAC,KAAKgB,KAAN,IAAehB,KAAK,KAAK,KAAKd,WAAW;;;;QAI3Cc,KAAK,CAAC5Y,WAAN,yBAAsB,KAAK8X,6DAALe,gBAAgB7Y,WAAtC,KACA4Y,KAAK,CAACiB,WAAN,0BAAsB,KAAK/B,8DAALiB,iBAAgBc,WAAtC,GACA;YACM;AAAE/b,QAAAA,CAAF;AAAKG,QAAAA,CAAL;AAAQC,QAAAA,CAAR;AAAWC,QAAAA;AAAX,UAAiBya,KAAK,CAAC5Y;AAC7BiY,MAAAA,KAAA,CAAU,KAAK2B,KAAf,EAAsB;AACpBpB,QAAAA,IAAI,EAAE,MADc;AAEpB1L,QAAAA,MAAM,UAAUhP,KAAKG,KAAKC,KAAKC,IAFX;wBAGJya,KAAK,CAACiB,WAAN,CAAkBtB,QAAlB,EAHI;0BAIF,OAJE;2BAKD;AALC,OAAtB;;;QAQEK,KAAK,CAACvY,OAAN,0BAAkB,KAAKyX,8DAAL2B,iBAAgBpZ,OAAlC,GAA2C;AAC7C4X,MAAAA,IAAA,CAAS,KAAK2B,KAAd,EAAqB,SAArB,EAAgChB,KAAK,CAACvY,OAAN,CAAckY,QAAd,EAAhC;;;QAEEK,KAAK,CAACrU,MAAN,0BAAiB,KAAKuT,8DAAL4B,iBAAgBnV,MAAjC,GAAyC;AAC3C0T,MAAAA,IAAA,CAAS,KAAK2B,KAAd,EAAqB,GAArB,EAA0BxR,aAAa,CAACwQ,KAAK,CAACrU,MAAP,CAAvC;;;SAEGuT,YAAYc;;;AAGnBkB,EAAAA,OAAO,GAAA;AACL7B,IAAAA,SAAA,CAAc,KAAK2B,KAAnB;;;AApCmC;;ACHzB,MAAOG,mBAAP,CAA0B;AAUtCza,EAAAA,WAAA,CAAYC,SAAZ,EAAkCgU,UAAlC,EAAwD;SACjDI,aAAapU;SACbqU,cAAcL;SACdyG,oBAAoB,IAAIhB,iBAAJ,CAAsBzZ,SAAtB;SACpB0a,uBAAuB,IAAIjB,iBAAJ,CAAsBzZ,SAAtB;SACvB2a,yBAAyB,IAAIlB,iBAAJ,CAAsBzZ,SAAtB;SACzB4a,uBAAuB;;;AAG9BpC,EAAAA,KAAK,CAAC7G,MAAD,EAAwB;UACrBkJ,gBAAgB,GAAGlJ,MAAM,CAACiI,qBAAP;UACnBkB,KAAK,GAAGD,gBAAgB,CAACnC;UACzB;AAAE9M,MAAAA,OAAF;AAAWC,MAAAA,OAAX;AAAoBR,MAAAA,MAApB;AAA4BrD,MAAAA;AAA5B,QAAsC,KAAKqM;AAEjDqE,IAAAA,IAAA,CACEoC,KADF,EAEE,WAFF,eAGelP,YAAYP,MAAM,GAAGQ,kBAAkB7D,UAAU,CAAC,CAAD,GAAKA,QAHrE;;SAKK0S,qBAAqBlC,MAAMqC;;SAC3BJ,kBAAkBjC,MAAMqC;;SACxBF,uBAAuBnC,MAAMqC;;SAC7BE,oBAAoBF;;;AAG3BzB,EAAAA,MAAM,CAACC,KAAD,EAAyB;UACvB;AAAExY,MAAAA,IAAF;AAAQI,MAAAA,OAAR;AAAiBE,MAAAA;AAAjB,QAA+BkY,KAAK,CAACrZ;UACrC;AACJU,MAAAA,YADI;AAEJC,MAAAA,YAFI;AAGJC,MAAAA,cAHI;AAIJH,MAAAA,WAJI;AAKJF,MAAAA,YALI;AAMJC,MAAAA;AANI,QAOF6Y,KAAK,CAACpZ;;SAELya,qBAAqBtB,OAAO;AAC/BtY,MAAAA,OAAO,EAAEG,OAAO,CAACH,OADc;AAE/BE,MAAAA,OAAO,EAAEC,OAAO,CAACD,OAFc;AAG/BP,MAAAA,WAAW,EAAEC;AAHkB;;SAM5B+Z,kBAAkBrB,OAAO;AAC5BtY,MAAAA,OAAO,EAAED,IAAI,CAACC,OADc;AAE5BE,MAAAA,OAAO,EAAEH,IAAI,CAACG,OAFc;AAG5BP,MAAAA,WAH4B;AAI5BE,MAAAA,YAAY,EAAEA;AAJc;;SAOzBga,uBAAuBvB,OAAO;AACjCtY,MAAAA,OAAO,EAAEK,SAAS,CAACL,OADc;AAEjCE,MAAAA,OAAO,EAAEG,SAAS,CAACH,OAFc;AAGjCP,MAAAA,WAAW,EAAEG;AAHoB;;UAM7BQ,WAAW,GAAGiY,KAAK,CAACjY,WAAN,IAAqB;;SAEpC,MAAMoS,gBAAgB,KAAKoH,sBAAsB;UAChD,CAACxZ,WAAW,CAACoS,YAAD,GAAgB;;;sCACzBoH,qBAAqBpH,6FAAe+G;eAClC,KAAKK,oBAAL,CAA0BpH,YAA1B;;;;SAIN,MAAMA,gBAAgBpS,aAAa;YAChCmM,MAAM,GAAGnM,WAAW,CAACoS,YAAD;;UACtB,CAACjG,QAAQ;;;;YAGPyN,eAAe,GAAoB;AACvCV,QAAAA,WAAW,EAAE/Z,YAD0B;AAEvCE,QAAAA,WAAW,EAAED,YAF0B;WAGpC+M;AAHoC;;YAMnCuM,cAAc,GAAG,CAAC,MAAK;YACvB,KAAKc,oBAAL,CAA0BpH,YAA1B,GAAyC;iBACpC,KAAKoH,oBAAL,CAA0BpH,YAA1B;;;cAEHyH,iBAAiB,GAAG,IAAIb,kBAAJ;AAC1Ba,QAAAA,iBAAiB,CAACzC,KAAlB,CAAwB,KAAKuC,iBAA7B;aACKH,qBAAqBpH,gBAAgByH;eACnCA;AAPc,OAAA;;AAUvBnB,MAAAA,cAAc,CAACV,MAAf,CAAsB4B,eAAtB;;;;AAIJT,EAAAA,OAAO,GAAA;AACL7B,IAAAA,SAAA,CAAc,KAAKqC,iBAAL,CAAwBrC,GAAtC;SACKqC,kBAAmB5B,KAAK+B,YAAY;;;AArGL;;ACFxC;AACc,MAAOC,gBAAP,CAAuB;AASnCpb,EAAAA,WAAA,CAAYqb,IAAZ,EAA0B;SACnBA,OAAOA;;;AAGdC,EAAAA,uBAAuB,CAAC3f,QAAD,EAAoC;SACpD0f,KAAKE,iBAAiB,aAAcC,GAAD,IAAQ;AAC9C7f,MAAAA,QAAQ,CAAC,KAAK8f,SAAL,CAAeD,GAAf,EAAkC,KAAKE,cAAvC,CAAD,CAAR;AADF;SAGKL,KAAKE,iBAAiB,cAAeC,GAAD,IAAQ;AAC/C7f,MAAAA,QAAQ,CAAC,KAAK8f,SAAL,CAAeD,GAAf,EAAkC,KAAKG,cAAvC,CAAD,CAAR;AADF;;;AAKFC,EAAAA,sBAAsB,CAACjgB,QAAD,EAAoC;SACnD0f,KAAKE,iBAAiB,aAAcC,GAAD,IAAQ;AAC9C7f,MAAAA,QAAQ,CAAC,KAAK8f,SAAL,CAAeD,GAAf,EAAkC,KAAKE,cAAvC,CAAD,CAAR;AADF;SAGKL,KAAKE,iBAAiB,aAAcC,GAAD,IAAQ;AAC9C7f,MAAAA,QAAQ,CAAC,KAAK8f,SAAL,CAAeD,GAAf,EAAkC,KAAKG,cAAvC,CAAD,CAAR;AADF;;;AAKFE,EAAAA,qBAAqB,CAAClgB,QAAD,EAAqB;;AAExCob,IAAAA,QAAQ,CAACwE,gBAAT,CAA0B,SAA1B,EAAqC5f,QAArC;AACAob,IAAAA,QAAQ,CAACwE,gBAAT,CAA0B,UAA1B,EAAsC5f,QAAtC;;;AAGFmgB,EAAAA,qBAAqB,GAAA;WACZ,KAAKT,IAAL,CAAUS,qBAAV;;;AAGTC,EAAAA,gBAAgB,CAAC1Q,KAAD,EAAyBC,MAAzB,EAAgD;SACzD+P,KAAKW,aAAa,YAAY3Q;SAC9BgQ,KAAKW,aAAa,aAAa1Q;;;AAGtCmQ,EAAAA,SAAS,CAAuBD,GAAvB,EAAoCS,SAApC,EAAuE;WACvE;AACLC,MAAAA,QAAQ,EAAE,MAAMD,SAAS,CAACE,IAAV,CAAe,IAAf,EAAqBX,GAArB,CADX;AAELY,MAAAA,cAAc,EAAE,MAAMZ,GAAG,CAACY,cAAJ;AAFjB;;;AAMTV,EAAAA,cAAc,CAACF,GAAD,EAAgB;UACtB;AAAEa,MAAAA,IAAF;AAAQC,MAAAA;AAAR,QAAgB,KAAKR,qBAAL;UAChB3X,CAAC,GAAGqX,GAAG,CAACe,OAAJ,GAAcF;UAClBjY,CAAC,GAAGoX,GAAG,CAACgB,OAAJ,GAAcF;WACjB;AAAEnY,MAAAA,CAAF;AAAKC,MAAAA;AAAL;;;AAGTuX,EAAAA,cAAc,CAACH,GAAD,EAAgB;UACtB;AAAEa,MAAAA,IAAF;AAAQC,MAAAA;AAAR,QAAgB,KAAKR,qBAAL;UAChB3X,CAAC,GAAGqX,GAAG,CAACiB,OAAJ,CAAY,CAAZ,EAAeF,OAAf,GAAyBF;UAC7BjY,CAAC,GAAGoX,GAAG,CAACiB,OAAJ,CAAY,CAAZ,EAAeD,OAAf,GAAyBF;WAC5B;AAAEnY,MAAAA,CAAF;AAAKC,MAAAA;AAAL;;;AAhE0B;;ACLvB,MAAOsY,YAAP,SAA4BtB,gBAA5B,CAAwE;AAmCpFpb,EAAAA,WAAA,CAAY2Y,GAAZ,EAA6CS,IAA7C,EAA6D;UACrDT;SAEDA,MAAMA;SACNS,OAAOA;;QAER,oBAAoBT,KAAK;WACtBgE,MAAMhE,GAAG,CAACiE,cAAJ;;;;SAzCRC,KAAKC,SAA2BzR,KAAK,GAAG,QAAQC,MAAM,GAAG,QAAM;UAC9DyR,OAAO,GAAG,CAAC,MAAK;UAChB,OAAOD,OAAP,KAAmB,UAAU;eACxB/F,QAAQ,CAACiG,cAAT,CAAwBF,OAAxB;;;aAEFA;AAJO,KAAA;;QAOZ,CAACC,SAAS;YACN,IAAI9d,KAAJ,0CAAmD6d,SAAnD;;;UAEFG,QAAQ,GAAGF,OAAO,CAACG,QAAR,CAAiBjf,WAAjB;;UAEX0a,GAAG,GAAG,CAAC,MAAK;UACZsE,QAAQ,KAAK,KAAb,IAAsBA,QAAQ,KAAK,KAAK;eACnCF;AADT,aAEO;cACCpE,GAAG,GAAG9B,SAAS,CAAC,KAAD;AACrBkG,QAAAA,OAAO,CAAC5D,WAAR,CAAoBR,GAApB;eACOA;;AANC,KAAA;;AAUZrB,IAAAA,KAAK,CAACqB,GAAD,EAAM;AAAEtN,MAAAA,KAAF;AAASC,MAAAA;AAAT,KAAN,CAAL;UACM8N,IAAI,GAAGvC,SAAS,CAAC,MAAD;AACtB8B,IAAAA,GAAG,CAACQ,WAAJ,CAAgBC,IAAhB;WAEO,IAAIsD,YAAJ,CAAiB/D,GAAjB,EAAsBS,IAAtB;;;AAkBTS,EAAAA,qBAAqB,GAAA;UACbkB,KAAK,GAAGlE,SAAS,CAAC,GAAD;SAClB8B,IAAIQ,YAAY4B;WACd,IAAI2B,YAAJ,CAAiB3B,KAAjB,EAAwB,KAAK3B,IAA7B;;;AAGTsC,EAAAA,cAAc,CAACF,GAAD,EAAgB;QACxB,KAAKmB,KAAK;WACPA,IAAIxY,IAAIqX,GAAG,CAACe;WACZI,IAAIvY,IAAIoX,GAAG,CAACgB;;UACb,kBAAkB,KAAKnB,MAAM;;;cACzB8B,OAAO,GAAG,KAAKR,GAAL,CAASS,eAAT,0BAAyB,KAAK/B,IAAL,CAAUgC,YAAV,4DAAAC,sBAA0BC,OAA1B,EAAzB;;eACT;AAAEpZ,UAAAA,CAAC,EAAEgZ,OAAO,CAAChZ,CAAb;AAAgBC,UAAAA,CAAC,EAAE+Y,OAAO,CAAC/Y;AAA3B;;;;WAGJ,MAAMsX,cAAN,CAAqBS,IAArB,CAA0B,IAA1B,EAAgCX,GAAhC;;;AAGTG,EAAAA,cAAc,CAACH,GAAD,EAAgB;QACxB,KAAKmB,KAAK;WACPA,IAAIxY,IAAIqX,GAAG,CAACiB,OAAJ,CAAY,CAAZ,EAAeF;WACvBI,IAAIvY,IAAIoX,GAAG,CAACiB,OAAJ,CAAY,CAAZ,EAAeD;;UACxB,kBAAkB,KAAKnB,MAAM;;;cACzB8B,OAAO,GAAG,KAAKR,GAAL,CAASS,eAAT,2BACb,KAAK/B,IAAL,CAA4BgC,YAA5B,6DAAAG,uBAA4CD,OAA5C,EADa;;eAGT;AAAEpZ,UAAAA,CAAC,EAAEgZ,OAAO,CAAChZ,CAAb;AAAgBC,UAAAA,CAAC,EAAE+Y,OAAO,CAAC/Y;AAA3B;;;;WAGJ,MAAMuX,cAAN,CAAqBH,GAArB;;;AA3E2E;;ACCtF,kBAAe;AACbf,EAAAA,mBADa;AAEbgD,EAAAA,kBAAkB,EAAEf,YAAY,CAACG;AAFpB,CAAf;;ACFO,MAAMa,QAAQ,GAAG,CAACC,GAAD,EAAgC1Y,MAAhC,KAAmD;AACzE0Y,EAAAA,GAAG,CAACC,SAAJ;QACM5U,KAAK,GAAG/D,MAAM,CAAC,CAAD;QACdgE,eAAe,GAAGhE,MAAM,CAACvG,KAAP,CAAa,CAAb;AACxBif,EAAAA,GAAG,CAACE,MAAJ,CAAW7U,KAAK,CAAC7E,CAAjB,EAAoB6E,KAAK,CAAC5E,CAA1B;;OACK,MAAME,SAAS2E,iBAAiB;AACnC0U,IAAAA,GAAG,CAACG,MAAJ,CAAWxZ,KAAK,CAACH,CAAjB,EAAoBG,KAAK,CAACF,CAA1B;;;AAEFuZ,EAAAA,GAAG,CAACnQ,MAAJ;AARK,CAAA;AAWP;;;;;AAKG;;AACI,MAAMuQ,kBAAkB,GAAI7U,UAAD,IAAuB;QACjD8U,SAAS,GAAG9U,UAAU,CAAChM,KAAX,CAAiB,kBAAjB,EAAqC2F,MAArC,CAA6Cob,IAAD,IAAUA,IAAI,KAAK,GAA/D;QACZC,QAAQ,GAAG,CAAEP,GAAD,IAAmCA,GAAG,CAACC,SAAJ,EAApC;;OACZ,MAAMK,QAAQD,WAAW;UACtB,CAACG,GAAD,EAAM,GAAGC,SAAT,IAAsBH,IAAI,CAAC/gB,KAAL,CAAW,KAAX;UACtBmhB,MAAM,GAAGD,SAAS,CAACtc,GAAV,CAAewc,KAAD,IAAWtf,UAAU,CAACsf,KAAD,CAAnC;;QACXH,GAAG,KAAK,KAAK;AACfD,MAAAA,QAAQ,CAACvb,IAAT,CAAegb,GAAD,IAASA,GAAG,CAACE,MAAJ,CAAW,GAAIQ,MAAf,CAAvB;AADF,WAEO,IAAIF,GAAG,KAAK,GAAZ,EAAiB;AACtBD,MAAAA,QAAQ,CAACvb,IAAT,CAAegb,GAAD,IAASA,GAAG,CAACG,MAAJ,CAAW,GAAIO,MAAf,CAAvB;AADK,KAAA,MAEA,IAAIF,GAAG,KAAK,GAAZ,EAAiB;AACtBD,MAAAA,QAAQ,CAACvb,IAAT,CAAegb,GAAD,IACZA,GAAG,CAACY,aAAJ,CAAkB,GAAIF,MAAtB,CADF;AADK,KAAA,MAIA,IAAIF,GAAG,KAAK,GAAZ,EAAiB;AACtBD,MAAAA,QAAQ,CAACvb,IAAT,CAAegb,GAAD,IACZA,GAAG,CAACa,gBAAJ,CAAqB,GAAIH,MAAzB,CADF;AADK,KAAA,MAIA;;;SAIDV,GAAD,IAAmCO,QAAQ,CAAC9a,OAAT,CAAkB+a,GAAD,IAASA,GAAG,CAACR,GAAD,CAA7B;AAtBrC,CAAA;;ACbP;;AACc,MAAOpF,gBAAP,SAA8BL,kBAA9B,CAAgD;AAO5DlY,EAAAA,WAAA,CAAYwN,MAAZ,EAA4BiR,SAAS,GAAG,IAAxC,EAA4C;UACpCjR;;QAEFiR,SAAS,IAAIC,QAAQ;WAClBC,UAAU,IAAID,MAAJ,CAAW,KAAKlR,MAAL,CAAY/D,IAAvB;AADjB,WAEO;WACAmV,WAAWb,kBAAkB,CAAC,KAAKvQ,MAAL,CAAY/D,IAAb;;;SAE/BoV,sBAAsBzV,WAAW,CACpC,KAAKoE,MAAL,CAAYvI,MADwB,EAEpCiT,kBAAkB,CAACE,YAAnB,GAAkC,CAFE;;;AAMxCiB,EAAAA,MAAM,CACJsE,GADI,EAEJrE,KAFI,EAOH;QAEGA,KAAK,CAACvY,OAAN,GAAgB,MAAM;;;;AAG1B4c,IAAAA,GAAG,CAACmB,IAAJ;;QAEI,KAAKH,SAAS;AAChBhB,MAAAA,GAAG,CAACoB,IAAJ,CAAS,KAAKJ,OAAd;AADF,WAEO;;;6BACAC,qFAAWjB,KADX;;AAGLA,MAAAA,GAAG,CAACqB,WAAJ,GAAkB,CAAlB;AACArB,MAAAA,GAAG,CAACnQ,MAAJ;AACAmQ,MAAAA,GAAG,CAACoB,IAAJ;;;UAGI;AAAEvgB,MAAAA,CAAF;AAAKG,MAAAA,CAAL;AAAQC,MAAAA,CAAR;AAAWC,MAAAA;AAAX,QAAiB,KAAKyZ,SAAL,CAAegB,KAAf;;UACjBhH,KAAK,GAAGzT,CAAC,KAAK,CAAN,UAAiBL,KAAKG,KAAKC,IAA3B,UAAyCJ,KAAKG,KAAKC,KAAKC;;UAChEogB,UAAU,GAAG,KAAK5G,oBAAL,CAA0BiB,KAAK,CAAChY,cAAhC;;AACnBqc,IAAAA,GAAG,CAACqB,WAAJ,GAAkB1F,KAAK,CAACvY,OAAxB;AACA4c,IAAAA,GAAG,CAACuB,WAAJ,GAAkB5M,KAAlB;AACAqL,IAAAA,GAAG,CAACwB,SAAJ,GAAgB7M,KAAhB;AACAqL,IAAAA,GAAG,CAACyB,SAAJ,GAAgBlH,kBAAkB,CAACE,YAAnC;AACAuF,IAAAA,GAAG,CAAC0B,OAAJ,GAAc,OAAd;AACA1B,IAAAA,GAAG,CAAC2B,QAAJ,GAAe,OAAf,CAzBC;;;AA4BD3B,IAAAA,GAAG,CAAC4B,WAAJ,CAAgB,CAAC,KAAKpH,WAAN,EAAmB,KAAKA,WAAxB,CAAhB,EAAsD8G,UAAtD;AACAtB,IAAAA,GAAG,CAAC6B,cAAJ,GAAqBP,UAArB;AACAvB,IAAAA,QAAQ,CAACC,GAAD,EAAM,KAAKkB,mBAAX,CAAR;AAEAlB,IAAAA,GAAG,CAAC8B,OAAJ;;;AA5D0D;;ACFhD,MAAO/F,mBAAP,CAAwB;AAGpC1Z,EAAAA,WAAA,CAAYC,SAAZ,EAAgC;SACzB0Z,mBAAmB1Z,SAAS,CAACgB,OAAV,CAAkBa,GAAlB,CAAuB0L,MAAD,IAAY,IAAI+K,gBAAJ,CAAmB/K,MAAnB,CAAlC;;;AAG1B6L,EAAAA,MAAM,CACJsE,GADI,EAEJrE,KAFI,EAOH;QAEGA,KAAK,CAACvY,OAAN,GAAgB,MAAM;UAEpB;AAAEA,MAAAA,OAAF;AAAWL,MAAAA,WAAX;AAAwBE,MAAAA,YAAxB;AAAsCK,MAAAA;AAAtC,QAAkDqY;;SAEnD,IAAIjc,CAAC,GAAG,GAAGA,CAAC,GAAG,KAAKsc,gBAAL,CAAsB1d,QAAQoB,CAAC,IAAI;WAChDsc,iBAAiBtc,GAAGgc,OAAOsE,KAAK;AACnCjd,QAAAA,WADmC;AAEnCE,QAAAA,YAFmC;AAGnCG,QAAAA,OAAO,EAAEE,OAAO,CAAC5D,CAAD,CAAP,CAAW0D,OAAX,GAAqBA,OAHK;AAInCO,QAAAA,cAAc,EAAEL,OAAO,CAAC5D,CAAD,CAAP,CAAWiE,cAAX,IAA6B;AAJV;;;;AArBL;;ACFxB,SAAUoe,gBAAV,CACZ/B,GADY,EAEZrE,KAFY,EAOX;MAEGA,KAAK,CAACvY,OAAN,GAAgB,MAAM;;;;QAGpB;AAAEA,IAAAA,OAAF;AAAWwZ,IAAAA,WAAX;AAAwB7Z,IAAAA,WAAxB;AAAqCuE,IAAAA;AAArC,MAAgDqU;QAChD;AAAE9a,IAAAA,CAAF;AAAKG,IAAAA,CAAL;AAAQC,IAAAA,CAAR;AAAWC,IAAAA;AAAX,MAAiB6B;AAEvBid,EAAAA,GAAG,CAACmB,IAAJ;AACAnB,EAAAA,GAAG,CAACqB,WAAJ,GAAkBje,OAAlB;AACA4c,EAAAA,GAAG,CAACyB,SAAJ,GAAgB7E,WAAhB;AACAoD,EAAAA,GAAG,CAACuB,WAAJ,WAA0B1gB,KAAKG,KAAKC,KAAKC,IAAzC;AACA8e,EAAAA,GAAG,CAAC0B,OAAJ,GAAc,OAAd;AACA1B,EAAAA,GAAG,CAAC2B,QAAJ,GAAe,OAAf;AACA5B,EAAAA,QAAQ,CAACC,GAAD,EAAM1Y,MAAN,CAAR;AACA0Y,EAAAA,GAAG,CAAC8B,OAAJ;;;AChBY,MAAOhF,qBAAP,CAA0B;AAStCza,EAAAA,WAAA,CAAYC,SAAZ,EAAkCgU,UAAlC,EAAwD;SAYxDuG,UAAU1a;SAXHuU,aAAapU;SACbqU,cAAcL;SACdyG,oBAAoB,IAAIhB,mBAAJ,CAAsBzZ,SAAtB;SACpB0a,uBAAuB,IAAIjB,mBAAJ,CAAsBzZ,SAAtB;SACvB2a,yBAAyB,IAAIlB,mBAAJ,CAAsBzZ,SAAtB;;;AAGhCwY,EAAAA,KAAK,CAAC7G,MAAD,EAA2B;SACzB+N,UAAU/N;;;AAKjBgO,EAAAA,eAAe,CAACpgB,EAAD,EAA4C;UACnD;AAAE6L,MAAAA,KAAF;AAASC,MAAAA,MAAT;AAAiBrD,MAAAA,KAAjB;AAAwB4D,MAAAA,OAAxB;AAAiCC,MAAAA;AAAjC,QAA6C,KAAKwI;;UAClDqJ,GAAG,GAAG,KAAKgC,OAAL,CAAcE,UAAd;;AACZlC,IAAAA,GAAG,CAACmC,SAAJ,CAAc,CAAd,EAAiB,CAAjB,EAAoBzU,KAApB,EAA2BC,MAA3B;AACAqS,IAAAA,GAAG,CAACmB,IAAJ;AACAnB,IAAAA,GAAG,CAACoC,SAAJ,CAAclU,OAAd,EAAuBP,MAAM,GAAGQ,OAAhC;AACA6R,IAAAA,GAAG,CAACqC,SAAJ,CAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAC,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;AACArC,IAAAA,GAAG,CAAC1V,KAAJ,CAAUA,KAAV,EAAiBA,KAAjB;AACAzI,IAAAA,EAAE,CAACme,GAAD,CAAF;AACAA,IAAAA,GAAG,CAAC8B,OAAJ,GATyD;;QAWrD9B,GAAG,CAACsC,MAAM;;AAEZtC,MAAAA,GAAG,CAACsC,IAAJ;;;;AAIJ5G,EAAAA,MAAM,CAACC,KAAD,EAAyB;UACvB;AAAEpY,MAAAA,OAAF;AAAWJ,MAAAA,IAAX;AAAiBM,MAAAA;AAAjB,QAA+BkY,KAAK,CAACrZ;UACrC;AACJU,MAAAA,YADI;AAEJD,MAAAA,WAFI;AAGJE,MAAAA,YAHI;AAIJC,MAAAA,cAJI;AAKJJ,MAAAA,YALI;AAMJD,MAAAA;AANI,QAOF8Y,KAAK,CAACpZ;;SAEL0f,gBAAiBjC,GAAD,IAAQ;WACtBhD,qBAAqBtB,OAAOsE,KAAK;AACpC5c,QAAAA,OAAO,EAAEG,OAAO,CAACH,OADmB;AAEpCE,QAAAA,OAAO,EAAEC,OAAO,CAACD,OAFmB;AAGpCP,QAAAA,WAAW,EAAEC;AAHuB;;WAKjC+Z,kBAAkBrB,OAAOsE,KAAK;AACjC5c,QAAAA,OAAO,EAAED,IAAI,CAACC,OADmB;AAEjCE,QAAAA,OAAO,EAAEH,IAAI,CAACG,OAFmB;AAGjCP,QAAAA,WAAW,EAAEA,WAHoB;AAIjCE,QAAAA,YAAY,EAAEA;AAJmB;;WAM9Bga,uBAAuBvB,OAAOsE,KAAK;AACtC5c,QAAAA,OAAO,EAAEK,SAAS,CAACL,OADmB;AAEtCE,QAAAA,OAAO,EAAEG,SAAS,CAACH,OAFmB;AAGtCP,QAAAA,WAAW,EAAEG;AAHyB;;YAMlCQ,WAAW,GAAGiY,KAAK,CAACjY,WAAN,IAAqB;;WAEpC,MAAMoS,gBAAgBpS,aAAa;cAChCgL,UAAU,GAAGhL,WAAW,CAACoS,YAAD;;YAC1BpH,YAAY;gBACR4O,eAAe,GAAG;AACtBV,YAAAA,WAAW,EAAE/Z,YADS;AAEtBE,YAAAA,WAAW,EAAED,YAFS;eAGnB4L;AAHmB;AAKxBqT,UAAAA,gBAAgB,CAAC/B,GAAD,EAAM1C,eAAN,CAAhB;;;AA5BN;;;AAnDoC;;ACP1B,MAAOyB,cAAP,SAA4BtB,gBAA5B,CAA+D;AAC3Epb,EAAAA,WAAA,CAAYkgB,MAAZ,EAAqC;UAC7BA;;;SAGDrD,KAAKC,SAAqCzR,KAAK,GAAG,QAAQC,MAAM,GAAG,QAAM;UACxEyR,OAAO,GAAG,CAAC,MAAK;UAChB,OAAOD,OAAP,KAAmB,UAAU;eACxB/F,QAAQ,CAACiG,cAAT,CAAwBF,OAAxB;;;aAEFA;AAJO,KAAA;;QAOZ,CAACC,SAAS;YACN,IAAI9d,KAAJ,0CAAmD6d,SAAnD;;;UAGFG,QAAQ,GAAGF,OAAO,CAACG,QAAR,CAAiBjf,WAAjB;;UAEXiiB,MAAM,GAAG,CAAC,MAAK;UACfjD,QAAQ,KAAK,UAAU;eAClBF;;;YAEHmD,MAAM,GAAGnJ,QAAQ,CAACoJ,aAAT,CAAuB,QAAvB;AACfpD,MAAAA,OAAO,CAAC5D,WAAR,CAAoB+G,MAApB;aACOA;AANM,KAAA;;AASfA,IAAAA,MAAM,CAAClE,YAAP,CAAoB,OAApB,EAA6B3Q,KAA7B;AACA6U,IAAAA,MAAM,CAAClE,YAAP,CAAoB,QAApB,EAA8B1Q,MAA9B;WAEO,IAAIoR,cAAJ,CAAiBwD,MAAjB;;;AAGTL,EAAAA,UAAU,GAAA;WACD,KAAKxE,IAAL,CAAUwE,UAAV,CAAqB,IAArB;;;AAnCkE;;ACE7E,qBAAe;AACbpF,uBAAAA,qBADa;AAEbgD,EAAAA,kBAAkB,EAAEf,cAAY,CAACG;AAFpB,CAAf;;ACFA,MAAMuD,OAAO,GAAG,KAAhB;;AACA,MAAMC,cAAc,GAAIC,IAAD,sDAC6BF,WAAWE,WAD/D;;AAGA,MAAMC,qBAAqB,GAAG,CAC5BD,IAD4B,EAE5BE,MAF4B,EAG5BC,OAH4B,KAI1B;;QAEIC,GAAG,GAAG,IAAIC,cAAJ;;MACRD,GAAG,CAACE,kBAAkB;;AAExBF,IAAAA,GAAG,CAACE,gBAAJ,CAAqB,kBAArB;;;AAEFF,EAAAA,GAAG,CAACG,IAAJ,CAAS,KAAT,EAAgBR,cAAc,CAACC,IAAD,CAA9B,EAAsC,IAAtC;;AACAI,EAAAA,GAAG,CAACI,OAAJ,GAAeC,KAAD,IAAU;AACtBN,IAAAA,OAAO,CAACC,GAAD,EAAMK,KAAN,CAAP;AADF,GAAA;;AAGAL,EAAAA,GAAG,CAACM,kBAAJ,GAAyB,MAAK;;QAExBN,GAAG,CAACO,UAAJ,KAAmB,GAAG;;QAEtBP,GAAG,CAACQ,MAAJ,KAAe,KAAK;AACtBV,MAAAA,MAAM,CAACW,IAAI,CAACC,KAAL,CAAWV,GAAG,CAACW,YAAf,CAAD,CAAN;AADF,WAEO,IAAIX,GAAG,CAACQ,MAAJ,KAAe,CAAf,IAAoBT,OAAxB,EAAiC;AACtCA,MAAAA,OAAO,CAACC,GAAD,CAAP;;AAPJ,GAAA;;AAUAA,EAAAA,GAAG,CAACY,IAAJ,CAAS,IAAT;AAzBF,CAAA;;ACHA,MAAMC,cAAc,GAAuB;AACzCC,EAAAA,cAAc,EAAEjB,qBADyB;AAEzCkB,EAAAA,mBAAmB,EAAE,IAFoB;AAGzCC,EAAAA,qBAAqB,EAAE,IAHkB;AAIzCvgB,EAAAA,WAAW,EAAE,IAJ4B;AAKzCH,EAAAA,aAAa,EAAE,IAL0B;AAMzC2gB,EAAAA,QAAQ,EAAE,KAN+B;;AAUzCtW,EAAAA,KAAK,EAAE,CAVkC;AAWzCC,EAAAA,MAAM,EAAE,CAXiC;AAYzCF,EAAAA,OAAO,EAAE,EAZgC;;AAgBzCwW,EAAAA,oBAAoB,EAAE,CAhBmB;AAiBzChN,EAAAA,kBAAkB,EAAE,GAjBqB;AAkBzC4B,EAAAA,uBAAuB,EAAE,GAlBgB;AAmBzCZ,EAAAA,oBAAoB,EAAE,CAnBmB;AAoBzC5C,EAAAA,mBAAmB,EAAE,IApBoB;AAqBzCG,EAAAA,iBAAiB,EAAE,IArBsB;;AAyBzCzS,EAAAA,WAAW,EAAE,MAzB4B;AA0BzCE,EAAAA,YAAY,EAAE,IA1B2B;AA2BzCC,EAAAA,cAAc,EAAE,MA3ByB;AA4BzCF,EAAAA,YAAY,EAAE,MA5B2B;AA6BzCF,EAAAA,YAAY,EAAE,MA7B2B;;AAiCzCuM,EAAAA,QAAQ,EAAE,CAjC+B;AAkCzC2I,EAAAA,mBAAmB,EAAE,CAlCoB;AAmCzCW,EAAAA,mBAAmB,EAAE,IAnCoB;AAoCzCC,EAAAA,sBAAsB,EAAE,IApCiB;AAqCzCnB,EAAAA,4BAA4B,EAAE,KArCW;AAsCzCD,EAAAA,sBAAsB,EAAE,KAtCiB;AAuCzCR,EAAAA,kBAAkB,EAAE,CAvCqB;AAwCzC/F,EAAAA,wBAAwB,EAAE,GAxCe;;AA4CzCrO,EAAAA,mBAAmB,EAAE,GA5CoB;AA6CzCC,EAAAA,YAAY,EAAE,CA7C2B;AA8CzC+Z,EAAAA,WAAW,EAAE,CA9C4B;AA+CzCsH,EAAAA,YAAY,EAAE,CA/C2B;AAgDzCC,EAAAA,gBAAgB,EAAE;AAhDuB,CAA3C;;ACCc,MAAOC,cAAP,CAAqB;AAYjC/hB,EAAAA,WAAA,CAAYE,OAAZ,EAA0C;SAX1C8hB,eAAe;SACfC,aAAa;;;SAQbC,gBAAgB;SAGTzN,WAAWvU;;;AAGlBiiB,EAAAA,cAAc,CAAC7B,IAAD,EAAe/iB,KAAf,EAA4B;;UAElC6kB,cAAc,GAAIC,IAAD,IAAwB;UACzC9kB,KAAK,KAAK,KAAKykB,cAAc;;;+BAC1B1f,qFAAW+f;;AAFpB;;UAKMC,aAAa,GAAIC,MAAD,IAA4B;UAC5ChlB,KAAK,KAAK,KAAKykB,cAAc;;;8BAC1BQ,kFAAUD;;AAFnB;;UAMME,YAAY,GAAG,KAAKhO,QAAL,CAAc+M,cAAd,CACnBlB,IADmB,EAEnB8B,cAFmB,EAGnBE,aAHmB;;QAMjBG,cAAc;UACZ,UAAUA,cAAc;AAC1BA,QAAAA,YAAY,CAACxf,IAAb,CAAkBmf,cAAlB,EAAkCM,KAAlC,CAAwCJ,aAAxC;AADF,aAEO;AACLF,QAAAA,cAAc,CAACK,YAAD,CAAd;;;;;AAKNE,EAAAA,oBAAoB,GAAA;WACX,IAAI1gB,OAAJ,CACL,CACEC,OADF,EAEE0gB,MAFF,KAGI;WACGtgB,WAAWJ;WACXsgB,UAAUI;AANZ,KAAA,EASJ3f,IATI,CASEof,IAAD,IAAwB;;;WACvBJ,aAAa;sDACbxN,UAASiN,0HAAwBW;aAC/BA;AAZJ,KAAA,EAcJK,KAdI,CAcGH,MAAD,IAAW;WACXN,aAAa;WACbC,gBAAgB,KAFL;;;UAMZ,KAAKzN,QAAL,CAAcgN,qBAAqB;aAChChN,SAASgN,oBAAoBc;;;AAPpB,OAAA;;;UAYZA,MAAM,YAAYtjB,OAAO;cACrBsjB;;;YAGFM,GAAG,GAAG,IAAI5jB,KAAJ,iCACsB,KAAK6jB,cAD3B;AAIZD,MAAAA,GAAG,CAACN,MAAJ,GAAaA,MAAb;YAEMM;AApCH,KAAA;;;AAwCTE,EAAAA,YAAY,CAACzC,IAAD,EAAa;SAClBwC,eAAexC;;UACd0C,OAAO,GAAG,KAAKL,oBAAL;;SACXT,gBAAgB;SAChBD,aAAa;SACbD;;SACAG,eAAe7B,MAAM,KAAK0B;;WACxBgB;;;AA5FwB;;AC4BrB,MAAOC,WAAP,CAAkB;AAuE9BjjB,EAAAA,WAAA,CAAY+c,OAAZ,EAA2C7c,OAAA,GAAuC,EAAlF,EAAoF;UAC5E;AAAEua,MAAAA,mBAAF;AAAuBgD,MAAAA;AAAvB,QACJvd,OAAO,CAACyhB,QAAR,KAAqB,QAArB,GAAgCuB,cAAhC,GAAiDC;UAC7CrB,gBAAgB,GAAG5hB,OAAO,CAAC4hB,gBAAR,IAA4B;SAEhDsB,YAAY;AACf3I,MAAAA,mBAAmB,EAAEqH,gBAAgB,CAACrH,mBAAjB,IAAwCA,mBAD9C;AAEfgD,MAAAA,kBAAkB,EAAEqE,gBAAgB,CAACrE,kBAAjB,IAAuCA;AAF5C,MALiE;;SAU7E7L,SAAS,KAAKwR,SAAL,CAAe3F,kBAAf,CACZV,OADY,EAEZ7c,OAAO,CAACmL,KAFI,EAGZnL,OAAO,CAACoL,MAHI;SAKTmJ,WAAW,KAAK4O,cAAL,CAAoBnjB,OAApB;SACXojB,kBAAkB,IAAIvB,cAAJ,CAAmB,KAAKtN,QAAxB;;SAClB8O;;;;;SA/DAC,OACLzG,SACA9c,WACAC,SAAqC;UAE/BujB,MAAM,GAAG,IAAIR,WAAJ,CAAgBlG,OAAhB,EAAyB7c,OAAzB;AACfujB,IAAAA,MAAM,CAACC,YAAP,CAAoBzjB,SAApB;WAEOwjB;;;SAQFE,kBACL1jB,WACAC,OAAA,GAA0C,IAAE;UAEtC0jB,cAAc,GAAG,CAAC,MAAK;YACrB;AAAEN,QAAAA,eAAF;AAAmBO,QAAAA;AAAnB,UAAuCZ;;UACzC,CAAAK,eAAe,SAAf,IAAAA,eAAe,WAAf,SAAA,GAAAA,eAAe,CAAER,YAAjB,MAAkC7iB,SAAlC,IAA+C4jB,eAAe,KAAK3jB,SAAS;eACvEojB;;;aAEF,IAAIvB,cAAJ,CAAmB,EAAE,GAAGR,cAAL;WAAwBrhB;AAAxB,OAAnB;AALc,KAAA;;AAQvB+iB,IAAAA,WAAW,CAACK,eAAZ,GAA8BM,cAA9B;AACAX,IAAAA,WAAW,CAACY,eAAZ,GAA8B3jB,OAA9B;WACO0jB,cAAc,CAACb,YAAf,CAA4B9iB,SAA5B;;;SAGF6jB,oBAAoBzY,OAAeC,QAAgBF,OAAO,GAAG,GAAC;UAC7D6I,UAAU,GAAG,IAAI9I,UAAJ,CAAe;AAAEE,MAAAA,KAAF;AAASC,MAAAA,MAAT;AAAiBF,MAAAA;AAAjB,KAAf;WACZ;AACLjH,MAAAA,CAAC,EAAE8P,UAAU,CAACpI,OADT;AAELzH,MAAAA,CAAC,EAAE6P,UAAU,CAACnI,OAFT;AAGL7D,MAAAA,KAAK,EAAEgM,UAAU,CAAChM,KAHb;AAIL+X,MAAAA,SAAS,EAAE9hB,IAAI;oBACD+V,UAAU,CAACpI,YAAYoI,UAAU,CAAC3I,MAAX,GAAoB2I,UAAU,CAACnI;gBAC1DmI,UAAU,CAAChM,UAAU,CAAC,CAAD,GAAKgM,UAAU,CAAChM;AAC9C,OAHc,CAAJ,CAGR9I,OAHQ,CAGA,MAHA,EAGQ,GAHR;AAJN;;;AA+BT6B,EAAAA,aAAa,CACXd,OAAA,GAGI,EAJO,EAIL;SAEDuU,SAASzT,gBAAgB;WACvB,KAAK+iB,SAAL,CAAe;;;mCACpB,KAAKnT,mEAALoT,mBACIriB,GADJ,CAEI2R,aAAA,CACE,MADF,EAEE,KAAKe,UAFP,EAGE,OAAOnU,OAAO,CAAC2P,QAAf,KAA4B,QAA5B,GACI3P,OAAO,CAAC2P,QADZ,GAEI,KAAK4E,QAAL,CAAcG,kBALpB,CAFJ,EAUG3R,IAVH,CAUSghB,GAAD,IAAQ;;;+BACZ/jB,OAAO,CAACmW,2FAARnW,SAAqB+jB;eACdA;AAZX,OAAA;AADK,KAAA;;;AAkBThS,EAAAA,aAAa,CACX/R,OAAA,GAGI,EAJO,EAIL;SAEDuU,SAASzT,gBAAgB;WACvB,KAAK+iB,SAAL,CAAe;;;oCACpB,KAAKnT,oEAALsT,oBACIviB,GADJ,CAEI2R,aAAA,CACE,MADF,EAEE,KAAKe,UAFP,EAGE,OAAOnU,OAAO,CAAC2P,QAAf,KAA4B,QAA5B,GACI3P,OAAO,CAAC2P,QADZ,GAEI,KAAK4E,QAAL,CAAcG,kBALpB,CAFJ,EAUG3R,IAVH,CAUSghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAZX,OAAA;AADK,KAAA;;;AAkBTnR,EAAAA,gBAAgB,CACd5S,OAAA,GAEI,EAHU,EAGR;SAEDikB;WAEE,KAAKJ,SAAL,CAAe;;;oCACpB,KAAKnT,oEAALwT,oBACIziB,GADJ,CAEI2R,gBAAA,CACE,MADF,EAEE,KAAKe,UAFP,EAGE,KAAKI,QAAL,CAAcG,kBAHhB,EAIE,KAAKH,QAAL,CAAcmN,oBAJhB,EAKE,KAAKnN,QAAL,CAAczB,mBALhB,CAFJ,EAUG/P,IAVH,CAUSghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAZX,OAAA;AADK,KAAA;;;AAkBTzR,EAAAA,aAAa,CACX9I,SADW,EAEXxJ,OAAA,GAEI,EAJO,EAIL;SAEDikB;WACE,KAAKJ,SAAL,CAAe;;;oCACpB,KAAKnT,oEAALyT,oBACI1iB,GADJ,CAEI2R,mBAAA,CACE,MADF,EAEE,KAAKe,UAFP,EAGEnY,QAAQ,CAACwN,SAAD,EAAY,KAAK2K,UAAL,CAAiBpT,OAAjB,CAAyBhF,MAArC,CAHV,EAIE,KAAKwY,QAAL,CAAcmN,oBAJhB,CAFJ,EASG3e,IATH,CASSghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAXX,OAAA;AADK,KAAA;;;AAiBT5R,EAAAA,eAAe,CACb3I,SADa,EAEbxJ,OAAA,GAEI,EAJS,EAIP;UAEA8iB,OAAO,GAAG,MAAK;UACf,CAAC,KAAK3O,UAAN,IAAoB,CAAC,KAAKzD,cAAc;;;;aAIrC,KAAKA,YAAL,CACJjP,GADI,CAEH2R,eAAA,CACElX,WAAW,CAAC,KAAKiY,UAAL,CAAgBpT,OAAjB,EAA0ByI,SAA1B,CADb,EAEE5L,iBAAiB,CAAC,KAAK2W,QAAL,CAAc5T,cAAf,CAFnB,EAGE,KAAK4T,QAAL,CAAcmB,oBAHhB,CAFG,EAQJ3S,IARI,CAQEghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAVJ,OAAA;AALT;;WAmBO,KAAKF,SAAL,CAAef,OAAf;;;QAGHsB,yBAAsB;SACrBH;WACE,KAAKJ,SAAL,CAAe,MACpB,KAAKnT,YAAL,CAAmBjP,GAAnB,CACE2R,oBAAA,CACE,MADF,EAEE,KAAKe,UAFP,EAGE,KAAKI,QAAL,CAAcG,kBAHhB,EAIE,KAAKH,QAAL,CAAcmN,oBAJhB,EAKE,KAAKnN,QAAL,CAAczB,mBALhB,EAME,KAAKyB,QAAL,CAActB,iBANhB,CADF,EASE;AAAE1Q,MAAAA,IAAI,EAAE;AAAR,KATF,CADK;;;AAeT8hB,EAAAA,cAAc,GAAA;WACL,KAAKR,SAAL,CAAe;;;oCAAM,KAAKnT,oEAAL4T,oBAAmBrhB,QAAnB;AAArB,KAAA;;;AAGTshB,EAAAA,eAAe,GAAA;WACN,KAAKV,SAAL,CAAe;;;oCAAM,KAAKnT,oEAAL8T,oBAAmBnhB,SAAnB;AAArB,KAAA;;;AAGTpC,EAAAA,WAAW,CACTjB,OAAA,GAGI,EAJK,EAIH;SAEDuU,SAAStT,cAAc;WACrB,KAAK4iB,SAAL,CAAe;;;oCACpB,KAAKnT,oEAAL+T,oBACIhjB,GADJ,CAEI2R,aAAA,CACE,SADF,EAEE,KAAKe,UAFP,EAGE,OAAOnU,OAAO,CAAC2P,QAAf,KAA4B,QAA5B,GACI3P,OAAO,CAAC2P,QADZ,GAEI,KAAK4E,QAAL,CAAcG,kBALpB,CAFJ,EAUG3R,IAVH,CAUSghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAZX,OAAA;AADK,KAAA;;;AAkBTW,EAAAA,WAAW,CACT1kB,OAAA,GAGI,EAJK,EAIH;SAEDuU,SAAStT,cAAc;WACrB,KAAK4iB,SAAL,CAAe;;;oCACpB,KAAKnT,oEAALiU,oBACIljB,GADJ,CAEI2R,aAAA,CACE,SADF,EAEE,KAAKe,UAFP,EAGE,OAAOnU,OAAO,CAAC2P,QAAf,KAA4B,QAA5B,GACI3P,OAAO,CAAC2P,QADZ,GAEI,KAAK4E,QAAL,CAAcG,kBALpB,CAFJ,EAUG3R,IAVH,CAUSghB,GAAD,IAAQ;;;gCACZ/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAZX,OAAA;AADK,KAAA;;;;;AAmBTlI,EAAAA,gBAAgB,CAAC;AAAE1Q,IAAAA,KAAF;AAASC,IAAAA,MAAT;AAAiBF,IAAAA;AAAjB,GAAD,EAAsD;QAChEC,KAAK,KAAK+E,WAAW,KAAKqE,QAAL,CAAcpJ,KAAd,GAAsBA,KAAtB;QACrBC,MAAM,KAAK8E,WAAW,KAAKqE,QAAL,CAAcnJ,MAAd,GAAuBA,MAAvB;QACtBF,OAAO,KAAKgF,WAAW,KAAKqE,QAAL,CAAcrJ,OAAd,GAAwBA,OAAxB;SACtBwG,OAAOmK,iBAAiB,KAAKtH,QAAL,CAAcpJ,OAAO,KAAKoJ,QAAL,CAAcnJ,QAJI;;QAOlE,KAAK+I,UAAL,IACA,KAAKzD,YADL,IAEA,KAAKkU,oBAFL,IAGA,KAAKxQ,aACL;WACKwQ,qBAAqBtK;;YACpBuK,mBAAmB,GAAG,KAAKC,gCAAL,CAAsC,KAAK3Q,UAA3C,EAF5B;;;WAIKzD,aAAarP,uBAAwBG,SAAD,IACvCqjB,mBAAmB,CAAC1L,MAApB,CAA2B3X,SAA3B;;AAEFqjB,MAAAA,mBAAmB,CAAC1L,MAApB,CAA2B,KAAKzI,YAAL,CAAkBtQ,KAA7C,EAPA;;UASI,KAAK2kB,OAAO;aACTA,MAAM/P,cAAc,KAAKZ;;;;;AAKpCpC,EAAAA,WAAW,CACTC,SADS,EAETC,QAFS,EAGTlS,OAAA,GAGI,EANK,EAMH;;;QAEF0B,SAAS,GAAsB;;UAE7BsjB,aAAa,GAAG,CAAC,MAAK;;UAEtB/S,SAAS,KAAK,cAAd,IAAgC,CAACC,UAAU;eACtC,KAAKqC,QAAL,CAAc/T;;;aAEhB0R;AALa,KAAA;;UAQhB+S,WAAW,GAAGrnB,iBAAiB,CAAConB,aAAD;SAEhCzQ,SAAStC,aAAaC;UAErBvC,QAAQ,wBAAG3P,OAAO,CAAC2P,yEAAY,KAAK4E,QAAL,CAAcG;AAEnDhT,IAAAA,SAAS,GAAGA,SAAS,CAACqR,MAAV,CACVK,WAAA,CAA6BnB,SAA7B,EAAwCgT,WAAxC,EAAqDtV,QAArD,CADU,CAAZ,CAlBM;;QAuBFsC,SAAS,KAAK,cAAd,IAAgC,CAACC,UAAU;AAC7CxQ,MAAAA,SAAS,GAAGA,SAAS,CAACqR,MAAV,CAAiBK,WAAA,CAA6BnB,SAA7B,EAAwC,IAAxC,EAA8C,CAA9C,CAAjB,CAAZ;;;WAGK,KAAK4R,SAAL,CAAe;;;oCACpB,KAAKnT,oEAALwU,oBAAmBzjB,GAAnB,CAAuBC,SAAvB,EAAkCqB,IAAlC,CAAwCghB,GAAD,IAAQ;;;gCAC7C/jB,OAAO,CAACmW,6FAARnW,SAAqB+jB;eACdA;AAFT,OAAA;AADK,KAAA;;;AAQToB,EAAAA,IAAI,CAACC,WAAA,GAAoC,EAArC,EAAuC;WAClC,KAAKvB,SAAL,CAAe,YAAW;UAC3B,KAAK1P,UAAL,IAAmB,KAAKzD,YAAxB,IAAwC,KAAK0D,aAAa;aACvD6P;aACAc,QAAQ,IAAIjR,IAAJ,CAAS,KAAKK,UAAd,EAA0B,KAAKzD,YAA/B,EAA6C,KAAK0D,WAAlD;aACRG,WAAW,EACd,GAAG,KAAKA,QADM;aAEX6Q;AAFW;;aAIXL,MAAM7R,UAAU,KAAKqB;;AARvB,KAAA;;;AAaT8Q,EAAAA,cAAc,GAAA;QACR,KAAKN,OAAO;WACTA,MAAM7O;;;;AAIf+N,EAAAA,UAAU,GAAA;QACJ,KAAKc,OAAO;WACTA,MAAMlhB;;WACNkhB,QAAQ7U;;;;AAIjBsT,EAAAA,YAAY,CAACpD,IAAD,EAAa;SAClB6D;SACAqB,QAAQlF;;QACT,KAAKwE,sBAAsB;WACxBA,qBAAqBtK;;;QAExB,KAAK5J,cAAc;WAChBA,aAAa9M;;;SAEfghB,uBAAuB;SACvBW,mBAAmB,KAAKnC,eAAL,CACrBP,YADqB,CACRzC,IADQ,EAErBrd,IAFqB,CAEfyiB,WAAD,IAAgB;;UAEhB,CAACA,WAAD,IAAgB,KAAKpC,eAAL,CAAqBpB,eAAe;;;;WAInD7N,aAAazJ,aAAa,CAAC0V,IAAD,EAAOoF,WAAP;WAC1B9U,eAAe,IAAI7Q,WAAJ,CAAgB,KAAKsU,UAArB,EAAiC,KAAKI,QAAtC,EAAiD/S,SAAD,IAClEqjB,mBAAmB,CAAC1L,MAApB,CAA2B3X,SAA3B,CADkB;;YAIdqjB,mBAAmB,GAAG,KAAKC,gCAAL,CAC1B,KAAK3Q,UADqB;;AAG5B0Q,MAAAA,mBAAmB,CAAC1L,MAApB,CAA2B,KAAKzI,YAAL,CAAkBtQ,KAA7C;AAhBoB,KAAA;WAkBjB,KAAKmlB;;;AAGdT,EAAAA,gCAAgC,CAAC/kB,SAAD,EAAqB;UAC7C;AAAEoL,MAAAA,KAAF;AAASC,MAAAA,MAAT;AAAiBF,MAAAA;AAAjB,QAA6B,KAAKqJ;SACnCH,cAAc,IAAInJ,UAAJ,CAAe;AAAEE,MAAAA,KAAF;AAASC,MAAAA,MAAT;AAAiBF,MAAAA;AAAjB,KAAf;UACb2Z,mBAAmB,GAAG,IAAI,KAAK3B,SAAL,CAAe3I,mBAAnB,CAC1Bxa,SAD0B,EAE1B,KAAKqU,WAFqB;AAI5ByQ,IAAAA,mBAAmB,CAACtM,KAApB,CAA0B,KAAK7G,MAA/B;SACKkT,uBAAuBC;WACrBA;;;QAGHY,mBAAgB;QAChB,CAAC,KAAKH,OAAO;YACT,IAAIvmB,KAAJ,CAAU,iEAAV;;;UAEFgB,SAAS,GAAG,MAAM,KAAK8jB,SAAL,CAAe,MAAM,KAAK1P,UAA1B;WACjBpU;;;AAGTojB,EAAAA,cAAc,CAACnjB,OAAD,EAAqC;UAC3C0lB,aAAa,GAAG,EACpB,GAAGrE,cADiB;SAEjBrhB;AAFiB,MAD2B;;QAO7CA,OAAO,CAAC2lB,uBAAR,IAAmC,CAAC3lB,OAAO,CAAC0hB,sBAAsB;AACpEgE,MAAAA,aAAa,CAAChE,oBAAd,GAAqC,MAAM1hB,OAAO,CAAC2lB,uBAAnD;;;QAEE3lB,OAAO,CAACsW,uBAAR,IAAmC,CAACtW,OAAO,CAAC0V,sBAAsB;AACpEgQ,MAAAA,aAAa,CAAChQ,oBAAd,GAAqC,MAAMgQ,aAAa,CAACpP,uBAAzD;;;QAGE,CAACtW,OAAO,CAACqW,wBAAwB;AACnCqP,MAAAA,aAAa,CAACrP,sBAAd,GAAuCqP,aAAa,CAAC/kB,cAArD;;;WAGK,KAAKilB,mBAAL,CAAyBF,aAAzB;;;;;AAITE,EAAAA,mBAAmB,CAAC5lB,OAAD,EAA4B;UACvC6lB,UAAU,GAAG,EAAE,GAAG7lB;AAAL;;QACf6lB,UAAU,CAAC1a,KAAX,IAAoB,CAAC0a,UAAU,CAACza,QAAQ;AAC1Cya,MAAAA,UAAU,CAACza,MAAX,GAAoBya,UAAU,CAAC1a,KAA/B;AADF,WAEO,IAAI0a,UAAU,CAACza,MAAX,IAAqB,CAACya,UAAU,CAAC1a,KAArC,EAA4C;AACjD0a,MAAAA,UAAU,CAAC1a,KAAX,GAAmB0a,UAAU,CAACza,MAA9B;AADK,KAAA,MAEA,IAAI,CAACya,UAAU,CAAC1a,KAAZ,IAAqB,CAAC0a,UAAU,CAACza,MAArC,EAA6C;YAC5C;AAAED,QAAAA,KAAF;AAASC,QAAAA;AAAT,UAAoB,KAAKsG,MAAL,CAAYkK,qBAAZ;YACpBkK,MAAM,GAAGzhB,IAAI,CAAC8B,GAAL,CAASgF,KAAT,EAAgBC,MAAhB;AACfya,MAAAA,UAAU,CAAC1a,KAAX,GAAmB2a,MAAnB;AACAD,MAAAA,UAAU,CAACza,MAAX,GAAoB0a,MAApB;;;WAEKD;;;AAGThC,EAAAA,SAAS,CAAIkC,IAAJ,EAAiB;;QAEpB,KAAK3C,eAAL,CAAqBpB,eAAe;YAChCjjB,KAAK,CAAC,iEAAD;;;QAGT,KAAKwmB,kBAAkB;aAClB,KAAKA,gBAAL,CAAsBxiB,IAAtB,CAA2B,MAAK;YACjC,CAAC,KAAKqgB,eAAL,CAAqBpB,eAAe;iBAChC+D,IAAI;;AAFR,OAAA;;;WAMFhkB,OAAO,CAACC,OAAR,GAAkBe,IAAlB,CAAuBgjB,IAAvB;;;AAGT1C,EAAAA,eAAe,GAAA;SACR3R,OAAO0J,wBAAyBE,GAAD,IAAQ;UACtC,KAAKyJ,OAAO;AACdzJ,QAAAA,GAAG,CAACY,cAAJ;;aACK6I,MAAM1R,gBAAgBiI,GAAG,CAACU,QAAJ;;AAH/B;SAMKtK,OAAOgK,uBAAwBJ,GAAD,IAAQ;UACrC,KAAKyJ,OAAO;AACdzJ,QAAAA,GAAG,CAACY,cAAJ;;aACK6I,MAAMjQ,mBAAmBwG,GAAG,CAACU,QAAJ;;AAHlC;SAMKtK,OAAOiK,sBAAsB,MAAK;;;0BAChCoJ,iEAAOnQ;AADd;;;AAzf4B;AAoC9B;;AACOmO,WAAA,CAAAK,eAAA,GAAyC,IAAzC;AACP;;AACOL,WAAA,CAAAY,eAAA,GAAsD,IAAtD;;"}