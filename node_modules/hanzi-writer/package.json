{"name": "hanzi-writer", "version": "3.7.2", "author": "<PERSON> <<EMAIL>> (http://chanind.github.io/hanzi-writer/)", "description": "Hanzi Writer is a free and open-source javascript library for both animating simplified Chinese characters and quizzing users on character stroke order.", "repository": "chanind/hanzi-writer", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/types/index.esm.d.ts", "license": "MIT", "devDependencies": {"@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@babel/preset-typescript": "^7.12.7", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-node-resolve": "^11.1.0", "@sinonjs/fake-timers": "^7.0.2", "@types/jest": "^26.0.20", "@types/nise": "^1.4.0", "@types/sinonjs__fake-timers": "^6.0.2", "@typescript-eslint/eslint-plugin": "^4.14.0", "@typescript-eslint/parser": "^4.14.0", "@wessberg/rollup-plugin-ts": "^1.3.8", "codecov": "^3.8.1", "eslint": "^7.18.0", "hanzi-writer-data": "^2.0.1", "jest-canvas-mock": "2.1.0", "jest-cli": "^26.6.3", "jest-fetch-mock": "^3.0.3", "nise": "^4.0.4", "prettier": "^2.2.1", "rollup": "^2.36.2", "rollup-plugin-filesize": "^9.1.0", "rollup-plugin-license": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "semantic-release": "^17.3.3", "typescript": "^4.1.3"}, "scripts": {"test": "jest", "lint-test": "eslint -c .eslintrc src", "lint-fix": "eslint --fix --ext .tsx,.ts .", "build": "rm -rf dist && rollup -c", "prepublishOnly": "rollup -c", "semantic-release": "semantic-release", "prettier": "prettier -w src", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "files": ["dist"], "jest": {"setupFiles": ["jest-canvas-mock"], "setupFilesAfterEnv": ["<rootDir>/../jest-setup.js"], "rootDir": "src", "coverageDirectory": "../coverage/", "testURL": "https://test.com/url#tag", "collectCoverage": true, "testEnvironment": "<rootDir>/../jest-jsdom-env"}, "dependencies": {}}