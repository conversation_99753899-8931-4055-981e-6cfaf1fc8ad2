{"version": 3, "sources": ["../../hanzi-writer/src/utils.ts", "../../hanzi-writer/src/RenderState.ts", "../../hanzi-writer/src/geometry.ts", "../../hanzi-writer/src/models/Stroke.ts", "../../hanzi-writer/src/models/Character.ts", "../../hanzi-writer/src/parseCharData.ts", "../../hanzi-writer/src/Positioner.ts", "../../hanzi-writer/src/strokeMatches.ts", "../../hanzi-writer/src/models/UserStroke.ts", "../../hanzi-writer/src/Mutation.ts", "../../hanzi-writer/src/characterActions.ts", "../../hanzi-writer/src/quizActions.ts", "../../hanzi-writer/src/Quiz.ts", "../../hanzi-writer/src/renderers/svg/svgUtils.ts", "../../hanzi-writer/src/renderers/StrokeRendererBase.ts", "../../hanzi-writer/src/renderers/svg/StrokeRenderer.ts", "../../hanzi-writer/src/renderers/svg/CharacterRenderer.ts", "../../hanzi-writer/src/renderers/svg/UserStrokeRenderer.ts", "../../hanzi-writer/src/renderers/svg/HanziWriterRenderer.ts", "../../hanzi-writer/src/renderers/RenderTargetBase.ts", "../../hanzi-writer/src/renderers/svg/RenderTarget.ts", "../../hanzi-writer/src/renderers/svg/index.ts", "../../hanzi-writer/src/renderers/canvas/canvasUtils.ts", "../../hanzi-writer/src/renderers/canvas/StrokeRenderer.ts", "../../hanzi-writer/src/renderers/canvas/CharacterRenderer.ts", "../../hanzi-writer/src/renderers/canvas/renderUserStroke.ts", "../../hanzi-writer/src/renderers/canvas/HanziWriterRenderer.ts", "../../hanzi-writer/src/renderers/canvas/RenderTarget.ts", "../../hanzi-writer/src/renderers/canvas/index.ts", "../../hanzi-writer/src/defaultCharDataLoader.ts", "../../hanzi-writer/src/defaultOptions.ts", "../../hanzi-writer/src/LoadingManager.ts", "../../hanzi-writer/src/HanziWriter.ts"], "sourcesContent": ["import { ColorObject, RecursivePartial } from './typings/types';\n\n// hacky way to get around rollup not properly setting `global` to `window` in browser\nconst globalObj = typeof window === 'undefined' ? global : window;\n\nexport const performanceNow =\n  (globalObj.performance && (() => globalObj.performance.now())) || (() => Date.now());\nexport const requestAnimationFrame =\n  globalObj.requestAnimationFrame?.bind(globalObj) ||\n  ((callback) => setTimeout(() => callback(performanceNow()), 1000 / 60));\nexport const cancelAnimationFrame =\n  globalObj.cancelAnimationFrame?.bind(globalObj) || clearTimeout;\n\n// Object.assign polyfill, because IE :/\nexport const _assign = function (target: any, ...overrides: any[]) {\n  const overrideTarget = Object(target);\n  overrides.forEach((override) => {\n    if (override != null) {\n      for (const key in override) {\n        if (Object.prototype.hasOwnProperty.call(override, key)) {\n          overrideTarget[key] = override[key];\n        }\n      }\n    }\n  });\n  return overrideTarget;\n};\n\nexport const assign = Object.assign || _assign;\n\nexport function arrLast<TValue>(arr: Array<TValue>) {\n  return arr[arr.length - 1];\n}\n\nexport const fixIndex = (index: number, length: number) => {\n  // helper to handle negative indexes in array indices\n  if (index < 0) {\n    return length + index;\n  }\n  return index;\n};\n\nexport const selectIndex = <T>(arr: Array<T>, index: number) => {\n  // helper to select item from array at index, supporting negative indexes\n  return arr[fixIndex(index, arr.length)];\n};\n\nexport function copyAndMergeDeep<T>(base: T, override: RecursivePartial<T> | undefined) {\n  const output = { ...base };\n  for (const key in override) {\n    const baseVal = base[key];\n    const overrideVal = override[key];\n    if (baseVal === overrideVal) {\n      continue;\n    }\n    if (\n      baseVal &&\n      overrideVal &&\n      typeof baseVal === 'object' &&\n      typeof overrideVal === 'object' &&\n      !Array.isArray(overrideVal)\n    ) {\n      output[key] = copyAndMergeDeep(baseVal, overrideVal);\n    } else {\n      // @ts-ignore\n      output[key] = overrideVal;\n    }\n  }\n  return output;\n}\n\n/** basically a simplified version of lodash.get, selects a key out of an object like 'a.b' from {a: {b: 7}} */\nexport function inflate(scope: string, obj: any): any {\n  const parts = scope.split('.');\n  const final: any = {};\n  let current = final;\n  for (let i = 0; i < parts.length; i++) {\n    const cap = i === parts.length - 1 ? obj : {};\n    current[parts[i]] = cap;\n    current = cap;\n  }\n  return final;\n}\n\nlet count = 0;\n\nexport function counter() {\n  count++;\n  return count;\n}\n\nexport function average(arr: number[]) {\n  const sum = arr.reduce((acc, val) => val + acc, 0);\n  return sum / arr.length;\n}\n\nexport function timeout(duration = 0) {\n  return new Promise((resolve) => setTimeout(resolve, duration));\n}\n\nexport function colorStringToVals(colorString: string): ColorObject {\n  const normalizedColor = colorString.toUpperCase().trim();\n  // based on https://stackoverflow.com/a/21648508\n  if (/^#([A-F0-9]{3}){1,2}$/.test(normalizedColor)) {\n    let hexParts = normalizedColor.substring(1).split('');\n    if (hexParts.length === 3) {\n      hexParts = [\n        hexParts[0],\n        hexParts[0],\n        hexParts[1],\n        hexParts[1],\n        hexParts[2],\n        hexParts[2],\n      ];\n    }\n    const hexStr = `${hexParts.join('')}`;\n    return {\n      r: parseInt(hexStr.slice(0, 2), 16),\n      g: parseInt(hexStr.slice(2, 4), 16),\n      b: parseInt(hexStr.slice(4, 6), 16),\n      a: 1,\n    };\n  }\n  const rgbMatch = normalizedColor.match(\n    /^RGBA?\\((\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)(?:\\s*,\\s*(\\d*\\.?\\d+))?\\)$/,\n  );\n  if (rgbMatch) {\n    return {\n      r: parseInt(rgbMatch[1], 10),\n      g: parseInt(rgbMatch[2], 10),\n      b: parseInt(rgbMatch[3], 10),\n      // @ts-expect-error ts-migrate(2554) FIXME: Expected 1 arguments, but got 2.\n      a: parseFloat(rgbMatch[4] || 1, 10),\n    };\n  }\n  throw new Error(`Invalid color: ${colorString}`);\n}\n\nexport const trim = (string: string) => string.replace(/^\\s+/, '').replace(/\\s+$/, '');\n\n// return a new array-like object with int keys where each key is item\n// ex: objRepeat({x: 8}, 3) === {0: {x: 8}, 1: {x: 8}, 2: {x: 8}}\nexport function objRepeat<T>(item: T, times: number) {\n  const obj: Record<number, T> = {};\n  for (let i = 0; i < times; i++) {\n    obj[i] = item;\n  }\n  return obj;\n}\n\n// similar to objRepeat, but takes in a callback which is called for each index in the object\nexport function objRepeatCb<T>(times: number, cb: (i: number) => T) {\n  const obj: Record<number, T> = {};\n  for (let i = 0; i < times; i++) {\n    obj[i] = cb(i);\n  }\n  return obj;\n}\n\nconst ua = globalObj.navigator?.userAgent || '';\n\nexport const isMsBrowser =\n  ua.indexOf('MSIE ') > 0 || ua.indexOf('Trident/') > 0 || ua.indexOf('Edge/') > 0;\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport const noop = () => {};\n", "import Character from './models/Character';\nimport { GenericMutation } from './Mutation';\nimport {\n  ColorObject,\n  OnCompleteFunction,\n  Point,\n  RecursivePartial,\n} from './typings/types';\nimport { copyAndMergeDeep, colorStringToVals, noop } from './utils';\n\nexport type StrokeRenderState = {\n  opacity: number;\n  displayPortion: number;\n};\n\nexport type CharacterRenderState = {\n  opacity: number;\n  strokes: Record<number | string, StrokeRenderState>;\n};\n\nexport type RenderStateObject = {\n  options: {\n    drawingFadeDuration: number;\n    drawingWidth: number;\n    drawingColor: ColorObject;\n    strokeColor: ColorObject;\n    outlineColor: ColorObject;\n    radicalColor: ColorObject;\n    highlightColor: ColorObject;\n  };\n  character: {\n    main: CharacterRenderState;\n    outline: CharacterRenderState;\n    highlight: CharacterRenderState;\n  };\n  userStrokes: Record<\n    string,\n    | {\n        points: Point[];\n        opacity: number;\n      }\n    | undefined\n  > | null;\n};\n\nexport type CharacterName = keyof RenderStateObject['character'];\n\ntype OnStateChangeCallback = (\n  nextState: RenderStateObject,\n  currentState: RenderStateObject,\n) => void;\n\ntype MutationChain = {\n  _isActive: boolean;\n  _index: number;\n  _resolve: OnCompleteFunction;\n  _mutations: GenericMutation[];\n  _loop: boolean | undefined;\n  _scopes: string[];\n};\n\nexport type RenderStateOptions = {\n  strokeColor: string;\n  radicalColor: string | null;\n  highlightColor: string;\n  outlineColor: string;\n  drawingColor: string;\n  drawingFadeDuration: number;\n  drawingWidth: number;\n  outlineWidth: number;\n  showCharacter: boolean;\n  showOutline: boolean;\n};\n\nexport default class RenderState {\n  _mutationChains: MutationChain[] = [];\n  _onStateChange: OnStateChangeCallback;\n\n  state: RenderStateObject;\n\n  constructor(\n    character: Character,\n    options: RenderStateOptions,\n    onStateChange: OnStateChangeCallback = noop,\n  ) {\n    this._onStateChange = onStateChange;\n\n    this.state = {\n      options: {\n        drawingFadeDuration: options.drawingFadeDuration,\n        drawingWidth: options.drawingWidth,\n        drawingColor: colorStringToVals(options.drawingColor),\n        strokeColor: colorStringToVals(options.strokeColor),\n        outlineColor: colorStringToVals(options.outlineColor),\n        radicalColor: colorStringToVals(options.radicalColor || options.strokeColor),\n        highlightColor: colorStringToVals(options.highlightColor),\n      },\n      character: {\n        main: {\n          opacity: options.showCharacter ? 1 : 0,\n          strokes: {},\n        },\n        outline: {\n          opacity: options.showOutline ? 1 : 0,\n          strokes: {},\n        },\n        highlight: {\n          opacity: 1,\n          strokes: {},\n        },\n      },\n      userStrokes: null,\n    };\n\n    for (let i = 0; i < character.strokes.length; i++) {\n      this.state.character.main.strokes[i] = {\n        opacity: 1,\n        displayPortion: 1,\n      };\n\n      this.state.character.outline.strokes[i] = {\n        opacity: 1,\n        displayPortion: 1,\n      };\n\n      this.state.character.highlight.strokes[i] = {\n        opacity: 0,\n        displayPortion: 1,\n      };\n    }\n  }\n\n  overwriteOnStateChange(onStateChange: OnStateChangeCallback) {\n    this._onStateChange = onStateChange;\n  }\n\n  updateState(stateChanges: RecursivePartial<RenderStateObject>) {\n    const nextState = copyAndMergeDeep(this.state, stateChanges);\n    this._onStateChange(nextState, this.state);\n    this.state = nextState;\n  }\n\n  run(\n    mutations: GenericMutation[],\n    options: {\n      loop?: boolean;\n    } = {},\n  ) {\n    const scopes = mutations.map((mut) => mut.scope);\n\n    this.cancelMutations(scopes);\n\n    return new Promise((resolve: OnCompleteFunction) => {\n      const mutationChain: MutationChain = {\n        _isActive: true,\n        _index: 0,\n        _resolve: resolve,\n        _mutations: mutations,\n        _loop: options.loop,\n        _scopes: scopes,\n      };\n      this._mutationChains.push(mutationChain);\n      this._run(mutationChain);\n    });\n  }\n\n  _run(mutationChain: MutationChain) {\n    if (!mutationChain._isActive) {\n      return;\n    }\n\n    const mutations = mutationChain._mutations;\n    if (mutationChain._index >= mutations.length) {\n      if (mutationChain._loop) {\n        mutationChain._index = 0; // eslint-disable-line no-param-reassign\n      } else {\n        mutationChain._isActive = false; // eslint-disable-line no-param-reassign\n        this._mutationChains = this._mutationChains.filter(\n          (chain) => chain !== mutationChain,\n        );\n        // The chain is done - resolve the promise to signal it finished successfully\n        mutationChain._resolve({ canceled: false });\n        return;\n      }\n    }\n\n    const activeMutation = mutationChain._mutations[mutationChain._index];\n\n    activeMutation.run(this).then(() => {\n      if (mutationChain._isActive) {\n        mutationChain._index++; // eslint-disable-line no-param-reassign\n        this._run(mutationChain);\n      }\n    });\n  }\n\n  _getActiveMutations() {\n    return this._mutationChains.map((chain) => chain._mutations[chain._index]);\n  }\n\n  pauseAll() {\n    this._getActiveMutations().forEach((mutation) => mutation.pause());\n  }\n\n  resumeAll() {\n    this._getActiveMutations().forEach((mutation) => mutation.resume());\n  }\n\n  cancelMutations(scopesToCancel: string[]) {\n    for (const chain of this._mutationChains) {\n      for (const chainId of chain._scopes) {\n        for (const scopeToCancel of scopesToCancel) {\n          if (chainId.startsWith(scopeToCancel) || scopeToCancel.startsWith(chainId)) {\n            this._cancelMutationChain(chain);\n          }\n        }\n      }\n    }\n  }\n\n  cancelAll() {\n    this.cancelMutations(['']);\n  }\n\n  _cancelMutationChain(mutationChain: MutationChain) {\n    mutationChain._isActive = false;\n    for (let i = mutationChain._index; i < mutationChain._mutations.length; i++) {\n      mutationChain._mutations[i].cancel(this);\n    }\n\n    mutationChain._resolve?.({ canceled: true });\n\n    this._mutationChains = this._mutationChains.filter(\n      (chain) => chain !== mutationChain,\n    );\n  }\n}\n", "import { Point } from './typings/types';\nimport { average, arrLast } from './utils';\n\nexport const subtract = (p1: Point, p2: Point) => ({ x: p1.x - p2.x, y: p1.y - p2.y });\n\nexport const magnitude = (point: Point) =>\n  Math.sqrt(Math.pow(point.x, 2) + Math.pow(point.y, 2));\n\nexport const distance = (point1: Point, point2: Point) =>\n  magnitude(subtract(point1, point2));\n\nexport const equals = (point1: Point, point2: Point) =>\n  point1.x === point2.x && point1.y === point2.y;\n\nexport const round = (point: Point, precision = 1) => {\n  const multiplier = precision * 10;\n  return {\n    x: Math.round(multiplier * point.x) / multiplier,\n    y: Math.round(multiplier * point.y) / multiplier,\n  };\n};\n\nexport const length = (points: Point[]) => {\n  let lastPoint = points[0];\n  const pointsSansFirst = points.slice(1);\n  return pointsSansFirst.reduce((acc, point) => {\n    const dist = distance(point, lastPoint);\n    lastPoint = point;\n    return acc + dist;\n  }, 0);\n};\n\nexport const cosineSimilarity = (point1: Point, point2: Point) => {\n  const rawDotProduct = point1.x * point2.x + point1.y * point2.y;\n  return rawDotProduct / magnitude(point1) / magnitude(point2);\n};\n\n/**\n * return a new point, p3, which is on the same line as p1 and p2, but distance away\n * from p2. p1, p2, p3 will always lie on the line in that order\n */\nexport const _extendPointOnLine = (p1: Point, p2: Point, dist: number) => {\n  const vect = subtract(p2, p1);\n  const norm = dist / magnitude(vect);\n  return { x: p2.x + norm * vect.x, y: p2.y + norm * vect.y };\n};\n\n/** based on http://www.kr.tuwien.ac.at/staff/eiter/et-archive/cdtr9464.pdf */\nexport const frechetDist = (curve1: Point[], curve2: Point[]) => {\n  const longCurve = curve1.length >= curve2.length ? curve1 : curve2;\n  const shortCurve = curve1.length >= curve2.length ? curve2 : curve1;\n\n  const calcVal = (\n    i: number,\n    j: number,\n    prevResultsCol: number[],\n    curResultsCol: number[],\n  ): number => {\n    if (i === 0 && j === 0) {\n      return distance(longCurve[0], shortCurve[0]);\n    }\n\n    if (i > 0 && j === 0) {\n      return Math.max(prevResultsCol[0], distance(longCurve[i], shortCurve[0]));\n    }\n\n    const lastResult = curResultsCol[curResultsCol.length - 1];\n\n    if (i === 0 && j > 0) {\n      return Math.max(lastResult, distance(longCurve[0], shortCurve[j]));\n    }\n\n    return Math.max(\n      Math.min(prevResultsCol[j], prevResultsCol[j - 1], lastResult),\n      distance(longCurve[i], shortCurve[j]),\n    );\n  };\n\n  let prevResultsCol: number[] = [];\n  for (let i = 0; i < longCurve.length; i++) {\n    const curResultsCol: number[] = [];\n    for (let j = 0; j < shortCurve.length; j++) {\n      // we only need the results from i - 1 and j - 1 to continue the calculation\n      // so we only need to hold onto the last column of calculated results\n      // prevResultsCol is results[i-1][:] in the original algorithm\n      // curResultsCol is results[i][:j-1] in the original algorithm\n      curResultsCol.push(calcVal(i, j, prevResultsCol, curResultsCol));\n    }\n    prevResultsCol = curResultsCol;\n  }\n\n  return prevResultsCol[shortCurve.length - 1];\n};\n\n/** break up long segments in the curve into smaller segments of len maxLen or smaller */\nexport const subdivideCurve = (curve: Point[], maxLen = 0.05) => {\n  const newCurve = curve.slice(0, 1);\n\n  for (const point of curve.slice(1)) {\n    const prevPoint = newCurve[newCurve.length - 1];\n    const segLen = distance(point, prevPoint);\n    if (segLen > maxLen) {\n      const numNewPoints = Math.ceil(segLen / maxLen);\n      const newSegLen = segLen / numNewPoints;\n      for (let i = 0; i < numNewPoints; i++) {\n        newCurve.push(_extendPointOnLine(point, prevPoint, -1 * newSegLen * (i + 1)));\n      }\n    } else {\n      newCurve.push(point);\n    }\n  }\n\n  return newCurve;\n};\n\n/** redraw the curve using numPoints equally spaced out along the length of the curve */\nexport const outlineCurve = (curve: Point[], numPoints = 30) => {\n  const curveLen = length(curve);\n  const segmentLen = curveLen / (numPoints - 1);\n  const outlinePoints = [curve[0]];\n  const endPoint = arrLast(curve);\n  const remainingCurvePoints = curve.slice(1);\n\n  for (let i = 0; i < numPoints - 2; i++) {\n    let lastPoint: Point = arrLast(outlinePoints);\n    let remainingDist = segmentLen;\n    let outlinePointFound = false;\n    while (!outlinePointFound) {\n      const nextPointDist = distance(lastPoint, remainingCurvePoints[0]);\n      if (nextPointDist < remainingDist) {\n        remainingDist -= nextPointDist;\n        lastPoint = remainingCurvePoints.shift()!;\n      } else {\n        const nextPoint = _extendPointOnLine(\n          lastPoint,\n          remainingCurvePoints[0],\n          remainingDist - nextPointDist,\n        );\n        outlinePoints.push(nextPoint);\n        outlinePointFound = true;\n      }\n    }\n  }\n\n  outlinePoints.push(endPoint);\n\n  return outlinePoints;\n};\n\n/** translate and scale from https://en.wikipedia.org/wiki/Procrustes_analysis */\nexport const normalizeCurve = (curve: Point[]) => {\n  const outlinedCurve = outlineCurve(curve);\n  const meanX = average(outlinedCurve.map((point) => point.x));\n  const meanY = average(outlinedCurve.map((point) => point.y));\n  const mean = { x: meanX, y: meanY };\n  const translatedCurve = outlinedCurve.map((point) => subtract(point, mean));\n  const scale = Math.sqrt(\n    average([\n      Math.pow(translatedCurve[0].x, 2) + Math.pow(translatedCurve[0].y, 2),\n      Math.pow(arrLast(translatedCurve).x, 2) + Math.pow(arrLast(translatedCurve).y, 2),\n    ]),\n  );\n  const scaledCurve = translatedCurve.map((point) => ({\n    x: point.x / scale,\n    y: point.y / scale,\n  }));\n  return subdivideCurve(scaledCurve);\n};\n\n// rotate around the origin\nexport const rotate = (curve: Point[], theta: number) => {\n  return curve.map((point) => ({\n    x: Math.cos(theta) * point.x - Math.sin(theta) * point.y,\n    y: Math.sin(theta) * point.x + Math.cos(theta) * point.y,\n  }));\n};\n\n// remove intermediate points that are on the same line as the points to either side\nexport const _filterParallelPoints = (points: Point[]) => {\n  if (points.length < 3) return points;\n  const filteredPoints = [points[0], points[1]];\n  points.slice(2).forEach((point) => {\n    const numFilteredPoints = filteredPoints.length;\n    const curVect = subtract(point, filteredPoints[numFilteredPoints - 1]);\n    const prevVect = subtract(\n      filteredPoints[numFilteredPoints - 1],\n      filteredPoints[numFilteredPoints - 2],\n    );\n    // this is the z coord of the cross-product. If this is 0 then they're parallel\n    const isParallel = curVect.y * prevVect.x - curVect.x * prevVect.y === 0;\n    if (isParallel) {\n      filteredPoints.pop();\n    }\n    filteredPoints.push(point);\n  });\n  return filteredPoints;\n};\n\nexport function getPathString(points: Point[], close = false) {\n  const start = round(points[0]);\n  const remainingPoints = points.slice(1);\n  let pathString = `M ${start.x} ${start.y}`;\n  remainingPoints.forEach((point) => {\n    const roundedPoint = round(point);\n    pathString += ` L ${roundedPoint.x} ${roundedPoint.y}`;\n  });\n  if (close) {\n    pathString += 'Z';\n  }\n  return pathString;\n}\n\n/** take points on a path and move their start point backwards by distance */\nexport const extendStart = (points: Point[], dist: number) => {\n  const filteredPoints = _filterParallelPoints(points);\n  if (filteredPoints.length < 2) return filteredPoints;\n  const p1 = filteredPoints[1];\n  const p2 = filteredPoints[0];\n  const newStart = _extendPointOnLine(p1, p2, dist);\n  const extendedPoints = filteredPoints.slice(1);\n  extendedPoints.unshift(newStart);\n  return extendedPoints;\n};\n", "import { subtract, distance, length } from '../geometry';\nimport { Point } from '../typings/types';\n\nexport default class Stroke {\n  path: string;\n  points: Point[];\n  strokeNum: number;\n  isInRadical: boolean;\n\n  constructor(path: string, points: Point[], strokeNum: number, isInRadical = false) {\n    this.path = path;\n    this.points = points;\n    this.strokeNum = strokeNum;\n    this.isInRadical = isInRadical;\n  }\n\n  getStartingPoint() {\n    return this.points[0];\n  }\n\n  getEndingPoint() {\n    return this.points[this.points.length - 1];\n  }\n\n  getLength(): number {\n    return length(this.points);\n  }\n\n  getVectors() {\n    let lastPoint = this.points[0];\n    const pointsSansFirst = this.points.slice(1);\n    return pointsSansFirst.map((point) => {\n      const vector = subtract(point, lastPoint);\n      lastPoint = point;\n      return vector;\n    });\n  }\n\n  getDistance(point: Point) {\n    const distances = this.points.map((strokePoint) => distance(strokePoint, point));\n    return Math.min(...distances);\n  }\n\n  getAverageDistance(points: Point[]) {\n    const totalDist = points.reduce((acc, point) => acc + this.getDistance(point), 0);\n    return totalDist / points.length;\n  }\n}\n", "import Stroke from './Stroke';\n\nexport default class Character {\n  symbol: string;\n  strokes: Stroke[];\n\n  constructor(symbol: string, strokes: Stroke[]) {\n    this.symbol = symbol;\n    this.strokes = strokes;\n  }\n}\n", "import Stroke from './models/Stroke';\nimport Character from './models/Character';\nimport { CharacterJson } from './typings/types';\n\nfunction generateStrokes({ radStrokes, strokes, medians }: CharacterJson) {\n  const isInRadical = (strokeNum: number) => (radStrokes?.indexOf(strokeNum) ?? -1) >= 0;\n  return strokes.map((path, index) => {\n    const points = medians[index].map((pointData) => {\n      const [x, y] = pointData;\n      return { x, y };\n    });\n    return new Stroke(path, points, index, isInRadical(index));\n  });\n}\n\nexport default function parseCharData(symbol: string, charJson: CharacterJson) {\n  const strokes = generateStrokes(charJson);\n  return new Character(symbol, strokes);\n}\n", "import { Point } from './typings/types';\n\n// All makemeahanzi characters have the same bounding box\nconst CHARACTER_BOUNDS = [\n  { x: 0, y: -124 },\n  { x: 1024, y: 900 },\n];\nconst [from, to] = CHARACTER_BOUNDS;\nconst preScaledWidth = to.x - from.x;\nconst preScaledHeight = to.y - from.y;\n\nexport type PositionerOptions = {\n  /** Default: 0 */\n  width: number;\n  /** Default: 0 */\n  height: number;\n  /** Default: 20 */\n  padding: number;\n};\n\nexport default class Positioner {\n  padding: number;\n  width: number;\n  height: number;\n  xOffset: number;\n  yOffset: number;\n  scale: number;\n\n  constructor(options: PositionerOptions) {\n    const { padding, width, height } = options;\n    this.padding = padding;\n    this.width = width;\n    this.height = height;\n\n    const effectiveWidth = width - 2 * padding;\n    const effectiveHeight = height - 2 * padding;\n    const scaleX = effectiveWidth / preScaledWidth;\n    const scaleY = effectiveHeight / preScaledHeight;\n\n    this.scale = Math.min(scaleX, scaleY);\n\n    const xCenteringBuffer = padding + (effectiveWidth - this.scale * preScaledWidth) / 2;\n    const yCenteringBuffer =\n      padding + (effectiveHeight - this.scale * preScaledHeight) / 2;\n\n    this.xOffset = -1 * from.x * this.scale + xCenteringBuffer;\n    this.yOffset = -1 * from.y * this.scale + yCenteringBuffer;\n  }\n\n  convertExternalPoint(point: Point) {\n    const x = (point.x - this.xOffset) / this.scale;\n    const y = (this.height - this.yOffset - point.y) / this.scale;\n    return { x, y };\n  }\n}\n", "import { average } from './utils';\nimport {\n  cosineSimilarity,\n  equals,\n  frechetDist,\n  distance,\n  subtract,\n  normalizeCurve,\n  rotate,\n  length,\n} from './geometry';\nimport { Point } from './typings/types';\nimport UserStroke from './models/UserStroke';\nimport Stroke from './models/Stroke';\nimport Character from './models/Character';\n\nconst COSINE_SIMILARITY_THRESHOLD = 0; // -1 to 1, smaller = more lenient\nconst START_AND_END_DIST_THRESHOLD = 250; // bigger = more lenient\nconst FRECHET_THRESHOLD = 0.4; // bigger = more lenient\nconst MIN_LEN_THRESHOLD = 0.35; // smaller = more lenient\n\nexport interface StrokeMatchResultMeta {\n  isStrokeBackwards: boolean;\n}\n\nexport interface StrokeMatchResult {\n  isMatch: boolean;\n  meta: StrokeMatchResultMeta;\n}\n\nexport default function strokeMatches(\n  userStroke: UserStroke,\n  character: Character,\n  strokeNum: number,\n  options: {\n    leniency?: number;\n    isOutlineVisible?: boolean;\n    averageDistanceThreshold?: number;\n  } = {},\n): StrokeMatchResult {\n  const strokes = character.strokes;\n  const points = stripDuplicates(userStroke.points);\n\n  if (points.length < 2) {\n    return { isMatch: false, meta: { isStrokeBackwards: false } };\n  }\n\n  const { isMatch, meta, avgDist } = getMatchData(points, strokes[strokeNum], options);\n\n  if (!isMatch) {\n    return { isMatch, meta };\n  }\n\n  // if there is a better match among strokes the user hasn't drawn yet, the user probably drew the wrong stroke\n  const laterStrokes = strokes.slice(strokeNum + 1);\n  let closestMatchDist = avgDist;\n\n  for (let i = 0; i < laterStrokes.length; i++) {\n    const { isMatch, avgDist } = getMatchData(points, laterStrokes[i], {\n      ...options,\n      checkBackwards: false,\n    });\n    if (isMatch && avgDist < closestMatchDist) {\n      closestMatchDist = avgDist;\n    }\n  }\n  // if there's a better match, rather that returning false automatically, try reducing leniency instead\n  // if leniency is already really high we can allow some similar strokes to pass\n  if (closestMatchDist < avgDist) {\n    // adjust leniency between 0.3 and 0.6 depending on how much of a better match the new match is\n    const leniencyAdjustment = (0.6 * (closestMatchDist + avgDist)) / (2 * avgDist);\n    const { isMatch, meta } = getMatchData(points, strokes[strokeNum], {\n      ...options,\n      leniency: (options.leniency || 1) * leniencyAdjustment,\n    });\n    return { isMatch, meta };\n  }\n\n  return { isMatch, meta };\n}\n\nconst startAndEndMatches = (points: Point[], closestStroke: Stroke, leniency: number) => {\n  const startingDist = distance(closestStroke.getStartingPoint(), points[0]);\n  const endingDist = distance(closestStroke.getEndingPoint(), points[points.length - 1]);\n  return (\n    startingDist <= START_AND_END_DIST_THRESHOLD * leniency &&\n    endingDist <= START_AND_END_DIST_THRESHOLD * leniency\n  );\n};\n\n// returns a list of the direction of all segments in the line connecting the points\nconst getEdgeVectors = (points: Point[]) => {\n  const vectors: Point[] = [];\n  let lastPoint = points[0];\n  points.slice(1).forEach((point) => {\n    vectors.push(subtract(point, lastPoint));\n    lastPoint = point;\n  });\n  return vectors;\n};\n\nconst directionMatches = (points: Point[], stroke: Stroke) => {\n  const edgeVectors = getEdgeVectors(points);\n  const strokeVectors = stroke.getVectors();\n  const similarities = edgeVectors.map((edgeVector) => {\n    const strokeSimilarities = strokeVectors.map((strokeVector) =>\n      cosineSimilarity(strokeVector, edgeVector),\n    );\n    return Math.max(...strokeSimilarities);\n  });\n  const avgSimilarity = average(similarities);\n  return avgSimilarity > COSINE_SIMILARITY_THRESHOLD;\n};\n\nconst lengthMatches = (points: Point[], stroke: Stroke, leniency: number) => {\n  return (\n    (leniency * (length(points) + 25)) / (stroke.getLength() + 25) >= MIN_LEN_THRESHOLD\n  );\n};\n\nconst stripDuplicates = (points: Point[]) => {\n  if (points.length < 2) return points;\n  const [firstPoint, ...rest] = points;\n  const dedupedPoints = [firstPoint];\n\n  for (const point of rest) {\n    if (!equals(point, dedupedPoints[dedupedPoints.length - 1])) {\n      dedupedPoints.push(point);\n    }\n  }\n\n  return dedupedPoints;\n};\n\nconst SHAPE_FIT_ROTATIONS = [\n  Math.PI / 16,\n  Math.PI / 32,\n  0,\n  (-1 * Math.PI) / 32,\n  (-1 * Math.PI) / 16,\n];\n\nconst shapeFit = (curve1: Point[], curve2: Point[], leniency: number) => {\n  const normCurve1 = normalizeCurve(curve1);\n  const normCurve2 = normalizeCurve(curve2);\n  let minDist = Infinity;\n  SHAPE_FIT_ROTATIONS.forEach((theta) => {\n    const dist = frechetDist(normCurve1, rotate(normCurve2, theta));\n    if (dist < minDist) {\n      minDist = dist;\n    }\n  });\n  return minDist <= FRECHET_THRESHOLD * leniency;\n};\n\nconst getMatchData = (\n  points: Point[],\n  stroke: Stroke,\n  options: {\n    leniency?: number;\n    isOutlineVisible?: boolean;\n    checkBackwards?: boolean;\n    averageDistanceThreshold?: number;\n  },\n): StrokeMatchResult & { avgDist: number } => {\n  const {\n    leniency = 1,\n    isOutlineVisible = false,\n    checkBackwards = true,\n    averageDistanceThreshold = 350,\n  } = options;\n  const avgDist = stroke.getAverageDistance(points);\n  const distMod = isOutlineVisible || stroke.strokeNum > 0 ? 0.5 : 1;\n  const withinDistThresh = avgDist <= averageDistanceThreshold * distMod * leniency;\n  // short circuit for faster matching\n  if (!withinDistThresh) {\n    return { isMatch: false, avgDist, meta: { isStrokeBackwards: false } };\n  }\n  const startAndEndMatch = startAndEndMatches(points, stroke, leniency);\n  const directionMatch = directionMatches(points, stroke);\n  const shapeMatch = shapeFit(points, stroke.points, leniency);\n  const lengthMatch = lengthMatches(points, stroke, leniency);\n\n  const isMatch =\n    withinDistThresh && startAndEndMatch && directionMatch && shapeMatch && lengthMatch;\n\n  if (checkBackwards && !isMatch) {\n    const backwardsMatchData = getMatchData([...points].reverse(), stroke, {\n      ...options,\n      checkBackwards: false,\n    });\n\n    if (backwardsMatchData.isMatch) {\n      return {\n        isMatch,\n        avgDist,\n        meta: { isStrokeBackwards: true },\n      };\n    }\n  }\n\n  return { isMatch, avgDist, meta: { isStrokeBackwards: false } };\n};\n", "import { Point } from '../typings/types';\n\nexport default class UserStroke {\n  id: number;\n  points: Point[];\n  externalPoints: Point[];\n\n  constructor(id: number, startingPoint: Point, startingExternalPoint: Point) {\n    this.id = id;\n    this.points = [startingPoint];\n    this.externalPoints = [startingExternalPoint];\n  }\n\n  appendPoint(point: Point, externalPoint: Point) {\n    this.points.push(point);\n    this.externalPoints.push(externalPoint);\n  }\n}\n", "import {\n  cancelAnimation<PERSON>rame,\n  requestAnimationFrame,\n  inflate,\n  performanceNow,\n} from './utils';\nimport RenderState from './RenderState';\nimport { RecursivePartial } from './typings/types';\n\n/** Used by `Mutation` & `Delay` */\nexport interface GenericMutation<\n  TRenderStateClass extends GenericRenderStateClass = RenderState\n> {\n  /** Allows mutations starting with the provided string to be cancelled */\n  scope: string;\n  /** Can be useful for checking whether the mutation is running */\n  _runningPromise: Promise<void> | undefined;\n  run(renderState: TRenderStateClass): Promise<void>;\n  pause(): void;\n  resume(): void;\n  cancel(renderState: TRenderStateClass): void;\n}\n\nclass Delay implements GenericMutation {\n  scope: string;\n  _runningPromise: Promise<void> | undefined;\n  _duration: number;\n  _startTime: number | null;\n  _paused: boolean;\n  _timeout!: NodeJS.Timeout;\n  _resolve: (() => void) | undefined;\n\n  constructor(duration: number) {\n    this._duration = duration;\n    this._startTime = null;\n    this._paused = false;\n    this.scope = `delay.${duration}`;\n  }\n\n  run() {\n    this._startTime = performanceNow();\n    this._runningPromise = new Promise((resolve) => {\n      this._resolve = resolve;\n      // @ts-ignore return type of \"setTimeout\" in builds is parsed as `number` instead of `Timeout`\n      this._timeout = setTimeout(() => this.cancel(), this._duration);\n    }) as Promise<void>;\n    return this._runningPromise;\n  }\n\n  pause() {\n    if (this._paused) return;\n    // to pause, clear the timeout and rewrite this._duration with whatever time is remaining\n    const elapsedDelay = performance.now() - (this._startTime || 0);\n    this._duration = Math.max(0, this._duration - elapsedDelay);\n    clearTimeout(this._timeout);\n    this._paused = true;\n  }\n\n  resume() {\n    if (!this._paused) return;\n    this._startTime = performance.now();\n    // @ts-ignore return type of \"setTimeout\" in builds is parsed as `number` instead of `Timeout`\n    this._timeout = setTimeout(() => this.cancel(), this._duration);\n    this._paused = false;\n  }\n\n  cancel() {\n    clearTimeout(this._timeout!);\n    if (this._resolve) {\n      this._resolve();\n    }\n    this._resolve = undefined;\n  }\n}\n\ntype GenericRenderStateClass<T = any> = {\n  state: T;\n  updateState(changes: RecursivePartial<T>): void;\n};\n\nexport default class Mutation<\n  TRenderStateClass extends GenericRenderStateClass,\n  TRenderStateObj = TRenderStateClass['state']\n> implements GenericMutation<TRenderStateClass> {\n  static Delay = Delay;\n\n  scope: string;\n  _runningPromise: Promise<void> | undefined;\n  _valuesOrCallable: any;\n  _duration: number;\n  _force: boolean | undefined;\n  _pausedDuration: number;\n  _startPauseTime: number | null;\n\n  // Only set on .run()\n  _startTime: number | undefined;\n  _startState: RecursivePartial<TRenderStateObj> | undefined;\n  _renderState: TRenderStateClass | undefined;\n  _frameHandle: number | undefined;\n  _values: RecursivePartial<TRenderStateObj> | undefined;\n  _resolve: ((_val?: any) => void) | undefined;\n\n  /**\n   *\n   * @param scope a string representation of what fields this mutation affects from the state. This is used to cancel conflicting mutations\n   * @param valuesOrCallable a thunk containing the value to set, or a callback which will return those values\n   */\n  constructor(\n    scope: string,\n    valuesOrCallable: any,\n    options: {\n      duration?: number;\n      /** Updates render state regardless if cancelled */\n      force?: boolean;\n    } = {},\n  ) {\n    this.scope = scope;\n    this._valuesOrCallable = valuesOrCallable;\n    this._duration = options.duration || 0;\n    this._force = options.force;\n    this._pausedDuration = 0;\n    this._startPauseTime = null;\n  }\n\n  run(renderState: TRenderStateClass): Promise<void> {\n    if (!this._values) this._inflateValues(renderState);\n    if (this._duration === 0) renderState.updateState(this._values!);\n    if (this._duration === 0 || isAlreadyAtEnd(renderState.state, this._values)) {\n      return Promise.resolve();\n    }\n    this._renderState = renderState;\n    this._startState = renderState.state;\n    this._startTime = performance.now();\n    this._frameHandle = requestAnimationFrame(this._tick);\n    return new Promise((resolve) => {\n      this._resolve = resolve;\n    });\n  }\n\n  private _inflateValues(renderState: TRenderStateClass) {\n    let values = this._valuesOrCallable;\n    if (typeof this._valuesOrCallable === 'function') {\n      values = this._valuesOrCallable(renderState.state);\n    }\n    this._values = inflate(this.scope, values);\n  }\n\n  pause() {\n    if (this._startPauseTime !== null) {\n      return;\n    }\n    if (this._frameHandle) {\n      cancelAnimationFrame(this._frameHandle);\n    }\n    this._startPauseTime = performance.now();\n  }\n\n  resume() {\n    if (this._startPauseTime === null) {\n      return;\n    }\n    this._frameHandle = requestAnimationFrame(this._tick);\n    this._pausedDuration += performance.now() - this._startPauseTime;\n    this._startPauseTime = null;\n  }\n\n  private _tick = (timing: number) => {\n    if (this._startPauseTime !== null) {\n      return;\n    }\n\n    const progress = Math.min(\n      1,\n      (timing - this._startTime! - this._pausedDuration) / this._duration,\n    );\n\n    if (progress === 1) {\n      this._renderState!.updateState(this._values!);\n      this._frameHandle = undefined;\n      this.cancel(this._renderState!);\n    } else {\n      const easedProgress = ease(progress);\n      const stateChanges = getPartialValues(\n        this._startState as TRenderStateObj,\n        this._values!,\n        easedProgress,\n      );\n\n      this._renderState!.updateState(stateChanges);\n      this._frameHandle = requestAnimationFrame(this._tick);\n    }\n  };\n\n  cancel(renderState: TRenderStateClass) {\n    this._resolve?.();\n    this._resolve = undefined;\n\n    cancelAnimationFrame(this._frameHandle || -1);\n    this._frameHandle = undefined;\n\n    if (this._force) {\n      if (!this._values) this._inflateValues(renderState);\n      renderState.updateState(this._values!);\n    }\n  }\n}\n\nfunction getPartialValues<T>(\n  startValues: T | undefined,\n  endValues: RecursivePartial<T> | undefined,\n  progress: number,\n) {\n  const target: RecursivePartial<T> = {};\n\n  for (const key in endValues) {\n    const endValue = endValues[key];\n    const startValue = startValues?.[key];\n    if (typeof startValue === 'number' && typeof endValue === 'number' && endValue >= 0) {\n      target[key] = progress * (endValue - startValue) + startValue;\n    } else {\n      target[key] = getPartialValues(startValue, endValue, progress);\n    }\n  }\n  return target;\n}\n\nfunction isAlreadyAtEnd<T>(\n  startValues: T | undefined,\n  endValues: RecursivePartial<T> | undefined,\n) {\n  for (const key in endValues) {\n    const endValue = endValues[key];\n    const startValue = startValues?.[key];\n    if (endValue >= 0) {\n      if (endValue !== startValue) {\n        return false;\n      }\n    } else if (!isAlreadyAtEnd(startValue, endValue)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// from https://github.com/maxwellito/vivus\nconst ease = (x: number) => -Math.cos(x * Math.PI) / 2 + 0.5;\n", "import Stroke from './models/Stroke';\nimport { ColorObject, RecursivePartial } from './typings/types';\nimport Character from './models/Character';\nimport Mutation, { GenericMutation } from './Mutation';\nimport { objRepeat } from './utils';\nimport { CharacterName, CharacterRenderState, RenderStateObject } from './RenderState';\n\nexport const showStrokes = (\n  charName: CharacterName,\n  character: Character,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}.strokes`,\n      objRepeat(\n        { opacity: 1, displayPortion: 1 },\n        character.strokes.length,\n      ) as CharacterRenderState['strokes'],\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const showCharacter = (\n  charName: CharacterName,\n  character: Character,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}`,\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 1, displayPortion: 1 }, character.strokes.length),\n      },\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const hideCharacter = (\n  charName: CharacterName,\n  character: Character,\n  duration?: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(`character.${charName}.opacity`, 0, { duration, force: true }),\n    ...showStrokes(charName, character, 0),\n  ];\n};\n\nexport const updateColor = (\n  colorName: string,\n  colorVal: ColorObject | null,\n  duration: number,\n) => {\n  return [new Mutation(`options.${colorName}`, colorVal, { duration })];\n};\n\nexport const highlightStroke = (\n  stroke: Stroke,\n  color: ColorObject | null,\n  speed: number,\n): GenericMutation[] => {\n  const strokeNum = stroke.strokeNum;\n  const duration = (stroke.getLength() + 600) / (3 * speed);\n  return [\n    new Mutation('options.highlightColor', color),\n    new Mutation('character.highlight', {\n      opacity: 1,\n      strokes: {\n        [strokeNum]: {\n          displayPortion: 0,\n          opacity: 0,\n        },\n      },\n    }),\n    new Mutation(\n      `character.highlight.strokes.${strokeNum}`,\n      {\n        displayPortion: 1,\n        opacity: 1,\n      },\n      { duration },\n    ),\n    new Mutation(`character.highlight.strokes.${strokeNum}.opacity`, 0, {\n      duration,\n      force: true,\n    }),\n  ];\n};\n\nexport const animateStroke = (\n  charName: CharacterName,\n  stroke: Stroke,\n  speed: number,\n): GenericMutation[] => {\n  const strokeNum = stroke.strokeNum;\n  const duration = (stroke.getLength() + 600) / (3 * speed);\n  return [\n    new Mutation(`character.${charName}`, {\n      opacity: 1,\n      strokes: {\n        [strokeNum]: {\n          displayPortion: 0,\n          opacity: 1,\n        },\n      },\n    }),\n    new Mutation(`character.${charName}.strokes.${strokeNum}.displayPortion`, 1, {\n      duration,\n    }),\n  ];\n};\n\nexport const animateSingleStroke = (\n  charName: CharacterName,\n  character: Character,\n  strokeNum: number,\n  speed: number,\n): GenericMutation[] => {\n  const mutationStateFunc = (state: RenderStateObject) => {\n    const curCharState = state.character[charName];\n    const mutationState: RecursivePartial<CharacterRenderState> = {\n      opacity: 1,\n      strokes: {},\n    };\n    for (let i = 0; i < character.strokes.length; i++) {\n      mutationState.strokes![i] = {\n        opacity: curCharState.opacity * curCharState.strokes[i].opacity,\n      };\n    }\n    return mutationState;\n  };\n  const stroke = character.strokes[strokeNum];\n  return [\n    new Mutation(`character.${charName}`, mutationStateFunc),\n    ...animateStroke(charName, stroke, speed),\n  ];\n};\n\nexport const showStroke = (\n  charName: CharacterName,\n  strokeNum: number,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(\n      `character.${charName}.strokes.${strokeNum}`,\n      {\n        displayPortion: 1,\n        opacity: 1,\n      },\n      { duration, force: true },\n    ),\n  ];\n};\n\nexport const animateCharacter = (\n  charName: CharacterName,\n  character: Character,\n  fadeDuration: number,\n  speed: number,\n  delayBetweenStrokes: number,\n): GenericMutation[] => {\n  let mutations: GenericMutation[] = hideCharacter(charName, character, fadeDuration);\n  mutations = mutations.concat(showStrokes(charName, character, 0));\n  mutations.push(\n    new Mutation(\n      `character.${charName}`,\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 0 }, character.strokes.length),\n      },\n      { force: true },\n    ),\n  );\n  character.strokes.forEach((stroke, i) => {\n    if (i > 0) mutations.push(new Mutation.Delay(delayBetweenStrokes));\n    mutations = mutations.concat(animateStroke(charName, stroke, speed));\n  });\n  return mutations;\n};\n\nexport const animateCharacterLoop = (\n  charName: CharacterName,\n  character: Character,\n  fadeDuration: number,\n  speed: number,\n  delayBetweenStrokes: number,\n  delayBetweenLoops: number,\n): GenericMutation[] => {\n  const mutations = animateCharacter(\n    charName,\n    character,\n    fadeDuration,\n    speed,\n    delayBetweenStrokes,\n  );\n  mutations.push(new Mutation.Delay(delayBetweenLoops));\n  return mutations;\n};\n", "import Mutation, { GenericMutation } from './Mutation';\nimport * as characterActions from './characterActions';\nimport { objRepeat, objRepeatCb } from './utils';\nimport Character from './models/Character';\nimport { ColorObject, Point } from './typings/types';\n\nexport const startQuiz = (\n  character: Character,\n  fadeDuration: number,\n  startStrokeNum: number,\n): GenericMutation[] => {\n  return [\n    ...characterActions.hideCharacter('main', character, fadeDuration),\n    new Mutation(\n      'character.highlight',\n      {\n        opacity: 1,\n        strokes: objRepeat({ opacity: 0 }, character.strokes.length),\n      },\n      { force: true },\n    ),\n    new Mutation(\n      'character.main',\n      {\n        opacity: 1,\n        strokes: objRepeatCb(character.strokes.length, (i) => ({\n          opacity: i < startStrokeNum ? 1 : 0,\n        })),\n      },\n      { force: true },\n    ),\n  ];\n};\n\nexport const startUserStroke = (id: string | number, point: Point): GenericMutation[] => {\n  return [\n    new Mutation('quiz.activeUserStrokeId', id, { force: true }),\n    new Mutation(\n      `userStrokes.${id}`,\n      {\n        points: [point],\n        opacity: 1,\n      },\n      { force: true },\n    ),\n  ];\n};\n\nexport const updateUserStroke = (\n  userStrokeId: string | number,\n  points: Point[],\n): GenericMutation[] => {\n  return [new Mutation(`userStrokes.${userStrokeId}.points`, points, { force: true })];\n};\n\nexport const hideUserStroke = (\n  userStrokeId: string | number,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation(`userStrokes.${userStrokeId}.opacity`, 0, { duration }),\n    // Do not remove the stroke, keep it hidden until quiz ends\n    // This avoids a bug in which touchmove stops being triggered in the middle of a stroke\n    // the only doc i found https://stackoverflow.com/questions/29384973/touchmove-event-stops-triggering-after-any-element-is-removed-from-dom\n    // so if the user on his phone is too quick to start his new stroke, the new stroke may stops in mid air\n    //new Mutation(`userStrokes.${userStrokeId}`, null, { force: true }),\n  ];\n};\n\nexport const removeAllUserStrokes = (userStrokeIds: Array<number>): GenericMutation[] => {\n  return userStrokeIds?.map(userStrokeId =>\n    new Mutation(`userStrokes.${userStrokeId}`, null, { force: true })\n  ) || [];\n};\n\nexport const highlightCompleteChar = (\n  character: Character,\n  color: ColorObject | null,\n  duration: number,\n): GenericMutation[] => {\n  return [\n    new Mutation('options.highlightColor', color),\n    ...characterActions.hideCharacter('highlight', character),\n    ...characterActions.showCharacter('highlight', character, duration / 2),\n    ...characterActions.hideCharacter('highlight', character, duration / 2),\n  ];\n};\n\nexport const highlightStroke = characterActions.highlightStroke;\n", "import strokeMatches, { StrokeMatchResultMeta } from './strokeMatches';\nimport UserStroke from './models/UserStroke';\nimport Positioner from './Positioner';\nimport { counter, colorStringToVals, fixIndex } from './utils';\nimport * as quizActions from './quizActions';\nimport * as geometry from './geometry';\nimport * as characterActions from './characterActions';\nimport Character from './models/Character';\nimport { ParsedHanziWriterOptions, Point, StrokeData } from './typings/types';\nimport RenderState from './RenderState';\nimport { GenericMutation } from './Mutation';\n\nconst getDrawnPath = (userStroke: UserStroke) => ({\n  pathString: geometry.getPathString(userStroke.externalPoints),\n  points: userStroke.points.map((point) => geometry.round(point)),\n});\n\nexport default class Quiz {\n  _character: Character;\n  _renderState: RenderState;\n  _isActive: boolean;\n  _positioner: Positioner;\n\n  /** Set on startQuiz */\n  _options: ParsedHanziWriterOptions | undefined;\n  _currentStrokeIndex = 0;\n  _mistakesOnStroke = 0;\n  _totalMistakes = 0;\n  _userStroke: UserStroke | undefined;\n  _userStrokesIds: Array<number> | undefined;\n\n  constructor(character: Character, renderState: RenderState, positioner: Positioner) {\n    this._character = character;\n    this._renderState = renderState;\n    this._isActive = false;\n    this._positioner = positioner;\n  }\n\n  startQuiz(options: ParsedHanziWriterOptions) {\n    if (this._userStrokesIds) {\n      this._renderState.run(\n        quizActions.removeAllUserStrokes( this._userStrokesIds ),\n      );\n    }\n    this._userStrokesIds = []\n\n    this._isActive = true;\n    this._options = options;\n    const startIndex = fixIndex(\n      options.quizStartStrokeNum,\n      this._character.strokes.length,\n    );\n    this._currentStrokeIndex = Math.min(startIndex, this._character.strokes.length - 1);\n    this._mistakesOnStroke = 0;\n    this._totalMistakes = 0;\n\n    return this._renderState.run(\n      quizActions.startQuiz(\n        this._character,\n        options.strokeFadeDuration,\n        this._currentStrokeIndex,\n      ),\n    );\n  }\n\n  startUserStroke(externalPoint: Point) {\n    if (!this._isActive) {\n      return null;\n    }\n    if (this._userStroke) {\n      return this.endUserStroke();\n    }\n    const point = this._positioner.convertExternalPoint(externalPoint);\n    const strokeId = counter();\n    this._userStroke = new UserStroke(strokeId, point, externalPoint);\n    this._userStrokesIds?.push(strokeId)\n    return this._renderState.run(quizActions.startUserStroke(strokeId, point));\n  }\n\n  continueUserStroke(externalPoint: Point) {\n    if (!this._userStroke) {\n      return Promise.resolve();\n    }\n    const point = this._positioner.convertExternalPoint(externalPoint);\n    this._userStroke.appendPoint(point, externalPoint);\n    const nextPoints = this._userStroke.points.slice(0);\n    return this._renderState.run(\n      quizActions.updateUserStroke(this._userStroke.id, nextPoints),\n    );\n  }\n\n  setPositioner(positioner: Positioner) {\n    this._positioner = positioner;\n  }\n\n  endUserStroke() {\n    if (!this._userStroke) return;\n\n    this._renderState.run(\n      quizActions.hideUserStroke(\n        this._userStroke.id,\n        this._options!.drawingFadeDuration ?? 300,\n      ),\n    );\n\n    // skip single-point strokes\n    if (this._userStroke.points.length === 1) {\n      this._userStroke = undefined;\n      return;\n    }\n\n    const { acceptBackwardsStrokes, markStrokeCorrectAfterMisses } = this._options!;\n\n    const currentStroke = this._getCurrentStroke();\n    const { isMatch, meta } = strokeMatches(\n      this._userStroke,\n      this._character,\n      this._currentStrokeIndex,\n      {\n        isOutlineVisible: this._renderState.state.character.outline.opacity > 0,\n        leniency: this._options!.leniency,\n        averageDistanceThreshold: this._options!.averageDistanceThreshold,\n      },\n    );\n\n    // if markStrokeCorrectAfterMisses is passed, just force the stroke to count as correct after n tries\n    const isForceAccepted =\n      markStrokeCorrectAfterMisses &&\n      this._mistakesOnStroke + 1 >= markStrokeCorrectAfterMisses;\n\n    const isAccepted =\n      isMatch || isForceAccepted || (meta.isStrokeBackwards && acceptBackwardsStrokes);\n\n    if (isAccepted) {\n      this._handleSuccess(meta);\n    } else {\n      this._handleFailure(meta);\n\n      const {\n        showHintAfterMisses,\n        highlightColor,\n        strokeHighlightSpeed,\n      } = this._options!;\n\n      if (\n        showHintAfterMisses !== false &&\n        this._mistakesOnStroke >= showHintAfterMisses\n      ) {\n        this._renderState.run(\n          characterActions.highlightStroke(\n            currentStroke,\n            colorStringToVals(highlightColor),\n            strokeHighlightSpeed,\n          ),\n        );\n      }\n    }\n\n    this._userStroke = undefined;\n  }\n\n  cancel() {\n    this._isActive = false;\n    if (this._userStrokesIds) {\n      this._renderState.run(\n        quizActions.removeAllUserStrokes( this._userStrokesIds ),\n      );\n    }\n  }\n\n  _getStrokeData({\n    isCorrect,\n    meta,\n  }: {\n    isCorrect: boolean;\n    meta: StrokeMatchResultMeta;\n  }): StrokeData {\n    return {\n      character: this._character.symbol,\n      strokeNum: this._currentStrokeIndex,\n      mistakesOnStroke: this._mistakesOnStroke,\n      totalMistakes: this._totalMistakes,\n      strokesRemaining:\n        this._character.strokes.length - this._currentStrokeIndex - (isCorrect ? 1 : 0),\n      drawnPath: getDrawnPath(this._userStroke!),\n      isBackwards: meta.isStrokeBackwards,\n    };\n  }\n\n  nextStroke() {\n    if (!this._options) return;\n\n    const { strokes, symbol } = this._character;\n\n    const {\n      onComplete,\n      highlightOnComplete,\n      strokeFadeDuration,\n      highlightCompleteColor,\n      highlightColor,\n      strokeHighlightDuration,\n    } = this._options;\n\n    let animation: GenericMutation[] = characterActions.showStroke(\n      'main',\n      this._currentStrokeIndex,\n      strokeFadeDuration,\n    );\n\n    this._mistakesOnStroke = 0;\n    this._currentStrokeIndex += 1;\n\n    const isComplete = this._currentStrokeIndex === strokes.length;\n\n    if (isComplete) {\n      this._isActive = false;\n      onComplete?.({\n        character: symbol,\n        totalMistakes: this._totalMistakes,\n      });\n      if (highlightOnComplete) {\n        animation = animation.concat(\n          quizActions.highlightCompleteChar(\n            this._character,\n            colorStringToVals(highlightCompleteColor || highlightColor),\n            (strokeHighlightDuration || 0) * 2,\n          ),\n        );\n      }\n    }\n\n    this._renderState.run(animation);\n  }\n\n  _handleSuccess(meta: StrokeMatchResultMeta) {\n    if (!this._options) return;\n\n    const { onCorrectStroke } = this._options;\n\n    onCorrectStroke?.({\n      ...this._getStrokeData({ isCorrect: true, meta }),\n    });\n\n    this.nextStroke();\n  }\n\n  _handleFailure(meta: StrokeMatchResultMeta) {\n    this._mistakesOnStroke += 1;\n    this._totalMistakes += 1;\n    this._options!.onMistake?.(this._getStrokeData({ isCorrect: false, meta }));\n  }\n\n  _getCurrentStroke() {\n    return this._character.strokes[this._currentStrokeIndex];\n  }\n}\n", "export function createElm(elmType: string) {\n  return document.createElementNS('http://www.w3.org/2000/svg', elmType);\n}\n\nexport function attr(elm: Element, name: string, value: string) {\n  elm.setAttributeNS(null, name, value);\n}\n\nexport function attrs(elm: Element, attrsMap: Record<string, string>) {\n  Object.keys(attrsMap).forEach((attrName) => attr(elm, attrName, attrsMap[attrName]));\n}\n\n// inspired by https://talk.observablehq.com/t/hanzi-writer-renders-incorrectly-inside-an-observable-notebook-on-a-mobile-browser/1898\nexport function urlIdRef(id: string) {\n  let prefix = '';\n  if (window.location && window.location.href) {\n    prefix = window.location.href.replace(/#[^#]*$/, '').replace(/\"/gi, '%22');\n  }\n  return `url(\"${prefix}#${id}\")`;\n}\n\nexport function removeElm(elm: Element | undefined) {\n  elm?.parentNode?.removeChild(elm);\n}\n", "import Stroke from '../models/Stroke';\nimport { ColorObject } from '../typings/types';\n\nexport default class StrokeRendererBase {\n  _pathLength: number;\n  stroke: Stroke;\n  static STROKE_WIDTH = 200;\n\n  constructor(stroke: Stroke) {\n    this.stroke = stroke;\n    this._pathLength = stroke.getLength() + StrokeRendererBase.STROKE_WIDTH / 2;\n  }\n\n  _getStrokeDashoffset(displayPortion: number) {\n    return this._pathLength * 0.999 * (1 - displayPortion);\n  }\n\n  _getColor({\n    strokeColor,\n    radicalColor,\n  }: {\n    strokeColor: ColorObject;\n    radicalColor?: ColorObject | null;\n  }) {\n    return radicalColor && this.stroke.isInRadical ? radicalColor : strokeColor;\n  }\n}\n", "import { counter } from '../../utils';\nimport * as svg from './svgUtils';\nimport { extendStart, getPathString } from '../../geometry';\nimport StrokeRendererBase from '../StrokeRendererBase';\nimport Stroke from '../../models/Stroke';\nimport SVGRenderTarget from './RenderTarget';\nimport { ColorObject } from '../../typings/types';\n\nconst STROKE_WIDTH = 200;\n\ntype StrokeRenderProps = {\n  strokeColor: ColorObject;\n  radicalColor: ColorObject | null;\n  displayPortion: number;\n  opacity: number;\n};\n\n/** This is a stroke composed of several stroke parts **/\nexport default class StrokeRenderer extends StrokeRendererBase {\n  _oldProps: StrokeRenderProps | undefined = undefined;\n\n  _animationPath: SVGPathElement | undefined;\n  _clip: SVGClipPathElement | undefined;\n  _strokePath: SVGPathElement | undefined;\n\n  constructor(stroke: Stroke) {\n    super(stroke);\n  }\n\n  mount(target: SVGRenderTarget) {\n    this._animationPath = svg.createElm('path') as SVGPathElement;\n    this._clip = svg.createElm('clipPath') as SVGClipPathElement;\n    this._strokePath = svg.createElm('path') as SVGPathElement;\n    const maskId = `mask-${counter()}`;\n    svg.attr(this._clip, 'id', maskId);\n\n    svg.attr(this._strokePath, 'd', this.stroke.path);\n    this._animationPath.style.opacity = '0';\n    svg.attr(this._animationPath, 'clip-path', svg.urlIdRef(maskId));\n\n    const extendedMaskPoints = extendStart(this.stroke.points, STROKE_WIDTH / 2);\n    svg.attr(this._animationPath, 'd', getPathString(extendedMaskPoints));\n    svg.attrs(this._animationPath, {\n      stroke: '#FFFFFF',\n      'stroke-width': STROKE_WIDTH.toString(),\n      fill: 'none',\n      'stroke-linecap': 'round',\n      'stroke-linejoin': 'miter',\n      'stroke-dasharray': `${this._pathLength},${this._pathLength}`,\n    });\n\n    this._clip.appendChild(this._strokePath);\n    target.defs.appendChild(this._clip);\n    target.svg.appendChild(this._animationPath);\n    return this;\n  }\n\n  render(props: StrokeRenderProps) {\n    if (props === this._oldProps || !this._animationPath) {\n      return;\n    }\n\n    if (props.displayPortion !== this._oldProps?.displayPortion) {\n      this._animationPath.style.strokeDashoffset = this._getStrokeDashoffset(\n        props.displayPortion,\n      ).toString();\n    }\n\n    const color = this._getColor(props);\n\n    if (!this._oldProps || color !== this._getColor(this._oldProps)) {\n      const { r, g, b, a } = color;\n      svg.attrs(this._animationPath, { stroke: `rgba(${r},${g},${b},${a})` });\n    }\n\n    if (props.opacity !== this._oldProps?.opacity) {\n      this._animationPath.style.opacity = props.opacity.toString();\n    }\n    this._oldProps = props;\n  }\n}\n", "import { isMs<PERSON>rowser } from '../../utils';\nimport StrokeRenderer from './StrokeRenderer';\nimport SVGRenderTarget from './RenderTarget';\nimport Character from '../../models/Character';\nimport { ColorObject } from '../../typings/types';\nimport { StrokeRenderState } from '../../RenderState';\n\ntype SvgCharacterRenderProps = {\n  opacity: number;\n  strokes: Record<number, StrokeRenderState>;\n  strokeColor: ColorObject;\n  radicalColor?: ColorObject | null;\n};\n\nexport default class CharacterRenderer {\n  _oldProps: SvgCharacterRenderProps | undefined = undefined;\n  _strokeRenderers: StrokeRenderer[];\n\n  // set on mount()\n  _group: SVGElement | SVGSVGElement | undefined;\n\n  constructor(character: Character) {\n    this._strokeRenderers = character.strokes.map((stroke) => new StrokeRenderer(stroke));\n  }\n\n  mount(target: SVGRenderTarget) {\n    const subTarget = target.createSubRenderTarget();\n    this._group = subTarget.svg;\n    this._strokeRenderers.forEach((strokeRenderer) => {\n      strokeRenderer.mount(subTarget);\n    });\n  }\n\n  render(props: SvgCharacterRenderProps) {\n    if (props === this._oldProps || !this._group) {\n      return;\n    }\n    const { opacity, strokes, strokeColor, radicalColor = null } = props;\n    if (opacity !== this._oldProps?.opacity) {\n      this._group.style.opacity = opacity.toString();\n      // MS browsers seem to have a bug where if SVG is set to display:none, it sometimes breaks.\n      // More info: https://github.com/chanind/hanzi-writer/issues/164\n      // this is just a perf improvement, so disable for MS browsers\n      if (!isMsBrowser) {\n        if (opacity === 0) {\n          this._group.style.display = 'none';\n        } else if (this._oldProps?.opacity === 0) {\n          this._group.style.removeProperty('display');\n        }\n      }\n    }\n    const colorsChanged =\n      !this._oldProps ||\n      strokeColor !== this._oldProps.strokeColor ||\n      radicalColor !== this._oldProps.radicalColor;\n\n    if (colorsChanged || strokes !== this._oldProps?.strokes) {\n      for (let i = 0; i < this._strokeRenderers.length; i++) {\n        if (\n          !colorsChanged &&\n          this._oldProps?.strokes &&\n          strokes[i] === this._oldProps.strokes[i]\n        ) {\n          continue;\n        }\n        this._strokeRenderers[i].render({\n          strokeColor,\n          radicalColor,\n          opacity: strokes[i].opacity,\n          displayPortion: strokes[i].displayPortion,\n        });\n      }\n    }\n    this._oldProps = props;\n  }\n}\n", "import * as svg from './svgUtils';\nimport { getPathString } from '../../geometry';\nimport { ColorObject, Point } from '../../typings/types';\nimport SVGRenderTarget from './RenderTarget';\n\nexport type UserStrokeProps = {\n  strokeWidth: number;\n  strokeColor: ColorObject;\n  opacity: number;\n  points: Point[];\n};\n\nexport default class UserStrokeRenderer {\n  _oldProps: UserStrokeProps | undefined = undefined;\n  _path: SVGElement | undefined;\n\n  mount(target: SVGRenderTarget) {\n    this._path = svg.createElm('path');\n    target.svg.appendChild(this._path);\n  }\n\n  render(props: UserStrokeProps) {\n    if (!this._path || props === this._oldProps) {\n      return;\n    }\n    if (\n      props.strokeColor !== this._oldProps?.strokeColor ||\n      props.strokeWidth !== this._oldProps?.strokeWidth\n    ) {\n      const { r, g, b, a } = props.strokeColor;\n      svg.attrs(this._path, {\n        fill: 'none',\n        stroke: `rgba(${r},${g},${b},${a})`,\n        'stroke-width': props.strokeWidth.toString(),\n        'stroke-linecap': 'round',\n        'stroke-linejoin': 'round',\n      });\n    }\n    if (props.opacity !== this._oldProps?.opacity) {\n      svg.attr(this._path, 'opacity', props.opacity.toString());\n    }\n    if (props.points !== this._oldProps?.points) {\n      svg.attr(this._path, 'd', getPathString(props.points));\n    }\n    this._oldProps = props;\n  }\n\n  destroy() {\n    svg.removeElm(this._path);\n  }\n}\n", "import CharacterRenderer from './CharacterRenderer';\nimport UserStrokeRenderer, { UserStrokeProps } from './UserStrokeRenderer';\nimport * as svg from './svgUtils';\nimport Character from '../../models/Character';\nimport Positioner from '../../Positioner';\nimport SVGRenderTarget from './RenderTarget';\nimport HanziWriterRendererBase from '../HanziWriterRendererBase';\nimport { RenderStateObject } from '../../RenderState';\n\nexport default class HanziWriterRenderer\n  implements HanziWriterRendererBase<SVGElement | SVGSVGElement, SVGRenderTarget> {\n  _character: Character;\n  _positioner: Positioner;\n  _mainCharRenderer: CharacterRenderer;\n  _outlineCharRenderer: CharacterRenderer;\n  _highlightCharRenderer: CharacterRenderer;\n  _userStrokeRenderers: Record<string, UserStrokeRenderer | undefined>;\n  _positionedTarget: SVGRenderTarget | undefined;\n\n  constructor(character: Character, positioner: Positioner) {\n    this._character = character;\n    this._positioner = positioner;\n    this._mainCharRenderer = new CharacterRenderer(character);\n    this._outlineCharRenderer = new CharacterRenderer(character);\n    this._highlightCharRenderer = new CharacterRenderer(character);\n    this._userStrokeRenderers = {};\n  }\n\n  mount(target: SVGRenderTarget) {\n    const positionedTarget = target.createSubRenderTarget();\n    const group = positionedTarget.svg;\n    const { xOffset, yOffset, height, scale } = this._positioner;\n\n    svg.attr(\n      group,\n      'transform',\n      `translate(${xOffset}, ${height - yOffset}) scale(${scale}, ${-1 * scale})`,\n    );\n    this._outlineCharRenderer.mount(positionedTarget);\n    this._mainCharRenderer.mount(positionedTarget);\n    this._highlightCharRenderer.mount(positionedTarget);\n    this._positionedTarget = positionedTarget;\n  }\n\n  render(props: RenderStateObject) {\n    const { main, outline, highlight } = props.character;\n    const {\n      outlineColor,\n      radicalColor,\n      highlightColor,\n      strokeColor,\n      drawingWidth,\n      drawingColor,\n    } = props.options;\n\n    this._outlineCharRenderer.render({\n      opacity: outline.opacity,\n      strokes: outline.strokes,\n      strokeColor: outlineColor,\n    });\n\n    this._mainCharRenderer.render({\n      opacity: main.opacity,\n      strokes: main.strokes,\n      strokeColor,\n      radicalColor: radicalColor,\n    });\n\n    this._highlightCharRenderer.render({\n      opacity: highlight.opacity,\n      strokes: highlight.strokes,\n      strokeColor: highlightColor,\n    });\n\n    const userStrokes = props.userStrokes || {};\n\n    for (const userStrokeId in this._userStrokeRenderers) {\n      if (!userStrokes[userStrokeId]) {\n        this._userStrokeRenderers[userStrokeId]?.destroy();\n        delete this._userStrokeRenderers[userStrokeId];\n      }\n    }\n\n    for (const userStrokeId in userStrokes) {\n      const stroke = userStrokes[userStrokeId];\n      if (!stroke) {\n        continue;\n      }\n      const userStrokeProps: UserStrokeProps = {\n        strokeWidth: drawingWidth,\n        strokeColor: drawingColor,\n        ...stroke,\n      };\n\n      const strokeRenderer = (() => {\n        if (this._userStrokeRenderers[userStrokeId]) {\n          return this._userStrokeRenderers[userStrokeId]!;\n        }\n        const newStrokeRenderer = new UserStrokeRenderer();\n        newStrokeRenderer.mount(this._positionedTarget!);\n        this._userStrokeRenderers[userStrokeId] = newStrokeRenderer;\n        return newStrokeRenderer;\n      })();\n\n      strokeRenderer.render(userStrokeProps);\n    }\n  }\n\n  destroy() {\n    svg.removeElm(this._positionedTarget!.svg);\n    this._positionedTarget!.defs.innerHTML = '';\n  }\n}\n", "import { Point } from '../typings/types';\n\ntype BoundEvent = {\n  getPoint(): Point;\n  preventDefault(): void;\n};\n\n/** Generic render target */\nexport default class RenderTargetBase<\n  TElement extends\n    | HTMLElement\n    | SVGElement\n    | SVGSVGElement\n    | HTMLCanvasElement = HTMLElement\n> {\n  node: TElement;\n\n  constructor(node: TElement) {\n    this.node = node;\n  }\n\n  addPointerStartListener(callback: (arg: BoundEvent) => void) {\n    this.node.addEventListener('mousedown', (evt) => {\n      callback(this._eventify(evt as MouseEvent, this._getMousePoint));\n    });\n    this.node.addEventListener('touchstart', (evt) => {\n      callback(this._eventify(evt as TouchEvent, this._getTouchPoint));\n    });\n  }\n\n  addPointerMoveListener(callback: (arg: BoundEvent) => void) {\n    this.node.addEventListener('mousemove', (evt) => {\n      callback(this._eventify(evt as <PERSON><PERSON><PERSON>, this._getMousePoint));\n    });\n    this.node.addEventListener('touchmove', (evt) => {\n      callback(this._eventify(evt as TouchEvent, this._getTouchPoint));\n    });\n  }\n\n  addPointerEndListener(callback: () => void) {\n    // TODO: find a way to not need global listeners\n    document.addEventListener('mouseup', callback);\n    document.addEventListener('touchend', callback);\n  }\n\n  getBoundingClientRect() {\n    return this.node.getBoundingClientRect();\n  }\n\n  updateDimensions(width: string | number, height: string | number) {\n    this.node.setAttribute('width', `${width}`);\n    this.node.setAttribute('height', `${height}`);\n  }\n\n  _eventify<TEvent extends Event>(evt: TEvent, pointFunc: (event: TEvent) => Point) {\n    return {\n      getPoint: () => pointFunc.call(this, evt),\n      preventDefault: () => evt.preventDefault(),\n    };\n  }\n\n  _getMousePoint(evt: MouseEvent): Point {\n    const { left, top } = this.getBoundingClientRect();\n    const x = evt.clientX - left;\n    const y = evt.clientY - top;\n    return { x, y };\n  }\n\n  _getTouchPoint(evt: TouchEvent): Point {\n    const { left, top } = this.getBoundingClientRect();\n    const x = evt.touches[0].clientX - left;\n    const y = evt.touches[0].clientY - top;\n    return { x, y };\n  }\n}\n", "import { createElm, attrs } from './svgUtils';\nimport RenderTargetBase from '../RenderTargetBase';\n\nexport default class RenderTarget extends RenderTargetBase<SVGSVGElement | SVGElement> {\n  static init(elmOrId: Element | string, width = '100%', height = '100%') {\n    const element = (() => {\n      if (typeof elmOrId === 'string') {\n        return document.getElementById(elmOrId);\n      }\n      return elmOrId;\n    })();\n\n    if (!element) {\n      throw new Error(`HanziWriter target element not found: ${elmOrId}`);\n    }\n    const nodeType = element.nodeName.toUpperCase();\n\n    const svg = (() => {\n      if (nodeType === 'SVG' || nodeType === 'G') {\n        return element;\n      } else {\n        const svg = createElm('svg');\n        element.appendChild(svg);\n        return svg;\n      }\n    })() as SVGSVGElement;\n\n    attrs(svg, { width, height });\n    const defs = createElm('defs');\n    svg.appendChild(defs);\n\n    return new RenderTarget(svg, defs);\n  }\n\n  svg: SVGSVGElement | SVGElement;\n  defs: SVGElement;\n  _pt: DOMPoint | undefined;\n\n  constructor(svg: SVGElement | SVGSVGElement, defs: SVGElement) {\n    super(svg);\n\n    this.svg = svg;\n    this.defs = defs;\n\n    if ('createSVGPoint' in svg) {\n      this._pt = svg.createSVGPoint();\n    }\n  }\n\n  createSubRenderTarget() {\n    const group = createElm('g');\n    this.svg.appendChild(group);\n    return new RenderTarget(group, this.defs);\n  }\n\n  _getMousePoint(evt: MouseEvent) {\n    if (this._pt) {\n      this._pt.x = evt.clientX;\n      this._pt.y = evt.clientY;\n      if ('getScreenCTM' in this.node) {\n        const localPt = this._pt.matrixTransform(this.node.getScreenCTM()?.inverse());\n        return { x: localPt.x, y: localPt.y };\n      }\n    }\n    return super._getMousePoint.call(this, evt);\n  }\n\n  _getTouchPoint(evt: TouchEvent) {\n    if (this._pt) {\n      this._pt.x = evt.touches[0].clientX;\n      this._pt.y = evt.touches[0].clientY;\n      if ('getScreenCTM' in this.node) {\n        const localPt = this._pt.matrixTransform(\n          (this.node as SVGSVGElement).getScreenCTM()?.inverse(),\n        );\n        return { x: localPt.x, y: localPt.y };\n      }\n    }\n    return super._getTouchPoint(evt);\n  }\n}\n", "import { RenderTargetInitFunction } from '../../typings/types';\nimport HanziWriterRenderer from './HanziWriterRenderer';\nimport RenderTarget from './RenderTarget';\n\nexport default {\n  HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init as RenderTargetInitFunction<\n    SVGSVGElement | SVGElement\n  >,\n};\n", "import { Point } from '../../typings/types';\n\nexport const drawPath = (ctx: CanvasRenderingContext2D, points: Point[]) => {\n  ctx.beginPath();\n  const start = points[0];\n  const remainingPoints = points.slice(1);\n  ctx.moveTo(start.x, start.y);\n  for (const point of remainingPoints) {\n    ctx.lineTo(point.x, point.y);\n  }\n  ctx.stroke();\n};\n\n/**\n * Break a path string into a series of canvas path commands\n *\n * Note: only works with the subset of SVG paths used by MakeMeAHanzi data\n * @param pathString\n */\nexport const pathStringToCanvas = (pathString: string) => {\n  const pathParts = pathString.split(/(^|\\s+)(?=[A-Z])/).filter((part) => part !== ' ');\n  const commands = [(ctx: CanvasRenderingContext2D) => ctx.beginPath()];\n  for (const part of pathParts) {\n    const [cmd, ...rawParams] = part.split(/\\s+/);\n    const params = rawParams.map((param) => parseFloat(param)) as any[];\n    if (cmd === 'M') {\n      commands.push((ctx) => ctx.moveTo(...(params as [number, number])));\n    } else if (cmd === 'L') {\n      commands.push((ctx) => ctx.lineTo(...(params as [number, number])));\n    } else if (cmd === 'C') {\n      commands.push((ctx) =>\n        ctx.bezierCurveTo(...(params as Parameters<typeof ctx.bezierCurveTo>)),\n      );\n    } else if (cmd === 'Q') {\n      commands.push((ctx) =>\n        ctx.quadraticCurveTo(...(params as Parameters<typeof ctx.quadraticCurveTo>)),\n      );\n    } else if (cmd === 'Z') {\n      // commands.push((ctx) => ctx.closePath());\n    }\n  }\n  return (ctx: CanvasRenderingContext2D) => commands.forEach((cmd) => cmd(ctx));\n};\n", "import { extendStart } from '../../geometry';\nimport { drawPath, pathStringToCanvas } from './canvasUtils';\nimport StrokeRendererBase from '../StrokeRendererBase';\nimport Stroke from '../../models/Stroke';\nimport { ColorObject, Point } from '../../typings/types';\n\n/** this is a stroke composed of several stroke parts */\nexport default class StrokeRenderer extends StrokeRendererBase {\n  _extendedMaskPoints: Point[];\n\n  // Conditionally set on constructor\n  _path2D: Path2D | undefined;\n  _pathCmd: ((ctx: CanvasRenderingContext2D) => void) | undefined;\n\n  constructor(stroke: Stroke, usePath2D = true) {\n    super(stroke);\n\n    if (usePath2D && Path2D) {\n      this._path2D = new Path2D(this.stroke.path);\n    } else {\n      this._pathCmd = pathStringToCanvas(this.stroke.path);\n    }\n    this._extendedMaskPoints = extendStart(\n      this.stroke.points,\n      StrokeRendererBase.STROKE_WIDTH / 2,\n    );\n  }\n\n  render(\n    ctx: CanvasRenderingContext2D,\n    props: {\n      opacity: number;\n      strokeColor: ColorObject;\n      radicalColor?: ColorObject | null;\n      displayPortion: number;\n    },\n  ) {\n    if (props.opacity < 0.05) {\n      return;\n    }\n    ctx.save();\n\n    if (this._path2D) {\n      ctx.clip(this._path2D);\n    } else {\n      this._pathCmd?.(ctx);\n      // wechat bugs out if the clip path isn't stroked or filled\n      ctx.globalAlpha = 0;\n      ctx.stroke();\n      ctx.clip();\n    }\n\n    const { r, g, b, a } = this._getColor(props);\n    const color = a === 1 ? `rgb(${r},${g},${b})` : `rgb(${r},${g},${b},${a})`;\n    const dashOffset = this._getStrokeDashoffset(props.displayPortion);\n    ctx.globalAlpha = props.opacity;\n    ctx.strokeStyle = color;\n    ctx.fillStyle = color;\n    ctx.lineWidth = StrokeRendererBase.STROKE_WIDTH;\n    ctx.lineCap = 'round';\n    ctx.lineJoin = 'round';\n    // wechat sets dashOffset as a second param here. Should be harmless for browsers to add here too\n    // @ts-ignore\n    ctx.setLineDash([this._pathLength, this._pathLength], dashOffset);\n    ctx.lineDashOffset = dashOffset;\n    drawPath(ctx, this._extendedMaskPoints);\n\n    ctx.restore();\n  }\n}\n", "import Character from '../../models/Character';\nimport { StrokeRenderState } from '../../RenderState';\nimport { ColorObject } from '../../typings/types';\nimport StrokeRenderer from './StrokeRenderer';\n\nexport default class CharacterRenderer {\n  _strokeRenderers: StrokeRenderer[];\n\n  constructor(character: Character) {\n    this._strokeRenderers = character.strokes.map((stroke) => new StrokeRenderer(stroke));\n  }\n\n  render(\n    ctx: CanvasRenderingContext2D,\n    props: {\n      opacity: number;\n      strokes: Record<number, StrokeRenderState>;\n      strokeColor: ColorObject;\n      radicalColor?: ColorObject | null;\n    },\n  ) {\n    if (props.opacity < 0.05) return;\n\n    const { opacity, strokeColor, radicalColor, strokes } = props;\n\n    for (let i = 0; i < this._strokeRenderers.length; i++) {\n      this._strokeRenderers[i].render(ctx, {\n        strokeColor,\n        radicalColor,\n        opacity: strokes[i].opacity * opacity,\n        displayPortion: strokes[i].displayPortion || 0,\n      });\n    }\n  }\n}\n", "import { ColorObject, Point } from '../../typings/types';\nimport { drawPath } from './canvasUtils';\n\nexport default function renderUserStroke(\n  ctx: CanvasRenderingContext2D,\n  props: {\n    opacity: number;\n    strokeWidth: number;\n    strokeColor: ColorObject;\n    points: Point[];\n  },\n) {\n  if (props.opacity < 0.05) {\n    return;\n  }\n  const { opacity, strokeWidth, strokeColor, points } = props;\n  const { r, g, b, a } = strokeColor;\n\n  ctx.save();\n  ctx.globalAlpha = opacity;\n  ctx.lineWidth = strokeWidth;\n  ctx.strokeStyle = `rgba(${r},${g},${b},${a})`;\n  ctx.lineCap = 'round';\n  ctx.lineJoin = 'round';\n  drawPath(ctx, points);\n  ctx.restore();\n}\n", "import Character from '../../models/Character';\nimport Positioner from '../../Positioner';\nimport HanziWriterRendererBase from '../HanziWriterRendererBase';\nimport CanvasRenderTarget from '../canvas/RenderTarget';\nimport CharacterRenderer from './CharacterRenderer';\nimport renderUserStroke from './renderUserStroke';\nimport { RenderStateObject } from '../../RenderState';\nimport { noop } from '../../utils';\n\nexport default class HanziWriterRenderer\n  implements HanziWriterRendererBase<HTMLCanvasElement, CanvasRenderTarget> {\n  _character: Character;\n  _positioner: Positioner;\n  _mainCharRenderer: CharacterRenderer;\n  _outlineCharRenderer: CharacterRenderer;\n  _highlightCharRenderer: CharacterRenderer;\n  _target: CanvasRenderTarget | undefined;\n\n  constructor(character: Character, positioner: Positioner) {\n    this._character = character;\n    this._positioner = positioner;\n    this._mainCharRenderer = new CharacterRenderer(character);\n    this._outlineCharRenderer = new CharacterRenderer(character);\n    this._highlightCharRenderer = new CharacterRenderer(character);\n  }\n\n  mount(target: CanvasRenderTarget) {\n    this._target = target;\n  }\n\n  destroy = noop;\n\n  _animationFrame(cb: (ctx: CanvasRenderingContext2D) => void) {\n    const { width, height, scale, xOffset, yOffset } = this._positioner;\n    const ctx = this._target!.getContext()!;\n    ctx.clearRect(0, 0, width, height);\n    ctx.save();\n    ctx.translate(xOffset, height - yOffset);\n    ctx.transform(1, 0, 0, -1, 0, 0);\n    ctx.scale(scale, scale);\n    cb(ctx);\n    ctx.restore();\n    // @ts-expect-error Verify if this is still needed for the \"wechat miniprogram\".\n    if (ctx.draw) {\n      // @ts-expect-error\n      ctx.draw();\n    }\n  }\n\n  render(props: RenderStateObject) {\n    const { outline, main, highlight } = props.character;\n    const {\n      outlineColor,\n      strokeColor,\n      radicalColor,\n      highlightColor,\n      drawingColor,\n      drawingWidth,\n    } = props.options;\n\n    this._animationFrame((ctx) => {\n      this._outlineCharRenderer.render(ctx, {\n        opacity: outline.opacity,\n        strokes: outline.strokes,\n        strokeColor: outlineColor,\n      });\n      this._mainCharRenderer.render(ctx, {\n        opacity: main.opacity,\n        strokes: main.strokes,\n        strokeColor: strokeColor,\n        radicalColor: radicalColor,\n      });\n      this._highlightCharRenderer.render(ctx, {\n        opacity: highlight.opacity,\n        strokes: highlight.strokes,\n        strokeColor: highlightColor,\n      });\n\n      const userStrokes = props.userStrokes || {};\n\n      for (const userStrokeId in userStrokes) {\n        const userStroke = userStrokes[userStrokeId];\n        if (userStroke) {\n          const userStrokeProps = {\n            strokeWidth: drawingWidth,\n            strokeColor: drawingColor,\n            ...userStroke,\n          };\n          renderUserStroke(ctx, userStrokeProps);\n        }\n      }\n    });\n  }\n}\n", "import RenderTargetBase from '../RenderTargetBase';\n\nexport default class RenderTarget extends RenderTargetBase<HTMLCanvasElement> {\n  constructor(canvas: HTMLCanvasElement) {\n    super(canvas);\n  }\n\n  static init(elmOrId: string | HTMLCanvasElement, width = '100%', height = '100%') {\n    const element = (() => {\n      if (typeof elmOrId === 'string') {\n        return document.getElementById(elmOrId);\n      }\n      return elmOrId;\n    })();\n\n    if (!element) {\n      throw new Error(`HanziWriter target element not found: ${elmOrId}`);\n    }\n\n    const nodeType = element.nodeName.toUpperCase();\n\n    const canvas = (() => {\n      if (nodeType === 'CANVAS') {\n        return element as HTMLCanvasElement;\n      }\n      const canvas = document.createElement('canvas');\n      element.appendChild(canvas);\n      return canvas;\n    })();\n\n    canvas.setAttribute('width', width);\n    canvas.setAttribute('height', height);\n\n    return new RenderTarget(canvas);\n  }\n\n  getContext() {\n    return this.node.getContext('2d');\n  }\n}\n", "import { RenderTargetInitFunction } from '../../typings/types';\nimport HanziWriterRenderer from './HanziWriterRenderer';\nimport RenderTarget from './RenderTarget';\n\nexport default {\n  HanziWriterRenderer,\n  createRenderTarget: RenderTarget.init as RenderTargetInitFunction<HTMLCanvasElement>,\n};\n", "import { CharacterJson } from './typings/types';\n\nconst VERSION = '2.0';\nconst getCharDataUrl = (char: string) =>\n  `https://cdn.jsdelivr.net/npm/hanzi-writer-data@${VERSION}/${char}.json`;\n\nconst defaultCharDataLoader = (\n  char: string,\n  onLoad: (parsedJson: CharacterJson) => void,\n  onError: (error?: any, context?: any) => void,\n) => {\n  // load char data from hanziwriter cdn (currently hosted on jsdelivr)\n  const xhr = new XMLHttpRequest();\n  if (xhr.overrideMimeType) {\n    // IE 9 and 10 don't seem to support this...\n    xhr.overrideMimeType('application/json');\n  }\n  xhr.open('GET', getCharDataUrl(char), true);\n  xhr.onerror = (event) => {\n    onError(xhr, event);\n  };\n  xhr.onreadystatechange = () => {\n    // TODO: error handling\n    if (xhr.readyState !== 4) return;\n\n    if (xhr.status === 200) {\n      onLoad(JSON.parse(xhr.responseText));\n    } else if (xhr.status !== 0 && onError) {\n      onError(xhr);\n    }\n  };\n  xhr.send(null);\n};\n\nexport default defaultCharDataLoader;\n", "import { HanziWriterOptions } from './typings/types';\nimport defaultCharDataLoader from './defaultCharDataLoader';\n\nconst defaultOptions: HanziWriterOptions = {\n  charDataLoader: defaultCharDataLoader,\n  onLoadCharDataError: null,\n  onLoadCharDataSuccess: null,\n  showOutline: true,\n  showCharacter: true,\n  renderer: 'svg',\n\n  // positioning options\n\n  width: 0,\n  height: 0,\n  padding: 20,\n\n  // animation options\n\n  strokeAnimationSpeed: 1,\n  strokeFadeDuration: 400,\n  strokeHighlightDuration: 200,\n  strokeHighlightSpeed: 2,\n  delayBetweenStrokes: 1000,\n  delayBetweenLoops: 2000,\n\n  // colors\n\n  strokeColor: '#555',\n  radicalColor: null,\n  highlightColor: '#AAF',\n  outlineColor: '#DDD',\n  drawingColor: '#333',\n\n  // quiz options\n\n  leniency: 1,\n  showHintAfterMisses: 3,\n  highlightOnComplete: true,\n  highlightCompleteColor: null,\n  markStrokeCorrectAfterMisses: false,\n  acceptBackwardsStrokes: false,\n  quizStartStrokeNum: 0,\n  averageDistanceThreshold: 350,\n\n  // undocumented obscure options\n\n  drawingFadeDuration: 300,\n  drawingWidth: 4,\n  strokeWidth: 2,\n  outlineWidth: 2,\n  rendererOverride: {},\n};\n\nexport default defaultOptions;\n", "import { CharacterJson, LoadingManagerOptions } from './typings/types';\n\ntype CustomError = Error & { reason: string };\n\nexport default class LoadingManager {\n  _loadCounter = 0;\n  _isLoading = false;\n  _resolve: ((data: CharacterJson) => void) | undefined;\n  _reject: ((error?: Error | CustomError | string) => void) | undefined;\n  _options: LoadingManagerOptions;\n\n  /** Set when calling LoadingManager.loadCharData  */\n  _loadingChar: string | undefined;\n  /** use this to attribute to determine if there was a problem with loading */\n  loadingFailed = false;\n\n  constructor(options: LoadingManagerOptions) {\n    this._options = options;\n  }\n\n  _debouncedLoad(char: string, count: number) {\n    // these wrappers ignore all responses except the most recent.\n    const wrappedResolve = (data: CharacterJson) => {\n      if (count === this._loadCounter) {\n        this._resolve?.(data);\n      }\n    };\n    const wrappedReject = (reason?: Error | string) => {\n      if (count === this._loadCounter) {\n        this._reject?.(reason);\n      }\n    };\n\n    const returnedData = this._options.charDataLoader(\n      char,\n      wrappedResolve,\n      wrappedReject,\n    );\n\n    if (returnedData) {\n      if ('then' in returnedData) {\n        returnedData.then(wrappedResolve).catch(wrappedReject);\n      } else {\n        wrappedResolve(returnedData);\n      }\n    }\n  }\n\n  _setupLoadingPromise() {\n    return new Promise(\n      (\n        resolve: (data: CharacterJson) => void,\n        reject: (err?: Error | CustomError | string) => void,\n      ) => {\n        this._resolve = resolve;\n        this._reject = reject;\n      },\n    )\n      .then((data: CharacterJson) => {\n        this._isLoading = false;\n        this._options.onLoadCharDataSuccess?.(data);\n        return data;\n      })\n      .catch((reason) => {\n        this._isLoading = false;\n        this.loadingFailed = true;\n\n        // If the user has provided an \"onLoadCharDataError\", call this function\n        // Otherwise, throw the promise\n        if (this._options.onLoadCharDataError) {\n          this._options.onLoadCharDataError(reason);\n          return;\n        }\n\n        // If error callback wasn't provided, throw an error so the developer will be aware something went wrong\n        if (reason instanceof Error) {\n          throw reason;\n        }\n\n        const err = new Error(\n          `Failed to load char data for ${this._loadingChar}`,\n        ) as CustomError;\n\n        err.reason = reason;\n\n        throw err;\n      });\n  }\n\n  loadCharData(char: string) {\n    this._loadingChar = char;\n    const promise = this._setupLoadingPromise();\n    this.loadingFailed = false;\n    this._isLoading = true;\n    this._loadCounter++;\n    this._debouncedLoad(char, this._loadCounter);\n    return promise;\n  }\n}\n", "import RenderState from './RenderState';\nimport parseCharData from './parseCharData';\nimport Positioner from './Positioner';\nimport Quiz from './Quiz';\nimport svgRenderer from './renderers/svg';\nimport canvasRenderer from './renderers/canvas';\nimport defaultOptions from './defaultOptions';\nimport LoadingManager from './LoadingManager';\nimport * as characterActions from './characterActions';\nimport { trim, colorStringToVals, selectIndex, fixIndex } from './utils';\nimport Character from './models/Character';\nimport HanziWriterRendererBase, {\n  HanziWriterRendererConstructor,\n} from './renderers/HanziWriterRendererBase';\nimport RenderTargetBase from './renderers/RenderTargetBase';\nimport { GenericMutation } from './Mutation';\n\n// Typings\nimport {\n  ColorOptions,\n  DimensionOptions,\n  HanziWriterOptions,\n  LoadingManagerOptions,\n  OnCompleteFunction,\n  ParsedHanziWriterOptions,\n  QuizOptions,\n  RenderTargetInitFunction,\n} from './typings/types';\n\n// Export type interfaces\nexport * from './typings/types';\n\nexport default class HanziWriter {\n  _options: ParsedHanziWriterOptions;\n  _loadingManager: LoadingManager;\n  /** Only set when calling .setCharacter() */\n  _char: string | undefined;\n  /** Only set when calling .setCharacter() */\n  _renderState: RenderState | undefined;\n  /** Only set when calling .setCharacter() */\n  _character: Character | undefined;\n  /** Only set when calling .setCharacter() */\n  _positioner: Positioner | undefined;\n  /** Only set when calling .setCharacter() */\n  _hanziWriterRenderer: HanziWriterRendererBase<HTMLElement, any> | null | undefined;\n  /** Only set when calling .setCharacter() */\n  _withDataPromise: Promise<void> | undefined;\n\n  _quiz: Quiz | undefined;\n  _renderer: {\n    HanziWriterRenderer: HanziWriterRendererConstructor;\n    createRenderTarget: RenderTargetInitFunction<any>;\n  };\n\n  target: RenderTargetBase;\n\n  /** Main entry point */\n  static create(\n    element: string | HTMLElement,\n    character: string,\n    options?: Partial<HanziWriterOptions>,\n  ) {\n    const writer = new HanziWriter(element, options);\n    writer.setCharacter(character);\n\n    return writer;\n  }\n\n  /** Singleton instance of LoadingManager. Only set in `loadCharacterData` */\n  static _loadingManager: LoadingManager | null = null;\n  /** Singleton loading options. Only set in `loadCharacterData` */\n  static _loadingOptions: Partial<HanziWriterOptions> | null = null;\n\n  static loadCharacterData(\n    character: string,\n    options: Partial<LoadingManagerOptions> = {},\n  ) {\n    const loadingManager = (() => {\n      const { _loadingManager, _loadingOptions } = HanziWriter;\n      if (_loadingManager?._loadingChar === character && _loadingOptions === options) {\n        return _loadingManager;\n      }\n      return new LoadingManager({ ...defaultOptions, ...options });\n    })();\n\n    HanziWriter._loadingManager = loadingManager;\n    HanziWriter._loadingOptions = options;\n    return loadingManager.loadCharData(character);\n  }\n\n  static getScalingTransform(width: number, height: number, padding = 0) {\n    const positioner = new Positioner({ width, height, padding });\n    return {\n      x: positioner.xOffset,\n      y: positioner.yOffset,\n      scale: positioner.scale,\n      transform: trim(`\n        translate(${positioner.xOffset}, ${positioner.height - positioner.yOffset})\n        scale(${positioner.scale}, ${-1 * positioner.scale})\n      `).replace(/\\s+/g, ' '),\n    };\n  }\n\n  constructor(element: string | HTMLElement, options: Partial<HanziWriterOptions> = {}) {\n    const { HanziWriterRenderer, createRenderTarget } =\n      options.renderer === 'canvas' ? canvasRenderer : svgRenderer;\n    const rendererOverride = options.rendererOverride || {};\n\n    this._renderer = {\n      HanziWriterRenderer: rendererOverride.HanziWriterRenderer || HanziWriterRenderer,\n      createRenderTarget: rendererOverride.createRenderTarget || createRenderTarget,\n    };\n    // wechat miniprogram component needs direct access to the render target, so this is public\n    this.target = this._renderer.createRenderTarget(\n      element,\n      options.width,\n      options.height,\n    );\n    this._options = this._assignOptions(options);\n    this._loadingManager = new LoadingManager(this._options);\n    this._setupListeners();\n  }\n\n  showCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n      duration?: number;\n    } = {},\n  ) {\n    this._options.showCharacter = true;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.showCharacter(\n            'main',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  hideCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n      duration?: number;\n    } = {},\n  ) {\n    this._options.showCharacter = false;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.hideCharacter(\n            'main',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  animateCharacter(\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this.cancelQuiz();\n\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.animateCharacter(\n            'main',\n            this._character!,\n            this._options.strokeFadeDuration,\n            this._options.strokeAnimationSpeed,\n            this._options.delayBetweenStrokes,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  animateStroke(\n    strokeNum: number,\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this.cancelQuiz();\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.animateSingleStroke(\n            'main',\n            this._character!,\n            fixIndex(strokeNum, this._character!.strokes.length),\n            this._options.strokeAnimationSpeed,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  highlightStroke(\n    strokeNum: number,\n    options: {\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    const promise = () => {\n      if (!this._character || !this._renderState) {\n        return;\n      }\n\n      return this._renderState\n        .run(\n          characterActions.highlightStroke(\n            selectIndex(this._character.strokes, strokeNum),\n            colorStringToVals(this._options.highlightColor),\n            this._options.strokeHighlightSpeed,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        });\n    };\n\n    return this._withData(promise);\n  }\n\n  async loopCharacterAnimation() {\n    this.cancelQuiz();\n    return this._withData(() =>\n      this._renderState!.run(\n        characterActions.animateCharacterLoop(\n          'main',\n          this._character!,\n          this._options.strokeFadeDuration,\n          this._options.strokeAnimationSpeed,\n          this._options.delayBetweenStrokes,\n          this._options.delayBetweenLoops,\n        ),\n        { loop: true },\n      ),\n    );\n  }\n\n  pauseAnimation() {\n    return this._withData(() => this._renderState?.pauseAll());\n  }\n\n  resumeAnimation() {\n    return this._withData(() => this._renderState?.resumeAll());\n  }\n\n  showOutline(\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this._options.showOutline = true;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.showCharacter(\n            'outline',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  hideOutline(\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    this._options.showOutline = false;\n    return this._withData(() =>\n      this._renderState\n        ?.run(\n          characterActions.hideCharacter(\n            'outline',\n            this._character!,\n            typeof options.duration === 'number'\n              ? options.duration\n              : this._options.strokeFadeDuration,\n          ),\n        )\n        .then((res) => {\n          options.onComplete?.(res);\n          return res;\n        }),\n    );\n  }\n\n  /** Updates the size of the writer instance without resetting render state */\n  updateDimensions({ width, height, padding }: Partial<DimensionOptions>) {\n    if (width !== undefined) this._options.width = width;\n    if (height !== undefined) this._options.height = height;\n    if (padding !== undefined) this._options.padding = padding;\n    this.target.updateDimensions(this._options.width, this._options.height);\n    // if there's already a character drawn, destroy and recreate the renderer in the same state\n    if (\n      this._character &&\n      this._renderState &&\n      this._hanziWriterRenderer &&\n      this._positioner\n    ) {\n      this._hanziWriterRenderer.destroy();\n      const hanziWriterRenderer = this._initAndMountHanziWriterRenderer(this._character);\n      // TODO: this should probably implement EventEmitter instead of manually tracking updates like this\n      this._renderState.overwriteOnStateChange((nextState) =>\n        hanziWriterRenderer.render(nextState),\n      );\n      hanziWriterRenderer.render(this._renderState.state);\n      // update the current quiz as well, if one is active\n      if (this._quiz) {\n        this._quiz.setPositioner(this._positioner);\n      }\n    }\n  }\n\n  updateColor(\n    colorName: keyof ColorOptions,\n    colorVal: string | null,\n    options: {\n      duration?: number;\n      onComplete?: OnCompleteFunction;\n    } = {},\n  ) {\n    let mutations: GenericMutation[] = [];\n\n    const fixedColorVal = (() => {\n      // If we're removing radical color, tween it to the stroke color\n      if (colorName === 'radicalColor' && !colorVal) {\n        return this._options.strokeColor;\n      }\n      return colorVal;\n    })();\n\n    const mappedColor = colorStringToVals(fixedColorVal as string);\n\n    this._options[colorName] = colorVal as any;\n\n    const duration = options.duration ?? this._options.strokeFadeDuration;\n\n    mutations = mutations.concat(\n      characterActions.updateColor(colorName, mappedColor, duration),\n    );\n\n    // make sure to set radicalColor back to null after the transition finishes if val == null\n    if (colorName === 'radicalColor' && !colorVal) {\n      mutations = mutations.concat(characterActions.updateColor(colorName, null, 0));\n    }\n\n    return this._withData(() =>\n      this._renderState?.run(mutations).then((res) => {\n        options.onComplete?.(res);\n        return res;\n      }),\n    );\n  }\n\n  quiz(quizOptions: Partial<QuizOptions> = {}) {\n    return this._withData(async () => {\n      if (this._character && this._renderState && this._positioner) {\n        this.cancelQuiz();\n        this._quiz = new Quiz(this._character, this._renderState, this._positioner);\n        this._options = {\n          ...this._options,\n          ...quizOptions,\n        };\n        this._quiz.startQuiz(this._options);\n      }\n    });\n  }\n\n  skipQuizStroke() {\n    if (this._quiz) {\n      this._quiz.nextStroke();\n    }\n  }\n\n  cancelQuiz() {\n    if (this._quiz) {\n      this._quiz.cancel();\n      this._quiz = undefined;\n    }\n  }\n\n  setCharacter(char: string) {\n    this.cancelQuiz();\n    this._char = char;\n    if (this._hanziWriterRenderer) {\n      this._hanziWriterRenderer.destroy();\n    }\n    if (this._renderState) {\n      this._renderState.cancelAll();\n    }\n    this._hanziWriterRenderer = null;\n    this._withDataPromise = this._loadingManager\n      .loadCharData(char)\n      .then((pathStrings) => {\n        // if \"pathStrings\" isn't set, \".catch()\"\" was probably called and loading likely failed\n        if (!pathStrings || this._loadingManager.loadingFailed) {\n          return;\n        }\n\n        this._character = parseCharData(char, pathStrings);\n        this._renderState = new RenderState(this._character, this._options, (nextState) =>\n          hanziWriterRenderer.render(nextState),\n        );\n\n        const hanziWriterRenderer = this._initAndMountHanziWriterRenderer(\n          this._character,\n        );\n        hanziWriterRenderer.render(this._renderState.state);\n      });\n    return this._withDataPromise;\n  }\n\n  _initAndMountHanziWriterRenderer(character: Character) {\n    const { width, height, padding } = this._options;\n    this._positioner = new Positioner({ width, height, padding });\n    const hanziWriterRenderer = new this._renderer.HanziWriterRenderer(\n      character,\n      this._positioner,\n    );\n    hanziWriterRenderer.mount(this.target);\n    this._hanziWriterRenderer = hanziWriterRenderer;\n    return hanziWriterRenderer;\n  }\n\n  async getCharacterData(): Promise<Character> {\n    if (!this._char) {\n      throw new Error('setCharacter() must be called before calling getCharacterData()');\n    }\n    const character = await this._withData(() => this._character);\n    return character!;\n  }\n\n  _assignOptions(options: Partial<HanziWriterOptions>): ParsedHanziWriterOptions {\n    const mergedOptions = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    // backfill strokeAnimationSpeed if deprecated strokeAnimationDuration is provided instead\n    if (options.strokeAnimationDuration && !options.strokeAnimationSpeed) {\n      mergedOptions.strokeAnimationSpeed = 500 / options.strokeAnimationDuration;\n    }\n    if (options.strokeHighlightDuration && !options.strokeHighlightSpeed) {\n      mergedOptions.strokeHighlightSpeed = 500 / mergedOptions.strokeHighlightDuration;\n    }\n\n    if (!options.highlightCompleteColor) {\n      mergedOptions.highlightCompleteColor = mergedOptions.highlightColor;\n    }\n\n    return this._fillWidthAndHeight(mergedOptions);\n  }\n\n  /** returns a new options object with width and height filled in if missing */\n  _fillWidthAndHeight(options: HanziWriterOptions): ParsedHanziWriterOptions {\n    const filledOpts = { ...options };\n    if (filledOpts.width && !filledOpts.height) {\n      filledOpts.height = filledOpts.width;\n    } else if (filledOpts.height && !filledOpts.width) {\n      filledOpts.width = filledOpts.height;\n    } else if (!filledOpts.width && !filledOpts.height) {\n      const { width, height } = this.target.getBoundingClientRect();\n      const minDim = Math.min(width, height);\n      filledOpts.width = minDim;\n      filledOpts.height = minDim;\n    }\n    return filledOpts as ParsedHanziWriterOptions;\n  }\n\n  _withData<T>(func: () => T) {\n    // if this._loadingManager.loadingFailed, then loading failed before this method was called\n    if (this._loadingManager.loadingFailed) {\n      throw Error('Failed to load character data. Call setCharacter and try again.');\n    }\n\n    if (this._withDataPromise) {\n      return this._withDataPromise.then(() => {\n        if (!this._loadingManager.loadingFailed) {\n          return func();\n        }\n      });\n    }\n    return Promise.resolve().then(func);\n  }\n\n  _setupListeners() {\n    this.target.addPointerStartListener((evt) => {\n      if (this._quiz) {\n        evt.preventDefault();\n        this._quiz.startUserStroke(evt.getPoint());\n      }\n    });\n    this.target.addPointerMoveListener((evt) => {\n      if (this._quiz) {\n        evt.preventDefault();\n        this._quiz.continueUserStroke(evt.getPoint());\n      }\n    });\n    this.target.addPointerEndListener(() => {\n      this._quiz?.endUserStroke();\n    });\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAMA,YAAY,OAAOC,WAAW,cAAcC,SAASD;AAEpD,IAAME,iBACVH,UAAUI,gBAAgB,MAAMJ,UAAUI,YAAYC,IAAtB,OAAkC,MAAMC,KAAKD,IAAL;AACpE,IAAME,0BACX,wBAAAP,UAAUO,2BAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAuBC,KAAKR,SAAAA,OACpCS,cAAaC,WAAW,MAAMD,SAASN,eAAc,CAAf,GAAoB,MAAO,EAA1C;AACpB,IAAMQ,yBACX,wBAAAX,UAAUW,0BAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAsBH,KAAKR,SAAAA,MAAcY;AAmB/C,SAAUC,QAAgBC,KAAkB;SACzCA,IAAIA,IAAIC,SAAS,CAAd;;AAGL,IAAMC,WAAW,CAACC,OAAeF,YAAkB;MAEpDE,QAAQ,GAAG;WACNF,UAASE;;SAEXA;AALF;AAQA,IAAMC,cAAc,CAAIJ,KAAeG,UAAiB;SAEtDH,IAAIE,SAASC,OAAOH,IAAIC,MAAZ,CAAT;AAFL;AAKD,SAAUI,iBAAoBC,MAASC,UAAyC;QAC9EC,SAAS;IAAE,GAAGF;EAAL;aACJG,OAAOF,UAAU;UACpBG,UAAUJ,KAAKG,GAAD;UACdE,cAAcJ,SAASE,GAAD;QACxBC,YAAYC,aAAa;;;QAI3BD,WACAC,eACA,OAAOD,YAAY,YACnB,OAAOC,gBAAgB,YACvB,CAACC,MAAMC,QAAQF,WAAd,GACD;AACAH,aAAOC,GAAD,IAAQJ,iBAAiBK,SAASC,WAAV;IAPhC,OAQO;AAELH,aAAOC,GAAD,IAAQE;;;SAGXH;;AAIH,SAAUM,QAAQC,OAAeC,KAAQ;QACvCC,QAAQF,MAAMG,MAAM,GAAZ;QACRC,QAAa,CAAA;MACfC,UAAUD;WACLE,IAAI,GAAGA,IAAIJ,MAAMhB,QAAQoB,KAAK;UAC/BC,MAAMD,MAAMJ,MAAMhB,SAAS,IAAIe,MAAM,CAAA;AAC3CI,YAAQH,MAAMI,CAAD,CAAN,IAAaC;AACpBF,cAAUE;;SAELH;;AAGT,IAAII,QAAQ;AAEN,SAAUC,UAAO;AACrBD;SACOA;;AAGH,SAAUE,QAAQzB,KAAa;QAC7B0B,MAAM1B,IAAI2B,OAAO,CAACC,KAAKC,QAAQA,MAAMD,KAAK,CAApC;SACLF,MAAM1B,IAAIC;;AAOb,SAAU6B,kBAAkBC,aAAmB;QAC7CC,kBAAkBD,YAAYE,YAAZ,EAA0BC,KAA1B;MAEpB,wBAAwBC,KAAKH,eAA7B,GAA+C;QAC7CI,WAAWJ,gBAAgBK,UAAU,CAA1B,EAA6BnB,MAAM,EAAnC;QACXkB,SAASnC,WAAW,GAAG;AACzBmC,iBAAW,CACTA,SAAS,CAAD,GACRA,SAAS,CAAD,GACRA,SAAS,CAAD,GACRA,SAAS,CAAD,GACRA,SAAS,CAAD,GACRA,SAAS,CAAD,CANC;;UASPE,SAAM,GAAMF,SAASG,KAAK,EAAd,CAAA;WACX;MACLC,GAAGC,SAASH,OAAOI,MAAM,GAAG,CAAhB,GAAoB,EAArB;MACXC,GAAGF,SAASH,OAAOI,MAAM,GAAG,CAAhB,GAAoB,EAArB;MACXE,GAAGH,SAASH,OAAOI,MAAM,GAAG,CAAhB,GAAoB,EAArB;MACXG,GAAG;IAJE;;QAOHC,WAAWd,gBAAgBe,MAC/B,iEADe;MAGbD,UAAU;WACL;MACLN,GAAGC,SAASK,SAAS,CAAD,GAAK,EAAd;MACXH,GAAGF,SAASK,SAAS,CAAD,GAAK,EAAd;MACXF,GAAGH,SAASK,SAAS,CAAD,GAAK,EAAd;;MAEXD,GAAGG,WAAWF,SAAS,CAAD,KAAO,GAAG,EAAnB;IALR;;QAQH,IAAIG,MAAJ,kBAA4BlB,WAAAA,EAA5B;;AAGD,IAAMG,OAAQgB,YAAmBA,OAAOC,QAAQ,QAAQ,EAAvB,EAA2BA,QAAQ,QAAQ,EAA3C;AAIlC,SAAUC,UAAaC,MAASC,OAAa;QAC3CtC,MAAyB,CAAA;WACtBK,IAAI,GAAGA,IAAIiC,OAAOjC,KAAK;AAC9BL,QAAIK,CAAD,IAAMgC;;SAEJrC;;AAIH,SAAUuC,YAAeD,OAAeE,IAAoB;QAC1DxC,MAAyB,CAAA;WACtBK,IAAI,GAAGA,IAAIiC,OAAOjC,KAAK;AAC9BL,QAAIK,CAAD,IAAMmC,GAAGnC,CAAD;;SAENL;;AAGT,IAAMyC,OAAK,uBAAAvE,UAAUwE,eAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAWC,cAAa;AAEtC,IAAMC,cACXH,GAAGI,QAAQ,OAAX,IAAsB,KAAKJ,GAAGI,QAAQ,UAAX,IAAyB,KAAKJ,GAAGI,QAAQ,OAAX,IAAsB;AAG1E,IAAMC,OAAO,MAAK;AAAA;AC3FX,IAAOC,cAAP,MAAkB;EAM9BC,YACEC,WACAC,SACAC,gBAAuCL,MAAI;SAR7CM,kBAAmC,CAAA;SAU5BC,iBAAiBF;SAEjBG,QAAQ;MACXJ,SAAS;QACPK,qBAAqBL,QAAQK;QAC7BC,cAAcN,QAAQM;QACtBC,cAAc3C,kBAAkBoC,QAAQO,YAAT;QAC/BC,aAAa5C,kBAAkBoC,QAAQQ,WAAT;QAC9BC,cAAc7C,kBAAkBoC,QAAQS,YAAT;QAC/BC,cAAc9C,kBAAkBoC,QAAQU,gBAAgBV,QAAQQ,WAAjC;QAC/BG,gBAAgB/C,kBAAkBoC,QAAQW,cAAT;MAP1B;MASTZ,WAAW;QACTa,MAAM;UACJC,SAASb,QAAQc,gBAAgB,IAAI;UACrCC,SAAS,CAAA;QAFL;QAINC,SAAS;UACPH,SAASb,QAAQiB,cAAc,IAAI;UACnCF,SAAS,CAAA;QAFF;QAITG,WAAW;UACTL,SAAS;UACTE,SAAS,CAAA;QAFA;MATF;MAcXI,aAAa;IAxBF;aA2BJhE,IAAI,GAAGA,IAAI4C,UAAUgB,QAAQhF,QAAQoB,KAAK;WAC5CiD,MAAML,UAAUa,KAAKG,QAAQ5D,CAAAA,IAAK;QACrC0D,SAAS;QACTO,gBAAgB;MAFqB;WAKlChB,MAAML,UAAUiB,QAAQD,QAAQ5D,CAAAA,IAAK;QACxC0D,SAAS;QACTO,gBAAgB;MAFwB;WAKrChB,MAAML,UAAUmB,UAAUH,QAAQ5D,CAAAA,IAAK;QAC1C0D,SAAS;QACTO,gBAAgB;MAF0B;;;EAOhDC,uBAAuBpB,eAAoC;SACpDE,iBAAiBF;;EAGxBqB,YAAYC,cAAiD;UACrDC,YAAYrF,iBAAiB,KAAKiE,OAAOmB,YAAb;SAC7BpB,eAAeqB,WAAW,KAAKpB,KAAAA;SAC/BA,QAAQoB;;EAGfC,IACEC,WACA1B,UAEI,CAAA,GAAE;UAEA2B,SAASD,UAAUE,IAAKC,SAAQA,IAAIhF,KAA3B;SAEViF,gBAAgBH,MAAAA;WAEd,IAAII,QAASC,aAA+B;YAC3CC,gBAA+B;QACnCC,WAAW;QACXC,QAAQ;QACRC,UAAUJ;QACVK,YAAYX;QACZY,OAAOtC,QAAQuC;QACfC,SAASb;MAN0B;WAQhCzB,gBAAgBuC,KAAKR,aAAAA;WACrBS,KAAKT,aAAAA;IAVL,CAAA;;EAcTS,KAAKT,eAA4B;QAC3B,CAACA,cAAcC,WAAW;;;UAIxBR,YAAYO,cAAcI;QAC5BJ,cAAcE,UAAUT,UAAU3F,QAAQ;UACxCkG,cAAcK,OAAO;AACvBL,sBAAcE,SAAS;MADzB,OAEO;AACLF,sBAAcC,YAAY;aACrBhC,kBAAkB,KAAKA,gBAAgByC,OACzCC,WAAUA,UAAUX,aADA;AAIvBA,sBAAcG,SAAS;UAAES,UAAU;QAAZ,CAAvB;;;;UAKEC,iBAAiBb,cAAcI,WAAWJ,cAAcE,MAAvC;AAEvBW,mBAAerB,IAAI,IAAnB,EAAyBsB,KAAK,MAAK;UAC7Bd,cAAcC,WAAW;AAC3BD,sBAAcE;aACTO,KAAKT,aAAAA;;IAHd,CAAA;;EAQFe,sBAAmB;WACV,KAAK9C,gBAAgB0B,IAAKgB,WAAUA,MAAMP,WAAWO,MAAMT,MAAvB,CAApC;;EAGTc,WAAQ;SACDD,oBAAAA,EAAsBE,QAASC,cAAaA,SAASC,MAAT,CAAA;;EAGnDC,YAAS;SACFL,oBAAAA,EAAsBE,QAASC,cAAaA,SAASG,OAAT,CAAA;;EAGnDxB,gBAAgByB,gBAAwB;eAC3BX,SAAS,KAAK1C,iBAAiB;iBAC7BsD,WAAWZ,MAAMJ,SAAS;mBACxBiB,iBAAiBF,gBAAgB;cACtCC,QAAQE,WAAWD,aAAnB,KAAqCA,cAAcC,WAAWF,OAAzB,GAAmC;iBACrEG,qBAAqBf,KAAAA;;;;;;EAOpCgB,YAAS;SACF9B,gBAAgB,CAAC,EAAD,CAAA;;EAGvB6B,qBAAqB1B,eAA4B;;AAC/CA,kBAAcC,YAAY;aACjB/E,IAAI8E,cAAcE,QAAQhF,IAAI8E,cAAcI,WAAWtG,QAAQoB,KAAK;AAC3E8E,oBAAcI,WAAWlF,CAAzB,EAA4B0G,OAAO,IAAnC;;6BAGF5B,cAAcG,cAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAAA,KAAdH,eAAyB;MAAEY,UAAU;IAAZ,CAAA;SAEpB3C,kBAAkB,KAAKA,gBAAgByC,OACzCC,WAAUA,UAAUX,aADA;;AA9JK;ACvEzB,IAAM6B,WAAW,CAACC,IAAWC,QAAe;EAAEC,GAAGF,GAAGE,IAAID,GAAGC;EAAGC,GAAGH,GAAGG,IAAIF,GAAGE;AAA/B;AAE5C,IAAMC,YAAaC,WACxBC,KAAKC,KAAKD,KAAKE,IAAIH,MAAMH,GAAG,CAAlB,IAAuBI,KAAKE,IAAIH,MAAMF,GAAG,CAAlB,CAAjC;AAEK,IAAMM,WAAW,CAACC,QAAeC,WACtCP,UAAUL,SAASW,QAAQC,MAAT,CAAT;AAEJ,IAAMC,SAAS,CAACF,QAAeC,WACpCD,OAAOR,MAAMS,OAAOT,KAAKQ,OAAOP,MAAMQ,OAAOR;AAExC,IAAMU,QAAQ,CAACR,OAAcS,YAAY,MAAK;QAC7CC,aAAaD,YAAY;SACxB;IACLZ,GAAGI,KAAKO,MAAME,aAAaV,MAAMH,CAA9B,IAAmCa;IACtCZ,GAAGG,KAAKO,MAAME,aAAaV,MAAMF,CAA9B,IAAmCY;EAFjC;AAFF;AAQA,IAAM/I,SAAUgJ,YAAmB;MACpCC,YAAYD,OAAO,CAAD;QAChBE,kBAAkBF,OAAOvG,MAAM,CAAb;SACjByG,gBAAgBxH,OAAO,CAACC,KAAK0G,UAAS;UACrCc,OAAOV,SAASJ,OAAOY,SAAR;AACrBA,gBAAYZ;WACL1G,MAAMwH;EAHR,GAIJ,CAJI;AAHF;AAUA,IAAMC,mBAAmB,CAACV,QAAeC,WAAiB;QACzDU,gBAAgBX,OAAOR,IAAIS,OAAOT,IAAIQ,OAAOP,IAAIQ,OAAOR;SACvDkB,gBAAgBjB,UAAUM,MAAD,IAAWN,UAAUO,MAAD;AAF/C;AASA,IAAMW,qBAAqB,CAACtB,IAAWC,IAAWkB,SAAgB;QACjEI,OAAOxB,SAASE,IAAID,EAAL;QACfwB,OAAOL,OAAOf,UAAUmB,IAAD;SACtB;IAAErB,GAAGD,GAAGC,IAAIsB,OAAOD,KAAKrB;IAAGC,GAAGF,GAAGE,IAAIqB,OAAOD,KAAKpB;EAAjD;AAHF;AAOA,IAAMsB,cAAc,CAACC,QAAiBC,WAAmB;QACxDC,YAAYF,OAAO1J,UAAU2J,OAAO3J,SAAS0J,SAASC;QACtDE,aAAaH,OAAO1J,UAAU2J,OAAO3J,SAAS2J,SAASD;QAEvDI,UAAU,CACd1I,GACA2I,GACAC,iBACAC,kBACU;QACN7I,MAAM,KAAK2I,MAAM,GAAG;aACftB,SAASmB,UAAU,CAAD,GAAKC,WAAW,CAAD,CAAzB;;QAGbzI,IAAI,KAAK2I,MAAM,GAAG;aACbzB,KAAK4B,IAAIF,gBAAe,CAAD,GAAKvB,SAASmB,UAAUxI,CAAD,GAAKyI,WAAW,CAAD,CAAzB,CAApC;;UAGHM,aAAaF,cAAcA,cAAcjK,SAAS,CAAxB;QAE5BoB,MAAM,KAAK2I,IAAI,GAAG;aACbzB,KAAK4B,IAAIC,YAAY1B,SAASmB,UAAU,CAAD,GAAKC,WAAWE,CAAD,CAAzB,CAA7B;;WAGFzB,KAAK4B,IACV5B,KAAK8B,IAAIJ,gBAAeD,CAAD,GAAKC,gBAAeD,IAAI,CAAL,GAASI,UAAnD,GACA1B,SAASmB,UAAUxI,CAAD,GAAKyI,WAAWE,CAAD,CAAzB,CAFH;EApBT;MA0BIC,iBAA2B,CAAA;WACtB5I,IAAI,GAAGA,IAAIwI,UAAU5J,QAAQoB,KAAK;UACnC6I,gBAA0B,CAAA;aACvBF,IAAI,GAAGA,IAAIF,WAAW7J,QAAQ+J,KAAK;AAK1CE,oBAAcvD,KAAKoD,QAAQ1I,GAAG2I,GAAGC,gBAAgBC,aAAvB,CAA1B;;AAEFD,qBAAiBC;;SAGZD,eAAeH,WAAW7J,SAAS,CAArB;AA3ChB;AA+CA,IAAMqK,iBAAiB,CAACC,OAAgBC,SAAS,SAAQ;QACxDC,WAAWF,MAAM7H,MAAM,GAAG,CAAf;aAEN4F,SAASiC,MAAM7H,MAAM,CAAZ,GAAgB;UAC5BgI,YAAYD,SAASA,SAASxK,SAAS,CAAnB;UACpB0K,SAASjC,SAASJ,OAAOoC,SAAR;QACnBC,SAASH,QAAQ;YACbI,eAAerC,KAAKsC,KAAKF,SAASH,MAAnB;YACfM,YAAYH,SAASC;eAClBvJ,IAAI,GAAGA,IAAIuJ,cAAcvJ,KAAK;AACrCoJ,iBAAS9D,KAAK4C,mBAAmBjB,OAAOoC,WAAW,KAAKI,aAAazJ,IAAI,EAAzC,CAAhC;;IAJJ,OAMO;AACLoJ,eAAS9D,KAAK2B,KAAd;;;SAIGmC;AAjBF;AAqBA,IAAMM,eAAe,CAACR,OAAgBS,YAAY,OAAM;QACvDC,WAAWhL,OAAOsK,KAAD;QACjBW,aAAaD,YAAYD,YAAY;QACrCG,gBAAgB,CAACZ,MAAM,CAAD,CAAN;QAChBa,WAAWrL,QAAQwK,KAAD;QAClBc,uBAAuBd,MAAM7H,MAAM,CAAZ;WAEpBrB,IAAI,GAAGA,IAAI2J,YAAY,GAAG3J,KAAK;QAClC6H,YAAmBnJ,QAAQoL,aAAD;QAC1BG,gBAAgBJ;QAChBK,oBAAoB;WACjB,CAACA,mBAAmB;YACnBC,gBAAgB9C,SAASQ,WAAWmC,qBAAqB,CAAD,CAAhC;UAC1BG,gBAAgBF,eAAe;AACjCA,yBAAiBE;AACjBtC,oBAAYmC,qBAAqBI,MAArB;MAFd,OAGO;cACCC,YAAYnC,mBAChBL,WACAmC,qBAAqB,CAAD,GACpBC,gBAAgBE,aAHkB;AAKpCL,sBAAcxE,KAAK+E,SAAnB;AACAH,4BAAoB;;;;AAK1BJ,gBAAcxE,KAAKyE,QAAnB;SAEOD;AA9BF;AAkCA,IAAMQ,iBAAkBpB,WAAkB;QACzCqB,gBAAgBb,aAAaR,KAAD;QAC5BsB,QAAQpK,QAAQmK,cAAc9F,IAAKwC,WAAUA,MAAMH,CAAnC,CAAD;QACf2D,QAAQrK,QAAQmK,cAAc9F,IAAKwC,WAAUA,MAAMF,CAAnC,CAAD;QACf2D,OAAO;IAAE5D,GAAG0D;IAAOzD,GAAG0D;EAAf;QACPE,kBAAkBJ,cAAc9F,IAAKwC,WAAUN,SAASM,OAAOyD,IAAR,CAArC;QAClBE,QAAQ1D,KAAKC,KACjB/G,QAAQ,CACN8G,KAAKE,IAAIuD,gBAAgB,CAAD,EAAI7D,GAAG,CAA/B,IAAoCI,KAAKE,IAAIuD,gBAAgB,CAAD,EAAI5D,GAAG,CAA/B,GACpCG,KAAKE,IAAI1I,QAAQiM,eAAD,EAAkB7D,GAAG,CAArC,IAA0CI,KAAKE,IAAI1I,QAAQiM,eAAD,EAAkB5D,GAAG,CAArC,CAFpC,CAAD,CADK;QAMR8D,cAAcF,gBAAgBlG,IAAKwC,YAAW;IAClDH,GAAGG,MAAMH,IAAI8D;IACb7D,GAAGE,MAAMF,IAAI6D;EAFqC,EAAhC;SAIb3B,eAAe4B,WAAD;AAhBhB;AAoBA,IAAMC,SAAS,CAAC5B,OAAgB6B,UAAiB;SAC/C7B,MAAMzE,IAAKwC,YAAW;IAC3BH,GAAGI,KAAK8D,IAAID,KAAT,IAAkB9D,MAAMH,IAAII,KAAK+D,IAAIF,KAAT,IAAkB9D,MAAMF;IACvDA,GAAGG,KAAK+D,IAAIF,KAAT,IAAkB9D,MAAMH,IAAII,KAAK8D,IAAID,KAAT,IAAkB9D,MAAMF;EAF5B,EAAtB;AADF;AAQA,IAAMmE,wBAAyBtD,YAAmB;MACnDA,OAAOhJ,SAAS,EAAG,QAAOgJ;QACxBuD,iBAAiB,CAACvD,OAAO,CAAD,GAAKA,OAAO,CAAD,CAAlB;AACvBA,SAAOvG,MAAM,CAAb,EAAgB0E,QAASkB,WAAS;UAC1BmE,oBAAoBD,eAAevM;UACnCyM,UAAU1E,SAASM,OAAOkE,eAAeC,oBAAoB,CAArB,CAAtB;UAClBE,WAAW3E,SACfwE,eAAeC,oBAAoB,CAArB,GACdD,eAAeC,oBAAoB,CAArB,CAFS;UAKnBG,aAAaF,QAAQtE,IAAIuE,SAASxE,IAAIuE,QAAQvE,IAAIwE,SAASvE,MAAM;QACnEwE,YAAY;AACdJ,qBAAeK,IAAf;;AAEFL,mBAAe7F,KAAK2B,KAApB;EAZF,CAAA;SAcOkE;AAjBF;AAoBD,SAAUM,cAAc7D,QAAiB8D,QAAQ,OAAK;QACpDC,QAAQlE,MAAMG,OAAO,CAAD,CAAP;QACbgE,kBAAkBhE,OAAOvG,MAAM,CAAb;MACpBwK,aAAU,KAAQF,MAAM7E,CAAAA,IAAK6E,MAAM5E,CAAAA;AACvC6E,kBAAgB7F,QAASkB,WAAS;UAC1B6E,eAAerE,MAAMR,KAAD;AAC1B4E,kBAAU,MAAUC,aAAahF,CAAAA,IAAKgF,aAAa/E,CAAAA;EAFrD,CAAA;MAII2E,OAAO;AACTG,kBAAc;;SAETA;;AAIF,IAAME,cAAc,CAACnE,QAAiBG,SAAgB;QACrDoD,iBAAiBD,sBAAsBtD,MAAD;MACxCuD,eAAevM,SAAS,EAAG,QAAOuM;QAChCvE,KAAKuE,eAAe,CAAD;QACnBtE,KAAKsE,eAAe,CAAD;QACnBa,WAAW9D,mBAAmBtB,IAAIC,IAAIkB,IAAT;QAC7BkE,iBAAiBd,eAAe9J,MAAM,CAArB;AACvB4K,iBAAeC,QAAQF,QAAvB;SACOC;AARF;AClNO,IAAOE,SAAP,MAAa;EAMzBxJ,YAAYyJ,MAAcxE,QAAiByE,WAAmBC,cAAc,OAAK;SAC1EF,OAAOA;SACPxE,SAASA;SACTyE,YAAYA;SACZC,cAAcA;;EAGrBC,mBAAgB;WACP,KAAK3E,OAAO,CAAZ;;EAGT4E,iBAAc;WACL,KAAK5E,OAAO,KAAKA,OAAOhJ,SAAS,CAAjC;;EAGT6N,YAAS;WACA7N,OAAO,KAAKgJ,MAAN;;EAGf8E,aAAU;QACJ7E,YAAY,KAAKD,OAAO,CAAZ;UACVE,kBAAkB,KAAKF,OAAOvG,MAAM,CAAlB;WACjByG,gBAAgBrD,IAAKwC,WAAS;YAC7B0F,SAAShG,SAASM,OAAOY,SAAR;AACvBA,kBAAYZ;aACL0F;IAHF,CAAA;;EAOTC,YAAY3F,OAAY;UAChB4F,YAAY,KAAKjF,OAAOnD,IAAKqI,iBAAgBzF,SAASyF,aAAa7F,KAAd,CAAzC;WACXC,KAAK8B,IAAI,GAAG6D,SAAZ;;EAGTE,mBAAmBnF,QAAe;UAC1BoF,YAAYpF,OAAOtH,OAAO,CAACC,KAAK0G,UAAU1G,MAAM,KAAKqM,YAAY3F,KAAjB,GAAyB,CAA7D;WACX+F,YAAYpF,OAAOhJ;;AA1CH;ACDb,IAAOqO,YAAP,MAAgB;EAI5BtK,YAAYuK,QAAgBtJ,SAAiB;SACtCsJ,SAASA;SACTtJ,UAAUA;;AANW;ACE9B,SAASuJ,gBAAgB;EAAEC;EAAYxJ;EAASyJ;AAAvB,GAA+C;QAChEf,cAAeD,eAAD;;aAAuB,sBAACe,eAAD,QAACA,eAAD,SAAC,SAAAA,WAAY5K,QAAQ6J,SAApB,OAAA,QAAA,wBAAA,SAAA,sBAAkC,OAAO;EAArF;SACOzI,QAAQa,IAAI,CAAC2H,MAAMtN,UAAS;UAC3B8I,SAASyF,QAAQvO,KAAD,EAAQ2F,IAAK6I,eAAa;YACxC,CAACxG,GAAGC,CAAJ,IAASuG;aACR;QAAExG;QAAGC;MAAL;IAFM,CAAA;WAIR,IAAIoF,OAAOC,MAAMxE,QAAQ9I,OAAOwN,YAAYxN,KAAD,CAA3C;EALF,CAAA;;AASK,SAAUyO,cAAcL,QAAgBM,UAAuB;QACrE5J,UAAUuJ,gBAAgBK,QAAD;SACxB,IAAIP,UAAUC,QAAQtJ,OAAtB;;ACdT,IAAM6J,mBAAmB,CACvB;EAAE3G,GAAG;EAAGC,GAAG;AAAX,GACA;EAAED,GAAG;EAAMC,GAAG;AAAd,CAFuB;AAIzB,IAAM,CAAC2G,MAAMC,EAAP,IAAaF;AACnB,IAAMG,iBAAiBD,GAAG7G,IAAI4G,KAAK5G;AACnC,IAAM+G,kBAAkBF,GAAG5G,IAAI2G,KAAK3G;AAWtB,IAAO+G,aAAP,MAAiB;EAQ7BnL,YAAYE,SAA0B;UAC9B;MAAEkL;MAASC;MAAOC;IAAlB,IAA6BpL;SAC9BkL,UAAUA;SACVC,QAAQA;SACRC,SAASA;UAERC,iBAAiBF,QAAQ,IAAID;UAC7BI,kBAAkBF,SAAS,IAAIF;UAC/BK,SAASF,iBAAiBN;UAC1BS,SAASF,kBAAkBN;SAE5BjD,QAAQ1D,KAAK8B,IAAIoF,QAAQC,MAAjB;UAEPC,mBAAmBP,WAAWG,iBAAiB,KAAKtD,QAAQgD,kBAAkB;UAC9EW,mBACJR,WAAWI,kBAAkB,KAAKvD,QAAQiD,mBAAmB;SAE1DW,UAAU,KAAKd,KAAK5G,IAAI,KAAK8D,QAAQ0D;SACrCG,UAAU,KAAKf,KAAK3G,IAAI,KAAK6D,QAAQ2D;;EAG5CG,qBAAqBzH,OAAY;UACzBH,KAAKG,MAAMH,IAAI,KAAK0H,WAAW,KAAK5D;UACpC7D,KAAK,KAAKkH,SAAS,KAAKQ,UAAUxH,MAAMF,KAAK,KAAK6D;WACjD;MAAE9D;MAAGC;IAAL;;AAhCoB;ACJ/B,IAAM4H,8BAA8B;AACpC,IAAMC,+BAA+B;AACrC,IAAMC,oBAAoB;AAC1B,IAAMC,oBAAoB;AAWZ,SAAUC,cACtBC,YACApM,WACAyJ,WACAxJ,UAII,CAAA,GAAE;QAEAe,UAAUhB,UAAUgB;QACpBgE,SAASqH,gBAAgBD,WAAWpH,MAAZ;MAE1BA,OAAOhJ,SAAS,GAAG;WACd;MAAEsQ,SAAS;MAAOC,MAAM;QAAEC,mBAAmB;MAArB;IAAxB;;QAGH;IAAEF;IAASC;IAAME;EAAjB,IAA6BC,aAAa1H,QAAQhE,QAAQyI,SAAD,GAAaxJ,OAA7B;MAE3C,CAACqM,SAAS;WACL;MAAEA;MAASC;IAAX;EAZH;QAgBAI,eAAe3L,QAAQvC,MAAMgL,YAAY,CAA1B;MACjBmD,mBAAmBH;WAEdrP,IAAI,GAAGA,IAAIuP,aAAa3Q,QAAQoB,KAAK;UACtC;MAAEkP,SAAAA;MAASG,SAAAA;IAAX,IAAuBC,aAAa1H,QAAQ2H,aAAavP,CAAD,GAAK;MACjE,GAAG6C;MACH4M,gBAAgB;IAFiD,CAA1B;QAIrCP,YAAWG,WAAUG,kBAAkB;AACzCA,yBAAmBH;;EAzBjB;MA8BFG,mBAAmBH,SAAS;UAExBK,qBAAsB,OAAOF,mBAAmBH,YAAa,IAAIA;UACjE;MAAEH,SAAAA;MAASC,MAAAA;IAAX,IAAoBG,aAAa1H,QAAQhE,QAAQyI,SAAD,GAAa;MACjE,GAAGxJ;MACH8M,WAAW9M,QAAQ8M,YAAY,KAAKD;IAF6B,CAA7B;WAI/B;MAAER,SAAAA;MAASC,MAAAA;IAAX;;SAGF;IAAED;IAASC;EAAX;;AAGT,IAAMS,qBAAqB,CAAChI,QAAiBiI,eAAuBF,aAAoB;QAChFG,eAAezI,SAASwI,cAActD,iBAAd,GAAkC3E,OAAO,CAAD,CAAzC;QACvBmI,aAAa1I,SAASwI,cAAcrD,eAAd,GAAgC5E,OAAOA,OAAOhJ,SAAS,CAAjB,CAAvC;SAEzBkR,gBAAgBlB,+BAA+Be,YAC/CI,cAAcnB,+BAA+Be;AALjD;AAUA,IAAMK,iBAAkBpI,YAAmB;QACnCqI,UAAmB,CAAA;MACrBpI,YAAYD,OAAO,CAAD;AACtBA,SAAOvG,MAAM,CAAb,EAAgB0E,QAASkB,WAAS;AAChCgJ,YAAQ3K,KAAKqB,SAASM,OAAOY,SAAR,CAArB;AACAA,gBAAYZ;EAFd,CAAA;SAIOgJ;AAPT;AAUA,IAAMC,mBAAmB,CAACtI,QAAiBuI,WAAkB;QACrDC,cAAcJ,eAAepI,MAAD;QAC5ByI,gBAAgBF,OAAOzD,WAAP;QAChB4D,eAAeF,YAAY3L,IAAK8L,gBAAc;UAC5CC,qBAAqBH,cAAc5L,IAAKgM,kBAC5CzI,iBAAiByI,cAAcF,UAAf,CADS;WAGpBrJ,KAAK4B,IAAI,GAAG0H,kBAAZ;EAJY,CAAA;QAMfE,gBAAgBtQ,QAAQkQ,YAAD;SACtBI,gBAAgB/B;AAVzB;AAaA,IAAMgC,gBAAgB,CAAC/I,QAAiBuI,QAAgBR,aAAoB;SAEvEA,YAAY/Q,OAAOgJ,MAAD,IAAW,OAAQuI,OAAO1D,UAAP,IAAqB,OAAOqC;AAFtE;AAMA,IAAMG,kBAAmBrH,YAAmB;MACtCA,OAAOhJ,SAAS,EAAG,QAAOgJ;QACxB,CAACgJ,YAAY,GAAGC,IAAhB,IAAwBjJ;QACxBkJ,gBAAgB,CAACF,UAAD;aAEX3J,SAAS4J,MAAM;QACpB,CAACrJ,OAAOP,OAAO6J,cAAcA,cAAclS,SAAS,CAAxB,CAArB,GAAkD;AAC3DkS,oBAAcxL,KAAK2B,KAAnB;;;SAIG6J;AAXT;AAcA,IAAMC,sBAAsB,CAC1B7J,KAAK8J,KAAK,IACV9J,KAAK8J,KAAK,IACV,GACC,KAAK9J,KAAK8J,KAAM,IAChB,KAAK9J,KAAK8J,KAAM,EALS;AAQ5B,IAAMC,WAAW,CAAC3I,QAAiBC,QAAiBoH,aAAoB;QAChEuB,aAAa5G,eAAehC,MAAD;QAC3B6I,aAAa7G,eAAe/B,MAAD;MAC7B6I,UAAUC;AACdN,sBAAoBhL,QAASgF,WAAS;UAC9BhD,OAAOM,YAAY6I,YAAYpG,OAAOqG,YAAYpG,KAAb,CAAnB;QACpBhD,OAAOqJ,SAAS;AAClBA,gBAAUrJ;;EAHd,CAAA;SAMOqJ,WAAWvC,oBAAoBc;AAVxC;AAaA,IAAML,eAAe,CACnB1H,QACAuI,QACAtN,YAM2C;QACrC;IACJ8M,WAAW;IACX2B,mBAAmB;IACnB7B,iBAAiB;IACjB8B,2BAA2B;EAJvB,IAKF1O;QACEwM,UAAUc,OAAOpD,mBAAmBnF,MAA1B;QACV4J,UAAUF,oBAAoBnB,OAAO9D,YAAY,IAAI,MAAM;QAC3DoF,mBAAmBpC,WAAWkC,2BAA2BC,UAAU7B;MAErE,CAAC8B,kBAAkB;WACd;MAAEvC,SAAS;MAAOG;MAASF,MAAM;QAAEC,mBAAmB;MAArB;IAAjC;;QAEHsC,mBAAmB9B,mBAAmBhI,QAAQuI,QAAQR,QAAjB;QACrCgC,iBAAiBzB,iBAAiBtI,QAAQuI,MAAT;QACjCyB,aAAaX,SAASrJ,QAAQuI,OAAOvI,QAAQ+H,QAAxB;QACrBkC,cAAclB,cAAc/I,QAAQuI,QAAQR,QAAjB;QAE3BT,UACJuC,oBAAoBC,oBAAoBC,kBAAkBC,cAAcC;MAEtEpC,kBAAkB,CAACP,SAAS;UACxB4C,qBAAqBxC,aAAa,CAAC,GAAG1H,MAAJ,EAAYmK,QAAZ,GAAuB5B,QAAQ;MACrE,GAAGtN;MACH4M,gBAAgB;IAFqD,CAAhC;QAKnCqC,mBAAmB5C,SAAS;aACvB;QACLA;QACAG;QACAF,MAAM;UAAEC,mBAAmB;QAArB;MAHD;;;SAQJ;IAAEF;IAASG;IAASF,MAAM;MAAEC,mBAAmB;IAArB;EAA1B;AA9CT;ACzJc,IAAO4C,aAAP,MAAiB;EAK7BrP,YAAYsP,IAAYC,eAAsBC,uBAA4B;SACnEF,KAAKA;SACLrK,SAAS,CAACsK,aAAD;SACTE,iBAAiB,CAACD,qBAAD;;EAGxBE,YAAYpL,OAAcqL,eAAoB;SACvC1K,OAAOtC,KAAK2B,KAAAA;SACZmL,eAAe9M,KAAKgN,aAAAA;;AAbE;ACqB/B,IAAMC,QAAN,MAAW;EAST5P,YAAY6P,UAAgB;SACrBC,YAAYD;SACZE,aAAa;SACbC,UAAU;SACVjT,QAAAA,SAAiB8S,QAAAA;;EAGxBlO,MAAG;SACIoO,aAAa1U,eAAc;SAC3B4U,kBAAkB,IAAIhO,QAASC,aAAW;WACxCI,WAAWJ;WAEXgO,WAAWtU,WAAW,MAAM,KAAKmI,OAAL,GAAe,KAAK+L,SAA3B;IAHL,CAAA;WAKhB,KAAKG;;EAGd3M,QAAK;QACC,KAAK0M,QAAS;UAEZG,eAAe7U,YAAYC,IAAZ,KAAqB,KAAKwU,cAAc;SACxDD,YAAYvL,KAAK4B,IAAI,GAAG,KAAK2J,YAAYK,YAA7B;AACjBrU,iBAAa,KAAKoU,QAAN;SACPF,UAAU;;EAGjBxM,SAAM;QACA,CAAC,KAAKwM,QAAS;SACdD,aAAazU,YAAYC,IAAZ;SAEb2U,WAAWtU,WAAW,MAAM,KAAKmI,OAAL,GAAe,KAAK+L,SAA3B;SACrBE,UAAU;;EAGjBjM,SAAM;AACJjI,iBAAa,KAAKoU,QAAN;QACR,KAAK5N,UAAU;WACZA,SAAAA;;SAEFA,WAAW8N;;AAhDT;AAyDG,IAAOC,WAAP,MAAe;;;;;;EA2B3BrQ,YACEjD,OACAuT,kBACApQ,UAII,CAAA,GAAE;SAoDAqQ,QAASC,YAAkB;UAC7B,KAAKC,oBAAoB,MAAM;;;YAI7BC,WAAWnM,KAAK8B,IACpB,IACCmK,SAAS,KAAKT,aAAc,KAAKY,mBAAmB,KAAKb,SAF3C;UAKbY,aAAa,GAAG;aACbE,aAAcpP,YAAY,KAAKqP,OAAAA;aAC/BC,eAAeV;aACfrM,OAAO,KAAK6M,YAAAA;MAHnB,OAIO;cACCG,gBAAgBC,KAAKN,QAAD;cACpBjP,eAAewP,iBACnB,KAAKC,aACL,KAAKL,SACLE,aAHmC;aAMhCH,aAAcpP,YAAYC,YAAAA;aAC1BqP,eAAerV,sBAAsB,KAAK8U,KAAN;;IAvBrC;SAlDDxT,QAAQA;SACRoU,oBAAoBb;SACpBR,YAAY5P,QAAQ2P,YAAY;SAChCuB,SAASlR,QAAQmR;SACjBV,kBAAkB;SAClBF,kBAAkB;;EAGzB9O,IAAI2P,aAA8B;QAC5B,CAAC,KAAKT,QAAS,MAAKU,eAAeD,WAApB;QACf,KAAKxB,cAAc,EAAGwB,aAAY9P,YAAY,KAAKqP,OAA7B;QACtB,KAAKf,cAAc,KAAK0B,eAAeF,YAAYhR,OAAO,KAAKuQ,OAAzB,GAAmC;aACpE5O,QAAQC,QAAR;;SAEJ0O,eAAeU;SACfJ,cAAcI,YAAYhR;SAC1ByP,aAAazU,YAAYC,IAAZ;SACbuV,eAAerV,sBAAsB,KAAK8U,KAAN;WAClC,IAAItO,QAASC,aAAW;WACxBI,WAAWJ;IADX,CAAA;;EAKDqP,eAAeD,aAA8B;QAC/CG,SAAS,KAAKN;QACd,OAAO,KAAKA,sBAAsB,YAAY;AAChDM,eAAS,KAAKN,kBAAkBG,YAAYhR,KAAnC;;SAENuQ,UAAU/T,QAAQ,KAAKC,OAAO0U,MAAb;;EAGxBnO,QAAK;QACC,KAAKmN,oBAAoB,MAAM;;;QAG/B,KAAKK,cAAc;AACrBjV,2BAAqB,KAAKiV,YAAN;;SAEjBL,kBAAkBnV,YAAYC,IAAZ;;EAGzBiI,SAAM;QACA,KAAKiN,oBAAoB,MAAM;;;SAG9BK,eAAerV,sBAAsB,KAAK8U,KAAN;SACpCI,mBAAmBrV,YAAYC,IAAZ,IAAoB,KAAKkV;SAC5CA,kBAAkB;;EA8BzB1M,OAAOuN,aAA8B;;2BAC9BhP,cAAAA,QAAAA,mBAAAA,SAAAA,SAAAA,eAAAA,KAAAA,IAAAA;SACAA,WAAW8N;AAEhBvU,yBAAqB,KAAKiV,gBAAgB,EAAtB;SACfA,eAAeV;QAEhB,KAAKgB,QAAQ;UACX,CAAC,KAAKP,QAAS,MAAKU,eAAeD,WAApB;AACnBA,kBAAY9P,YAAY,KAAKqP,OAA7B;;;AA1HuB;AAIpBR,SAAAT,QAAQA;AA2HjB,SAASqB,iBACPS,aACAC,WACAjB,UAAgB;QAEVkB,SAA8B,CAAA;aAEzBnV,OAAOkV,WAAW;UACrBE,WAAWF,UAAUlV,GAAD;UACpBqV,aAAaJ,gBAAH,QAAGA,gBAAH,SAAG,SAAAA,YAAcjV,GAAH;QAC1B,OAAOqV,eAAe,YAAY,OAAOD,aAAa,YAAYA,YAAY,GAAG;AACnFD,aAAOnV,GAAD,IAAQiU,YAAYmB,WAAWC,cAAcA;IADrD,OAEO;AACLF,aAAOnV,GAAD,IAAQwU,iBAAiBa,YAAYD,UAAUnB,QAAvB;;;SAG3BkB;;AAGT,SAASJ,eACPE,aACAC,WAA0C;aAE/BlV,OAAOkV,WAAW;UACrBE,WAAWF,UAAUlV,GAAD;UACpBqV,aAAaJ,gBAAH,QAAGA,gBAAH,SAAG,SAAAA,YAAcjV,GAAH;QAC1BoV,YAAY,GAAG;UACbA,aAAaC,YAAY;eACpB;;IAFX,WAIW,CAACN,eAAeM,YAAYD,QAAb,GAAwB;aACzC;;;SAGJ;;AAIT,IAAMb,OAAQ7M,OAAc,CAACI,KAAK8D,IAAIlE,IAAII,KAAK8J,EAAlB,IAAwB,IAAI;AC9OlD,IAAM0D,cAAc,CACzBC,UACA/R,WACA4P,aACqB;SACd,CACL,IAAIQ,SAAJ,aACe2B,QAAAA,YACb5S,UACE;IAAE2B,SAAS;IAAGO,gBAAgB;EAA9B,GACArB,UAAUgB,QAAQhF,MAFX,GAIT;IAAE4T;IAAUwB,OAAO;EAAnB,CANF,CADK;AALF;AAiBA,IAAMrQ,gBAAgB,CAC3BgR,UACA/R,WACA4P,aACqB;SACd,CACL,IAAIQ,SAAJ,aACe2B,QAAAA,IACb;IACEjR,SAAS;IACTE,SAAS7B,UAAU;MAAE2B,SAAS;MAAGO,gBAAgB;IAA9B,GAAmCrB,UAAUgB,QAAQhF,MAAtD;EAFpB,GAIA;IAAE4T;IAAUwB,OAAO;EAAnB,CANF,CADK;AALF;AAiBA,IAAMY,gBAAgB,CAC3BD,UACA/R,WACA4P,aACqB;SACd,CACL,IAAIQ,SAAJ,aAA0B2B,QAAAA,YAAoB,GAAG;IAAEnC;IAAUwB,OAAO;EAAnB,CAAjD,GACA,GAAGU,YAAYC,UAAU/R,WAAW,CAAtB,CAFT;AALF;AAWA,IAAMiS,cAAc,CACzBC,WACAC,UACAvC,aACE;SACK,CAAC,IAAIQ,SAAJ,WAAwB8B,SAAAA,IAAaC,UAAU;IAAEvC;EAAF,CAA/C,CAAD;AALF;AAQA,IAAMwC,kBAAkB,CAC7B7E,QACA8E,OACAC,UACqB;QACf7I,YAAY8D,OAAO9D;QACnBmG,YAAYrC,OAAO1D,UAAP,IAAqB,QAAQ,IAAIyI;SAC5C,CACL,IAAIlC,SAAS,0BAA0BiC,KAAvC,GACA,IAAIjC,SAAS,uBAAuB;IAClCtP,SAAS;IACTE,SAAS;OACNyI,SAAAA,GAAY;QACXpI,gBAAgB;QAChBP,SAAS;MAFE;IADN;EAFyB,CAApC,GASA,IAAIsP,SAAJ,+BACiC3G,SAAAA,IAC/B;IACEpI,gBAAgB;IAChBP,SAAS;EAFX,GAIA;IAAE8O;EAAF,CANF,GAQA,IAAIQ,SAAJ,+BAA4C3G,SAAAA,YAAqB,GAAG;IAClEmG;IACAwB,OAAO;EAF2D,CAApE,CAnBK;AAPF;AAiCA,IAAMmB,gBAAgB,CAC3BR,UACAxE,QACA+E,UACqB;QACf7I,YAAY8D,OAAO9D;QACnBmG,YAAYrC,OAAO1D,UAAP,IAAqB,QAAQ,IAAIyI;SAC5C,CACL,IAAIlC,SAAJ,aAA0B2B,QAAAA,IAAY;IACpCjR,SAAS;IACTE,SAAS;OACNyI,SAAAA,GAAY;QACXpI,gBAAgB;QAChBP,SAAS;MAFE;IADN;EAF2B,CAAtC,GASA,IAAIsP,SAAJ,aAA0B2B,QAAAA,YAAoBtI,SAAAA,mBAA4B,GAAG;IAC3EmG;EAD2E,CAA7E,CAVK;AAPF;AAuBA,IAAM4C,sBAAsB,CACjCT,UACA/R,WACAyJ,WACA6I,UACqB;QACfG,oBAAqBpS,WAA4B;UAC/CqS,eAAerS,MAAML,UAAU+R,QAAhB;UACfY,gBAAwD;MAC5D7R,SAAS;MACTE,SAAS,CAAA;IAFmD;aAIrD5D,IAAI,GAAGA,IAAI4C,UAAUgB,QAAQhF,QAAQoB,KAAK;AACjDuV,oBAAc3R,QAAS5D,CAAvB,IAA4B;QAC1B0D,SAAS4R,aAAa5R,UAAU4R,aAAa1R,QAAQ5D,CAArB,EAAwB0D;MAD9B;;WAIvB6R;EAXT;QAaMpF,SAASvN,UAAUgB,QAAQyI,SAAlB;SACR,CACL,IAAI2G,SAAJ,aAA0B2B,QAAAA,IAAYU,iBAAtC,GACA,GAAGF,cAAcR,UAAUxE,QAAQ+E,KAAnB,CAFX;AApBF;AA0BA,IAAMM,aAAa,CACxBb,UACAtI,WACAmG,aACqB;SACd,CACL,IAAIQ,SAAJ,aACe2B,QAAAA,YAAoBtI,SAAAA,IACjC;IACEpI,gBAAgB;IAChBP,SAAS;EAFX,GAIA;IAAE8O;IAAUwB,OAAO;EAAnB,CANF,CADK;AALF;AAiBA,IAAMyB,mBAAmB,CAC9Bd,UACA/R,WACA8S,cACAR,OACAS,wBACqB;MACjBpR,YAA+BqQ,cAAcD,UAAU/R,WAAW8S,YAAtB;AAChDnR,cAAYA,UAAUqR,OAAOlB,YAAYC,UAAU/R,WAAW,CAAtB,CAA5B;AACZ2B,YAAUe,KACR,IAAI0N,SAAJ,aACe2B,QAAAA,IACb;IACEjR,SAAS;IACTE,SAAS7B,UAAU;MAAE2B,SAAS;IAAX,GAAgBd,UAAUgB,QAAQhF,MAAnC;EAFpB,GAIA;IAAEoV,OAAO;EAAT,CANF,CADF;AAUApR,YAAUgB,QAAQmC,QAAQ,CAACoK,QAAQnQ,MAAK;QAClCA,IAAI,EAAGuE,WAAUe,KAAK,IAAI0N,SAAST,MAAMoD,mBAAnB,CAAf;AACXpR,gBAAYA,UAAUqR,OAAOT,cAAcR,UAAUxE,QAAQ+E,KAAnB,CAA9B;EAFd,CAAA;SAIO3Q;AAvBF;AA0BA,IAAMsR,uBAAuB,CAClClB,UACA/R,WACA8S,cACAR,OACAS,qBACAG,sBACqB;QACfvR,YAAYkR,iBAChBd,UACA/R,WACA8S,cACAR,OACAS,mBALgC;AAOlCpR,YAAUe,KAAK,IAAI0N,SAAST,MAAMuD,iBAAnB,CAAf;SACOvR;AAhBF;ACnLA,IAAMwR,YAAY,CACvBnT,WACA8S,cACAM,mBACqB;SACd,CACL,GAAGC,cAA+B,QAAQrT,WAAW8S,YAAlD,GACH,IAAI1C,SACF,uBACA;IACEtP,SAAS;IACTE,SAAS7B,UAAU;MAAE2B,SAAS;IAAX,GAAgBd,UAAUgB,QAAQhF,MAAnC;EAFpB,GAIA;IAAEoV,OAAO;EAAT,CANF,GAQA,IAAIhB,SACF,kBACA;IACEtP,SAAS;IACTE,SAAS1B,YAAYU,UAAUgB,QAAQhF,QAASoB,QAAO;MACrD0D,SAAS1D,IAAIgW,iBAAiB,IAAI;IADmB,EAAnC;EAFtB,GAMA;IAAEhC,OAAO;EAAT,CARF,CAVK;AALF;AA4BA,IAAMkC,kBAAkB,CAACjE,IAAqBhL,UAAmC;SAC/E,CACL,IAAI+L,SAAS,2BAA2Bf,IAAI;IAAE+B,OAAO;EAAT,CAA5C,GACA,IAAIhB,SAAJ,eACiBf,EAAAA,IACf;IACErK,QAAQ,CAACX,KAAD;IACRvD,SAAS;EAFX,GAIA;IAAEsQ,OAAO;EAAT,CANF,CAFK;AADF;AAcA,IAAMmC,mBAAmB,CAC9BC,cACAxO,WACqB;SACd,CAAC,IAAIoL,SAAJ,eAA4BoD,YAAAA,WAAuBxO,QAAQ;IAAEoM,OAAO;EAAT,CAA3D,CAAD;AAJF;AAOA,IAAMqC,iBAAiB,CAC5BD,cACA5D,aACqB;SACd,CACL,IAAIQ,SAAJ,eAA4BoD,YAAAA,YAAwB,GAAG;IAAE5D;EAAF,CAAvD,CADK;AAJF;AAcA,IAAM8D,uBAAwBC,mBAAmD;UAC/EA,kBAAa,QAAbA,kBAAa,SAAb,SAAAA,cAAe9R,IAAI2R,kBACxB,IAAIpD,SAAJ,eAA4BoD,YAAAA,IAAgB,MAAM;IAAEpC,OAAO;EAAT,CAAlD,CADK,MAEF,CAAA;AAHA;AAMA,IAAMwC,wBAAwB,CACnC5T,WACAqS,OACAzC,aACqB;SACd,CACL,IAAIQ,SAAS,0BAA0BiC,KAAvC,GACA,GAAGgB,cAA+B,aAAarT,SAA5C,GACH,GAAGqT,cAA+B,aAAarT,WAAW4P,WAAW,CAAlE,GACH,GAAGyD,cAA+B,aAAarT,WAAW4P,WAAW,CAAlE,CAJE;AALF;AC/DP,IAAMiE,eAAgBzH,iBAA4B;EAChDnD,YAAY6K,cAAuB1H,WAAWoD,cAAlC;EACZxK,QAAQoH,WAAWpH,OAAOnD,IAAKwC,WAAUyP,MAAezP,KAAf,CAAjC;AAFwC;AAKpC,IAAO0P,OAAP,MAAW;EAcvBhU,YAAYC,WAAsBqR,aAA0B2C,YAAsB;SANlFC,sBAAsB;SACtBC,oBAAoB;SACpBC,iBAAiB;SAKVC,aAAapU;SACb2Q,eAAeU;SACflP,YAAY;SACZkS,cAAcL;;EAGrBb,UAAUlT,SAAiC;QACrC,KAAKqU,iBAAiB;WACnB3D,aAAajP,IAChB6S,qBAAkC,KAAKD,eAAvC,CAAA;;SAGCA,kBAAkB,CAAA;SAElBnS,YAAY;SACZqS,WAAWvU;UACVwU,aAAaxY,SACjBgE,QAAQyU,oBACR,KAAKN,WAAWpT,QAAQhF,MAFC;SAItBiY,sBAAsB3P,KAAK8B,IAAIqO,YAAY,KAAKL,WAAWpT,QAAQhF,SAAS,CAAtD;SACtBkY,oBAAoB;SACpBC,iBAAiB;WAEf,KAAKxD,aAAajP,IACvB6S,UACE,KAAKH,YACLnU,QAAQ0U,oBACR,KAAKV,mBAHP,CADK;;EASTX,gBAAgB5D,eAAoB;;QAC9B,CAAC,KAAKvN,WAAW;aACZ;;QAEL,KAAKyS,aAAa;aACb,KAAKC,cAAL;;UAEHxQ,QAAQ,KAAKgQ,YAAYvI,qBAAqB4D,aAAtC;UACRoF,WAAWvX,QAAO;SACnBqX,cAAc,IAAIxF,WAAW0F,UAAUzQ,OAAOqL,aAAhC;kCACd4E,qBAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAiB5R,KAAKoS,QAAAA;WACpB,KAAKnE,aAAajP,IAAI6S,gBAA4BO,UAAUzQ,KAAtC,CAAtB;;EAGT0Q,mBAAmBrF,eAAoB;QACjC,CAAC,KAAKkF,aAAa;aACd5S,QAAQC,QAAR;;UAEHoC,QAAQ,KAAKgQ,YAAYvI,qBAAqB4D,aAAtC;SACTkF,YAAYnF,YAAYpL,OAAOqL,aAAAA;UAC9BsF,aAAa,KAAKJ,YAAY5P,OAAOvG,MAAM,CAA9B;WACZ,KAAKkS,aAAajP,IACvB6S,iBAA6B,KAAKK,YAAYvF,IAAI2F,UAAlD,CADK;;EAKTC,cAAcjB,YAAsB;SAC7BK,cAAcL;;EAGrBa,gBAAa;;QACP,CAAC,KAAKD,YAAa;SAElBjE,aAAajP,IAChB6S,eACE,KAAKK,YAAYvF,KADnB,wBAEE,KAAKmF,SAAUlU,yBAAAA,QAAAA,0BAAAA,SAAAA,wBAAuB,GAFxC,CAAA;QAOE,KAAKsU,YAAY5P,OAAOhJ,WAAW,GAAG;WACnC4Y,cAAczE;;;UAIf;MAAE+E;MAAwBC;IAA1B,IAA2D,KAAKX;UAEhEY,gBAAgB,KAAKC,kBAAL;UAChB;MAAE/I;MAASC;IAAX,IAAoBJ,cACxB,KAAKyI,aACL,KAAKR,YACL,KAAKH,qBACL;MACEvF,kBAAkB,KAAKiC,aAAatQ,MAAML,UAAUiB,QAAQH,UAAU;MACtEiM,UAAU,KAAKyH,SAAUzH;MACzB4B,0BAA0B,KAAK6F,SAAU7F;IAH3C,CAJqC;UAYjC2G,kBACJH,gCACA,KAAKjB,oBAAoB,KAAKiB;UAE1BI,aACJjJ,WAAWgJ,mBAAoB/I,KAAKC,qBAAqB0I;QAEvDK,YAAY;WACTC,eAAejJ,IAAAA;IADtB,OAEO;WACAkJ,eAAelJ,IAAAA;YAEd;QACJmJ;QACA9U;QACA+U;MAHI,IAIF,KAAKnB;UAGPkB,wBAAwB,SACxB,KAAKxB,qBAAqBwB,qBAC1B;aACK/E,aAAajP,IAChB2R,gBACE+B,eACAvX,kBAAkB+C,cAAD,GACjB+U,oBAHF,CAAA;;;SASDf,cAAczE;;EAGrBrM,SAAM;SACC3B,YAAY;QACb,KAAKmS,iBAAiB;WACnB3D,aAAajP,IAChB6S,qBAAkC,KAAKD,eAAvC,CAAA;;;EAKNsB,eAAe;IACbC;IACAtJ;EAFa,GAMd;WACQ;MACLvM,WAAW,KAAKoU,WAAW9J;MAC3Bb,WAAW,KAAKwK;MAChB6B,kBAAkB,KAAK5B;MACvB6B,eAAe,KAAK5B;MACpB6B,kBACE,KAAK5B,WAAWpT,QAAQhF,SAAS,KAAKiY,uBAAuB4B,YAAY,IAAI;MAC/EI,WAAWpC,aAAa,KAAKe,WAAN;MACvBsB,aAAa3J,KAAKC;IARb;;EAYT2J,aAAU;QACJ,CAAC,KAAK3B,SAAU;UAEd;MAAExT;MAASsJ;IAAX,IAAsB,KAAK8J;UAE3B;MACJgC;MACAC;MACA1B;MACA2B;MACA1V;MACA2V;IANI,IAOF,KAAK/B;QAELgC,YAA+BnD,WACjC,QACA,KAAKY,qBACLU,kBAHiC;SAM9BT,oBAAoB;SACpBD,uBAAuB;UAEtBwC,aAAa,KAAKxC,wBAAwBjT,QAAQhF;QAEpDya,YAAY;WACTtU,YAAY;AACjBiU,qBAAU,QAAVA,eAAU,SAAV,SAAAA,WAAa;QACXpW,WAAWsK;QACXyL,eAAe,KAAK5B;MAFT,CAAH;UAINkC,qBAAqB;AACvBG,oBAAYA,UAAUxD,OACpBuB,sBACE,KAAKH,YACLvW,kBAAkByY,0BAA0B1V,cAA3B,IAChB2V,2BAA2B,KAAK,CAHnC,CADU;;;SAUX5F,aAAajP,IAAI8U,SAAAA;;EAGxBhB,eAAejJ,MAA2B;QACpC,CAAC,KAAKiI,SAAU;UAEd;MAAEkC;IAAF,IAAsB,KAAKlC;AAEjCkC,wBAAe,QAAfA,oBAAe,SAAf,SAAAA,gBAAkB;MAChB,GAAG,KAAKd,eAAe;QAAEC,WAAW;QAAMtJ;MAAnB,CAApB;IADa,CAAH;SAIV4J,WAAAA;;EAGPV,eAAelJ,MAA2B;;SACnC2H,qBAAqB;SACrBC,kBAAkB;oDAClBK,UAAUmC,eAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAAA,KAAAA,gBAAY,KAAKf,eAAe;MAAEC,WAAW;MAAOtJ;IAApB,CAApB,CAAA;;EAG7B8I,oBAAiB;WACR,KAAKjB,WAAWpT,QAAQ,KAAKiT,mBAA7B;;AA5Oc;ACjBnB,SAAU2C,UAAUC,SAAe;SAChCC,SAASC,gBAAgB,8BAA8BF,OAAvD;;AAGH,SAAUG,KAAKC,KAAcC,MAAcC,OAAa;AAC5DF,MAAIG,eAAe,MAAMF,MAAMC,KAA/B;;AAGI,SAAUE,MAAMJ,KAAcK,UAAgC;AAClEC,SAAOC,KAAKF,QAAZ,EAAsBnU,QAASsU,cAAaT,KAAKC,KAAKQ,UAAUH,SAASG,QAAD,CAAxB,CAAhD;;AAII,SAAUC,SAASrI,IAAU;MAC7BsI,SAAS;MACTzc,OAAO0c,YAAY1c,OAAO0c,SAASC,MAAM;AAC3CF,aAASzc,OAAO0c,SAASC,KAAK3Y,QAAQ,WAAW,EAAxC,EAA4CA,QAAQ,OAAO,KAA3D;;iBAEIyY,MAAAA,IAAUtI,EAAAA;;AAGrB,SAAUyI,UAAUb,KAAwB;;AAChDA,UAAG,QAAHA,QAAG,SAAH,UAAA,kBAAAA,IAAKc,gBAAAA,QAAAA,oBAAAA,SAAAA,SAAAA,gBAAYC,YAAYf,GAAAA;;ACnBjB,IAAOgB,qBAAP,MAAOA,oBAAkB;EAKrClY,YAAYwN,QAAc;SACnBA,SAASA;SACT2K,cAAc3K,OAAO1D,UAAP,IAAqBoO,oBAAmBE,eAAe;;EAG5EC,qBAAqB/W,gBAAsB;WAClC,KAAK6W,cAAc,SAAS,IAAI7W;;EAGzCgX,UAAU;IACR5X;IACAE;EAFQ,GAMT;WACQA,gBAAgB,KAAK4M,OAAO7D,cAAc/I,eAAeF;;AArB7B;AAG9BwX,mBAAAE,eAAe;ACExB,IAAMA,eAAe;AAUP,IAAOG,iBAAP,cAA8BL,mBAAkB;EAO5DlY,YAAYwN,QAAc;UAClBA,MAAAA;SAPRgL,YAA2CpI;;EAU3CqI,MAAM7G,QAAuB;SACtB8G,iBAAiBC,UAAc,MAAd;SACjBC,QAAQD,UAAc,UAAd;SACRE,cAAcF,UAAc,MAAd;UACbG,SAAM,QAAWtb,QAAO,CAAA;AAC9Bmb,SAAS,KAAKC,OAAO,MAAME,MAA3B;AAEAH,SAAS,KAAKE,aAAa,KAAK,KAAKrL,OAAO/D,IAA5C;SACKiP,eAAeK,MAAMhY,UAAU;AACpC4X,SAAS,KAAKD,gBAAgB,aAAaC,SAAaG,MAAb,CAA3C;UAEME,qBAAqB5P,YAAY,KAAKoE,OAAOvI,QAAQmT,eAAe,CAApC;AACtCO,SAAS,KAAKD,gBAAgB,KAAK5P,cAAckQ,kBAAD,CAAhD;AACAL,UAAU,KAAKD,gBAAgB;MAC7BlL,QAAQ;sBACQ4K,aAAaa,SAAb;MAChBC,MAAM;wBACY;yBACC;6BACI,KAAKf,WAAAA,IAAe,KAAKA,WAAAA;IANnB,CAA/B;SASKS,MAAMO,YAAY,KAAKN,WAAAA;AAC5BjH,WAAOwH,KAAKD,YAAY,KAAKP,KAA7B;AACAhH,WAAO+G,IAAIQ,YAAY,KAAKT,cAA5B;WACO;;EAGTW,OAAOC,OAAwB;;QACzBA,UAAU,KAAKd,aAAa,CAAC,KAAKE,gBAAgB;;;QAIlDY,MAAMhY,qBAAN,kBAAyB,KAAKkX,eAAAA,QAAAA,oBAAAA,SAAAA,SAALe,gBAAgBjY,iBAAgB;WACtDoX,eAAeK,MAAMS,mBAAmB,KAAKnB,qBAChDiB,MAAMhY,cADqC,EAE3C2X,SAF2C;;UAKzC3G,QAAQ,KAAKgG,UAAUgB,KAAf;QAEV,CAAC,KAAKd,aAAalG,UAAU,KAAKgG,UAAU,KAAKE,SAApB,GAAgC;YACzD;QAAEha;QAAGG;QAAGC;QAAGC;MAAX,IAAiByT;AACvBqG,YAAU,KAAKD,gBAAgB;QAAElL,QAAM,QAAUhP,CAAAA,IAAKG,CAAAA,IAAKC,CAAAA,IAAKC,CAAAA;MAAjC,CAA/B;;QAGEya,MAAMvY,cAAN,mBAAkB,KAAKyX,eAAAA,QAAAA,qBAAAA,SAAAA,SAALiB,iBAAgB1Y,UAAS;WACxC2X,eAAeK,MAAMhY,UAAUuY,MAAMvY,QAAQkY,SAAd;;SAEjCT,YAAYc;;AA5DyC;ACJhD,IAAOI,oBAAP,MAAwB;EAOpC1Z,YAAYC,WAAoB;SANhCuY,YAAiDpI;SAO1CuJ,mBAAmB1Z,UAAUgB,QAAQa,IAAK0L,YAAW,IAAI+K,eAAe/K,MAAnB,CAAlC;;EAG1BiL,MAAM7G,QAAuB;UACrBgI,YAAYhI,OAAOiI,sBAAP;SACbC,SAASF,UAAUjB;SACnBgB,iBAAiBvW,QAAS2W,oBAAkB;AAC/CA,qBAAetB,MAAMmB,SAArB;IADF,CAAA;;EAKFP,OAAOC,OAA8B;;QAC/BA,UAAU,KAAKd,aAAa,CAAC,KAAKsB,QAAQ;;;UAGxC;MAAE/Y;MAASE;MAASP;MAAaE,eAAe;IAAhD,IAAyD0Y;QAC3DvY,cAAO,kBAAK,KAAKyX,eAAAA,QAAAA,oBAAAA,SAAAA,SAALe,gBAAgBxY,UAAS;WAClC+Y,OAAOf,MAAMhY,UAAUA,QAAQkY,SAAR;UAIxB,CAACrZ,aAAa;;YACZmB,YAAY,GAAG;eACZ+Y,OAAOf,MAAMiB,UAAU;QAD9B,aAEW,mBAAA,KAAKxB,eAAAA,QAAAA,qBAAAA,SAAAA,SAAAA,iBAAWzX,aAAY,GAAG;eACnC+Y,OAAOf,MAAMkB,eAAe,SAAA;;;;UAIjCC,gBACJ,CAAC,KAAK1B,aACN9X,gBAAgB,KAAK8X,UAAU9X,eAC/BE,iBAAiB,KAAK4X,UAAU5X;QAE9BsZ,iBAAiBjZ,cAAO,mBAAK,KAAKuX,eAAAA,QAAAA,qBAAAA,SAAAA,SAAL2B,iBAAgBlZ,UAAS;eAC/C5D,IAAI,GAAGA,IAAI,KAAKsc,iBAAiB1d,QAAQoB,KAAK;;YAEnD,CAAC6c,kBAAD,mBACA,KAAK1B,eAAAA,QADL,qBAAA,UACA4B,iBAAgBnZ,WAChBA,QAAQ5D,CAAD,MAAQ,KAAKmb,UAAUvX,QAAQ5D,CAAvB,GACf;;;aAGGsc,iBAAiBtc,CAAAA,EAAGgc,OAAO;UAC9B3Y;UACAE;UACAG,SAASE,QAAQ5D,CAAD,EAAI0D;UACpBO,gBAAgBL,QAAQ5D,CAAD,EAAIiE;QAJG,CAAA;;;SAQ/BkX,YAAYc;;AA3DiB;ACFxB,IAAOe,qBAAP,MAAyB;EAAvCra,cAAA;SACEwY,YAAyCpI;;EAGzCqI,MAAM7G,QAAuB;SACtB0I,QAAQ3B,UAAc,MAAd;AACb/G,WAAO+G,IAAIQ,YAAY,KAAKmB,KAA5B;;EAGFjB,OAAOC,OAAsB;;QACvB,CAAC,KAAKgB,SAAShB,UAAU,KAAKd,WAAW;;;QAI3Cc,MAAM5Y,kBAAN,kBAAsB,KAAK8X,eAAAA,QAAAA,oBAAAA,SAAAA,SAALe,gBAAgB7Y,gBACtC4Y,MAAMiB,kBAAN,mBAAsB,KAAK/B,eAAAA,QAAAA,qBAAAA,SAAAA,SAALiB,iBAAgBc,cACtC;YACM;QAAE/b;QAAGG;QAAGC;QAAGC;MAAX,IAAiBya,MAAM5Y;AAC7BiY,YAAU,KAAK2B,OAAO;QACpBpB,MAAM;QACN1L,QAAM,QAAUhP,CAAAA,IAAKG,CAAAA,IAAKC,CAAAA,IAAKC,CAAAA;wBACfya,MAAMiB,YAAYtB,SAAlB;0BACE;2BACC;MALC,CAAtB;;QAQEK,MAAMvY,cAAN,mBAAkB,KAAKyX,eAAAA,QAAAA,qBAAAA,SAAAA,SAAL2B,iBAAgBpZ,UAAS;AAC7C4X,WAAS,KAAK2B,OAAO,WAAWhB,MAAMvY,QAAQkY,SAAd,CAAhC;;QAEEK,MAAMrU,aAAN,mBAAiB,KAAKuT,eAAAA,QAAAA,qBAAAA,SAAAA,SAAL4B,iBAAgBnV,SAAQ;AAC3C0T,WAAS,KAAK2B,OAAO,KAAKxR,cAAcwQ,MAAMrU,MAAP,CAAvC;;SAEGuT,YAAYc;;EAGnBkB,UAAO;AACL7B,cAAc,KAAK2B,KAAnB;;AApCmC;ACHzB,IAAOG,sBAAP,MAA0B;EAUtCza,YAAYC,WAAsBgU,YAAsB;SACjDI,aAAapU;SACbqU,cAAcL;SACdyG,oBAAoB,IAAIhB,kBAAkBzZ,SAAtB;SACpB0a,uBAAuB,IAAIjB,kBAAkBzZ,SAAtB;SACvB2a,yBAAyB,IAAIlB,kBAAkBzZ,SAAtB;SACzB4a,uBAAuB,CAAA;;EAG9BpC,MAAM7G,QAAuB;UACrBkJ,mBAAmBlJ,OAAOiI,sBAAP;UACnBkB,QAAQD,iBAAiBnC;UACzB;MAAE9M;MAASC;MAASR;MAAQrD;IAA5B,IAAsC,KAAKqM;AAEjDqE,SACEoC,OACA,aAFF,aAGelP,OAAAA,KAAYP,SAASQ,OAAAA,WAAkB7D,KAAAA,KAAU,KAAKA,KAAAA,GAHrE;SAKK0S,qBAAqBlC,MAAMqC,gBAAAA;SAC3BJ,kBAAkBjC,MAAMqC,gBAAAA;SACxBF,uBAAuBnC,MAAMqC,gBAAAA;SAC7BE,oBAAoBF;;EAG3BzB,OAAOC,OAAwB;UACvB;MAAExY;MAAMI;MAASE;IAAjB,IAA+BkY,MAAMrZ;UACrC;MACJU;MACAC;MACAC;MACAH;MACAF;MACAC;IANI,IAOF6Y,MAAMpZ;SAELya,qBAAqBtB,OAAO;MAC/BtY,SAASG,QAAQH;MACjBE,SAASC,QAAQD;MACjBP,aAAaC;IAHkB,CAAA;SAM5B+Z,kBAAkBrB,OAAO;MAC5BtY,SAASD,KAAKC;MACdE,SAASH,KAAKG;MACdP;MACAE;IAJ4B,CAAA;SAOzBga,uBAAuBvB,OAAO;MACjCtY,SAASK,UAAUL;MACnBE,SAASG,UAAUH;MACnBP,aAAaG;IAHoB,CAAA;UAM7BQ,cAAciY,MAAMjY,eAAe,CAAA;eAE9BoS,gBAAgB,KAAKoH,sBAAsB;UAChD,CAACxZ,YAAYoS,YAAD,GAAgB;;sCACzBoH,qBAAqBpH,YAAAA,OAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAe+G,QAAAA;eAClC,KAAKK,qBAAqBpH,YAA1B;;;eAIAA,gBAAgBpS,aAAa;YAChCmM,SAASnM,YAAYoS,YAAD;UACtB,CAACjG,QAAQ;;;YAGPyN,kBAAmC;QACvCV,aAAa/Z;QACbE,aAAaD;WACV+M;MAHoC;YAMnCuM,kBAAkB,MAAK;YACvB,KAAKc,qBAAqBpH,YAA1B,GAAyC;iBACpC,KAAKoH,qBAAqBpH,YAA1B;;cAEHyH,oBAAoB,IAAIb,mBAAJ;AAC1Ba,0BAAkBzC,MAAM,KAAKuC,iBAA7B;aACKH,qBAAqBpH,YAAAA,IAAgByH;eACnCA;MAPc,GAAA;AAUvBnB,qBAAeV,OAAO4B,eAAtB;;;EAIJT,UAAO;AACL7B,cAAc,KAAKqC,kBAAmBrC,GAAtC;SACKqC,kBAAmB5B,KAAK+B,YAAY;;AArGL;ACD1B,IAAOC,mBAAP,MAAuB;EASnCpb,YAAYqb,MAAc;SACnBA,OAAOA;;EAGdC,wBAAwB3f,UAAmC;SACpD0f,KAAKE,iBAAiB,aAAcC,SAAO;AAC9C7f,eAAS,KAAK8f,UAAUD,KAAmB,KAAKE,cAAvC,CAAD;IADV,CAAA;SAGKL,KAAKE,iBAAiB,cAAeC,SAAO;AAC/C7f,eAAS,KAAK8f,UAAUD,KAAmB,KAAKG,cAAvC,CAAD;IADV,CAAA;;EAKFC,uBAAuBjgB,UAAmC;SACnD0f,KAAKE,iBAAiB,aAAcC,SAAO;AAC9C7f,eAAS,KAAK8f,UAAUD,KAAmB,KAAKE,cAAvC,CAAD;IADV,CAAA;SAGKL,KAAKE,iBAAiB,aAAcC,SAAO;AAC9C7f,eAAS,KAAK8f,UAAUD,KAAmB,KAAKG,cAAvC,CAAD;IADV,CAAA;;EAKFE,sBAAsBlgB,UAAoB;AAExCob,aAASwE,iBAAiB,WAAW5f,QAArC;AACAob,aAASwE,iBAAiB,YAAY5f,QAAtC;;EAGFmgB,wBAAqB;WACZ,KAAKT,KAAKS,sBAAV;;EAGTC,iBAAiB1Q,OAAwBC,QAAuB;SACzD+P,KAAKW,aAAa,SAAA,GAAY3Q,KAAAA,EAAAA;SAC9BgQ,KAAKW,aAAa,UAAA,GAAa1Q,MAAAA,EAAAA;;EAGtCmQ,UAAgCD,KAAaS,WAAmC;WACvE;MACLC,UAAU,MAAMD,UAAUE,KAAK,MAAMX,GAArB;MAChBY,gBAAgB,MAAMZ,IAAIY,eAAJ;IAFjB;;EAMTV,eAAeF,KAAe;UACtB;MAAEa;MAAMC;IAAR,IAAgB,KAAKR,sBAAL;UAChB3X,IAAIqX,IAAIe,UAAUF;UAClBjY,IAAIoX,IAAIgB,UAAUF;WACjB;MAAEnY;MAAGC;IAAL;;EAGTuX,eAAeH,KAAe;UACtB;MAAEa;MAAMC;IAAR,IAAgB,KAAKR,sBAAL;UAChB3X,IAAIqX,IAAIiB,QAAQ,CAAZ,EAAeF,UAAUF;UAC7BjY,IAAIoX,IAAIiB,QAAQ,CAAZ,EAAeD,UAAUF;WAC5B;MAAEnY;MAAGC;IAAL;;AAhE0B;ACLvB,IAAOsY,eAAP,MAAOA,sBAAqBtB,iBAA4C;EAmCpFpb,YAAY2Y,KAAiCS,MAAgB;UACrDT,GAAAA;SAEDA,MAAMA;SACNS,OAAOA;QAER,oBAAoBT,KAAK;WACtBgE,MAAMhE,IAAIiE,eAAJ;;;SAzCRC,KAAKC,SAA2BzR,QAAQ,QAAQC,SAAS,QAAM;UAC9DyR,WAAW,MAAK;UAChB,OAAOD,YAAY,UAAU;eACxB/F,SAASiG,eAAeF,OAAxB;;aAEFA;IAJO,GAAA;QAOZ,CAACC,SAAS;YACN,IAAI9d,MAAJ,yCAAmD6d,OAAAA,EAAnD;;UAEFG,WAAWF,QAAQG,SAASjf,YAAjB;UAEX0a,OAAO,MAAK;UACZsE,aAAa,SAASA,aAAa,KAAK;eACnCF;MADT,OAEO;cACCpE,OAAM9B,UAAU,KAAD;AACrBkG,gBAAQ5D,YAAYR,IAApB;eACOA;;IANC,GAAA;AAUZrB,UAAMqB,KAAK;MAAEtN;MAAOC;IAAT,CAAN;UACC8N,OAAOvC,UAAU,MAAD;AACtB8B,QAAIQ,YAAYC,IAAhB;WAEO,IAAIsD,cAAa/D,KAAKS,IAAtB;;EAkBTS,wBAAqB;UACbkB,QAAQlE,UAAU,GAAD;SAClB8B,IAAIQ,YAAY4B,KAAAA;WACd,IAAI2B,cAAa3B,OAAO,KAAK3B,IAA7B;;EAGTsC,eAAeF,KAAe;QACxB,KAAKmB,KAAK;WACPA,IAAIxY,IAAIqX,IAAIe;WACZI,IAAIvY,IAAIoX,IAAIgB;UACb,kBAAkB,KAAKnB,MAAM;;cACzB8B,UAAU,KAAKR,IAAIS,iBAAT,wBAAyB,KAAK/B,KAAKgC,aAAV,OAAA,QAAA,0BAAA,SAAA,SAAAC,sBAA0BC,QAA1B,CAAzB;eACT;UAAEpZ,GAAGgZ,QAAQhZ;UAAGC,GAAG+Y,QAAQ/Y;QAA3B;;;WAGJ,MAAMsX,eAAeS,KAAK,MAAMX,GAAhC;;EAGTG,eAAeH,KAAe;QACxB,KAAKmB,KAAK;WACPA,IAAIxY,IAAIqX,IAAIiB,QAAQ,CAAZ,EAAeF;WACvBI,IAAIvY,IAAIoX,IAAIiB,QAAQ,CAAZ,EAAeD;UACxB,kBAAkB,KAAKnB,MAAM;;cACzB8B,UAAU,KAAKR,IAAIS,iBAAT,yBACb,KAAK/B,KAAuBgC,aAA5B,OAAA,QAAA,2BAAA,SAAA,SAAAG,uBAA4CD,QAA5C,CADa;eAGT;UAAEpZ,GAAGgZ,QAAQhZ;UAAGC,GAAG+Y,QAAQ/Y;QAA3B;;;WAGJ,MAAMuX,eAAeH,GAArB;;AA3E2E;ACCtF,IAAA,cAAe;EACbf;EACAgD,oBAAoBf,aAAaG;AAFpB;ACFR,IAAMa,WAAW,CAACC,KAA+B1Y,WAAmB;AACzE0Y,MAAIC,UAAJ;QACM5U,QAAQ/D,OAAO,CAAD;QACdgE,kBAAkBhE,OAAOvG,MAAM,CAAb;AACxBif,MAAIE,OAAO7U,MAAM7E,GAAG6E,MAAM5E,CAA1B;aACWE,SAAS2E,iBAAiB;AACnC0U,QAAIG,OAAOxZ,MAAMH,GAAGG,MAAMF,CAA1B;;AAEFuZ,MAAInQ,OAAJ;AARK;AAiBA,IAAMuQ,qBAAsB7U,gBAAsB;QACjD8U,YAAY9U,WAAWhM,MAAM,kBAAjB,EAAqC2F,OAAQob,UAASA,SAAS,GAA/D;QACZC,WAAW,CAAEP,SAAkCA,IAAIC,UAAJ,CAApC;aACNK,QAAQD,WAAW;UACtB,CAACG,KAAK,GAAGC,SAAT,IAAsBH,KAAK/gB,MAAM,KAAX;UACtBmhB,SAASD,UAAUtc,IAAKwc,WAAUtf,WAAWsf,KAAD,CAAnC;QACXH,QAAQ,KAAK;AACfD,eAASvb,KAAMgb,SAAQA,IAAIE,OAAO,GAAIQ,MAAf,CAAvB;IADF,WAEWF,QAAQ,KAAK;AACtBD,eAASvb,KAAMgb,SAAQA,IAAIG,OAAO,GAAIO,MAAf,CAAvB;IADK,WAEIF,QAAQ,KAAK;AACtBD,eAASvb,KAAMgb,SACbA,IAAIY,cAAc,GAAIF,MAAtB,CADF;IADK,WAIIF,QAAQ,KAAK;AACtBD,eAASvb,KAAMgb,SACbA,IAAIa,iBAAiB,GAAIH,MAAzB,CADF;IADK,MAIA;;SAIDV,SAAkCO,SAAS9a,QAAS+a,SAAQA,IAAIR,GAAD,CAA7B;AAtBrC;ACZO,IAAOpF,mBAAP,cAA8BL,mBAAkB;EAO5DlY,YAAYwN,QAAgBiR,YAAY,MAAI;UACpCjR,MAAAA;QAEFiR,aAAaC,QAAQ;WAClBC,UAAU,IAAID,OAAO,KAAKlR,OAAO/D,IAAvB;IADjB,OAEO;WACAmV,WAAWb,mBAAmB,KAAKvQ,OAAO/D,IAAb;;SAE/BoV,sBAAsBzV,YACzB,KAAKoE,OAAOvI,QACZiT,mBAAmBE,eAAe,CAFE;;EAMxCiB,OACEsE,KACArE,OAKC;QAEGA,MAAMvY,UAAU,MAAM;;;AAG1B4c,QAAImB,KAAJ;QAEI,KAAKH,SAAS;AAChBhB,UAAIoB,KAAK,KAAKJ,OAAd;IADF,OAEO;;6BACAC,cAAAA,QAAAA,mBAAAA,SAAAA,SAAAA,eAAAA,KAAAA,MAAWjB,GAAAA;AAEhBA,UAAIqB,cAAc;AAClBrB,UAAInQ,OAAJ;AACAmQ,UAAIoB,KAAJ;;UAGI;MAAEvgB;MAAGG;MAAGC;MAAGC;IAAX,IAAiB,KAAKyZ,UAAUgB,KAAf;UACjBhH,QAAQzT,MAAM,IAAN,OAAiBL,CAAAA,IAAKG,CAAAA,IAAKC,CAAAA,MAA3B,OAAyCJ,CAAAA,IAAKG,CAAAA,IAAKC,CAAAA,IAAKC,CAAAA;UAChEogB,aAAa,KAAK5G,qBAAqBiB,MAAMhY,cAAhC;AACnBqc,QAAIqB,cAAc1F,MAAMvY;AACxB4c,QAAIuB,cAAc5M;AAClBqL,QAAIwB,YAAY7M;AAChBqL,QAAIyB,YAAYlH,mBAAmBE;AACnCuF,QAAI0B,UAAU;AACd1B,QAAI2B,WAAW;AAGf3B,QAAI4B,YAAY,CAAC,KAAKpH,aAAa,KAAKA,WAAxB,GAAsC8G,UAAtD;AACAtB,QAAI6B,iBAAiBP;AACrBvB,aAASC,KAAK,KAAKkB,mBAAX;AAERlB,QAAI8B,QAAJ;;AA5D0D;ACFhD,IAAO/F,sBAAP,MAAwB;EAGpC1Z,YAAYC,WAAoB;SACzB0Z,mBAAmB1Z,UAAUgB,QAAQa,IAAK0L,YAAW,IAAI+K,iBAAe/K,MAAnB,CAAlC;;EAG1B6L,OACEsE,KACArE,OAKC;QAEGA,MAAMvY,UAAU,KAAM;UAEpB;MAAEA;MAASL;MAAaE;MAAcK;IAAtC,IAAkDqY;aAE/Cjc,IAAI,GAAGA,IAAI,KAAKsc,iBAAiB1d,QAAQoB,KAAK;WAChDsc,iBAAiBtc,CAAAA,EAAGgc,OAAOsE,KAAK;QACnCjd;QACAE;QACAG,SAASE,QAAQ5D,CAAD,EAAI0D,UAAUA;QAC9BO,gBAAgBL,QAAQ5D,CAAD,EAAIiE,kBAAkB;MAJV,CAAA;;;AArBL;ACFxB,SAAUoe,iBACtB/B,KACArE,OAKC;MAEGA,MAAMvY,UAAU,MAAM;;;QAGpB;IAAEA;IAASwZ;IAAa7Z;IAAauE;EAArC,IAAgDqU;QAChD;IAAE9a;IAAGG;IAAGC;IAAGC;EAAX,IAAiB6B;AAEvBid,MAAImB,KAAJ;AACAnB,MAAIqB,cAAcje;AAClB4c,MAAIyB,YAAY7E;AAChBoD,MAAIuB,cAAJ,QAA0B1gB,CAAAA,IAAKG,CAAAA,IAAKC,CAAAA,IAAKC,CAAAA;AACzC8e,MAAI0B,UAAU;AACd1B,MAAI2B,WAAW;AACf5B,WAASC,KAAK1Y,MAAN;AACR0Y,MAAI8B,QAAJ;;AChBY,IAAOhF,wBAAP,MAA0B;EAStCza,YAAYC,WAAsBgU,YAAsB;SAYxDuG,UAAU1a;SAXHuU,aAAapU;SACbqU,cAAcL;SACdyG,oBAAoB,IAAIhB,oBAAkBzZ,SAAtB;SACpB0a,uBAAuB,IAAIjB,oBAAkBzZ,SAAtB;SACvB2a,yBAAyB,IAAIlB,oBAAkBzZ,SAAtB;;EAGhCwY,MAAM7G,QAA0B;SACzB+N,UAAU/N;;EAKjBgO,gBAAgBpgB,IAA2C;UACnD;MAAE6L;MAAOC;MAAQrD;MAAO4D;MAASC;IAAjC,IAA6C,KAAKwI;UAClDqJ,MAAM,KAAKgC,QAASE,WAAd;AACZlC,QAAImC,UAAU,GAAG,GAAGzU,OAAOC,MAA3B;AACAqS,QAAImB,KAAJ;AACAnB,QAAIoC,UAAUlU,SAASP,SAASQ,OAAhC;AACA6R,QAAIqC,UAAU,GAAG,GAAG,GAAG,IAAI,GAAG,CAA9B;AACArC,QAAI1V,MAAMA,OAAOA,KAAjB;AACAzI,OAAGme,GAAD;AACFA,QAAI8B,QAAJ;QAEI9B,IAAIsC,MAAM;AAEZtC,UAAIsC,KAAJ;;;EAIJ5G,OAAOC,OAAwB;UACvB;MAAEpY;MAASJ;MAAMM;IAAjB,IAA+BkY,MAAMrZ;UACrC;MACJU;MACAD;MACAE;MACAC;MACAJ;MACAD;IANI,IAOF8Y,MAAMpZ;SAEL0f,gBAAiBjC,SAAO;WACtBhD,qBAAqBtB,OAAOsE,KAAK;QACpC5c,SAASG,QAAQH;QACjBE,SAASC,QAAQD;QACjBP,aAAaC;MAHuB,CAAA;WAKjC+Z,kBAAkBrB,OAAOsE,KAAK;QACjC5c,SAASD,KAAKC;QACdE,SAASH,KAAKG;QACdP;QACAE;MAJiC,CAAA;WAM9Bga,uBAAuBvB,OAAOsE,KAAK;QACtC5c,SAASK,UAAUL;QACnBE,SAASG,UAAUH;QACnBP,aAAaG;MAHyB,CAAA;YAMlCQ,cAAciY,MAAMjY,eAAe,CAAA;iBAE9BoS,gBAAgBpS,aAAa;cAChCgL,aAAahL,YAAYoS,YAAD;YAC1BpH,YAAY;gBACR4O,kBAAkB;YACtBV,aAAa/Z;YACbE,aAAaD;eACV4L;UAHmB;AAKxBqT,2BAAiB/B,KAAK1C,eAAN;;;IA5BtB,CAAA;;AAnDoC;ACP1B,IAAOyB,iBAAP,MAAOA,wBAAqBtB,iBAAmC;EAC3Epb,YAAYkgB,QAAyB;UAC7BA,MAAAA;;SAGDrD,KAAKC,SAAqCzR,QAAQ,QAAQC,SAAS,QAAM;UACxEyR,WAAW,MAAK;UAChB,OAAOD,YAAY,UAAU;eACxB/F,SAASiG,eAAeF,OAAxB;;aAEFA;IAJO,GAAA;QAOZ,CAACC,SAAS;YACN,IAAI9d,MAAJ,yCAAmD6d,OAAAA,EAAnD;;UAGFG,WAAWF,QAAQG,SAASjf,YAAjB;UAEXiiB,UAAU,MAAK;UACfjD,aAAa,UAAU;eAClBF;;YAEHmD,UAASnJ,SAASoJ,cAAc,QAAvB;AACfpD,cAAQ5D,YAAY+G,OAApB;aACOA;IANM,GAAA;AASfA,WAAOlE,aAAa,SAAS3Q,KAA7B;AACA6U,WAAOlE,aAAa,UAAU1Q,MAA9B;WAEO,IAAIoR,gBAAawD,MAAjB;;EAGTL,aAAU;WACD,KAAKxE,KAAKwE,WAAW,IAArB;;AAnCkE;ACE7E,IAAA,iBAAe;EACbpF,qBAAAA;EACAgD,oBAAoBf,eAAaG;AAFpB;ACFf,IAAMuD,UAAU;AAChB,IAAMC,iBAAkBC,UAAD,kDAC6BF,OAAAA,IAAWE,IAAAA;AAE/D,IAAMC,wBAAwB,CAC5BD,MACAE,QACAC,YACE;QAEIC,MAAM,IAAIC,eAAJ;MACRD,IAAIE,kBAAkB;AAExBF,QAAIE,iBAAiB,kBAArB;;AAEFF,MAAIG,KAAK,OAAOR,eAAeC,IAAD,GAAQ,IAAtC;AACAI,MAAII,UAAWC,WAAS;AACtBN,YAAQC,KAAKK,KAAN;EADT;AAGAL,MAAIM,qBAAqB,MAAK;QAExBN,IAAIO,eAAe,EAAG;QAEtBP,IAAIQ,WAAW,KAAK;AACtBV,aAAOW,KAAKC,MAAMV,IAAIW,YAAf,CAAD;IADR,WAEWX,IAAIQ,WAAW,KAAKT,SAAS;AACtCA,cAAQC,GAAD;;EAPX;AAUAA,MAAIY,KAAK,IAAT;AAzBF;ACHA,IAAMC,iBAAqC;EACzCC,gBAAgBjB;EAChBkB,qBAAqB;EACrBC,uBAAuB;EACvBvgB,aAAa;EACbH,eAAe;EACf2gB,UAAU;;EAIVtW,OAAO;EACPC,QAAQ;EACRF,SAAS;;EAITwW,sBAAsB;EACtBhN,oBAAoB;EACpB4B,yBAAyB;EACzBZ,sBAAsB;EACtB5C,qBAAqB;EACrBG,mBAAmB;;EAInBzS,aAAa;EACbE,cAAc;EACdC,gBAAgB;EAChBF,cAAc;EACdF,cAAc;;EAIduM,UAAU;EACV2I,qBAAqB;EACrBW,qBAAqB;EACrBC,wBAAwB;EACxBnB,8BAA8B;EAC9BD,wBAAwB;EACxBR,oBAAoB;EACpB/F,0BAA0B;;EAI1BrO,qBAAqB;EACrBC,cAAc;EACd+Z,aAAa;EACbsH,cAAc;EACdC,kBAAkB,CAAA;AAhDuB;ACC7B,IAAOC,iBAAP,MAAqB;EAYjC/hB,YAAYE,SAA8B;SAX1C8hB,eAAe;SACfC,aAAa;SAQbC,gBAAgB;SAGTzN,WAAWvU;;EAGlBiiB,eAAe7B,MAAc/iB,QAAa;UAElC6kB,iBAAkBC,UAAuB;UACzC9kB,WAAU,KAAKykB,cAAc;;+BAC1B1f,cAAAA,QAAAA,mBAAAA,SAAAA,SAAAA,eAAAA,KAAAA,MAAW+f,IAAAA;;IAFpB;UAKMC,gBAAiBC,YAA2B;UAC5ChlB,WAAU,KAAKykB,cAAc;;8BAC1BQ,aAAAA,QAAAA,kBAAAA,SAAAA,SAAAA,cAAAA,KAAAA,MAAUD,MAAAA;;IAFnB;UAMME,eAAe,KAAKhO,SAAS+M,eACjClB,MACA8B,gBACAE,aAHmB;QAMjBG,cAAc;UACZ,UAAUA,cAAc;AAC1BA,qBAAaxf,KAAKmf,cAAlB,EAAkCM,MAAMJ,aAAxC;MADF,OAEO;AACLF,uBAAeK,YAAD;;;;EAKpBE,uBAAoB;WACX,IAAI1gB,QACT,CACEC,SACA0gB,WACE;WACGtgB,WAAWJ;WACXsgB,UAAUI;IANZ,CAAA,EASJ3f,KAAMof,UAAuB;;WACvBJ,aAAa;sDACbxN,UAASiN,2BAAAA,QAAAA,0BAAAA,SAAAA,SAAAA,sBAAAA,KAAAA,gBAAwBW,IAAAA;aAC/BA;IAZJ,CAAA,EAcJK,MAAOH,YAAU;WACXN,aAAa;WACbC,gBAAgB;UAIjB,KAAKzN,SAASgN,qBAAqB;aAChChN,SAASgN,oBAAoBc,MAAAA;;MAPpB;UAYZA,kBAAkBtjB,OAAO;cACrBsjB;;YAGFM,MAAM,IAAI5jB,MAAJ,gCACsB,KAAK6jB,YAAAA,EAD3B;AAIZD,UAAIN,SAASA;YAEPM;IApCH,CAAA;;EAwCTE,aAAazC,MAAY;SAClBwC,eAAexC;UACd0C,UAAU,KAAKL,qBAAL;SACXT,gBAAgB;SAChBD,aAAa;SACbD;SACAG,eAAe7B,MAAM,KAAK0B,YAAAA;WACxBgB;;AA5FwB;AC4BrB,IAAOC,cAAP,MAAOA,aAAW;EAuE9BjjB,YAAY+c,SAA+B7c,UAAuC,CAAA,GAAE;UAC5E;MAAEua,qBAAAA;MAAqBgD;IAAvB,IACJvd,QAAQyhB,aAAa,WAAWuB,iBAAiBC;UAC7CrB,mBAAmB5hB,QAAQ4hB,oBAAoB,CAAA;SAEhDsB,YAAY;MACf3I,qBAAqBqH,iBAAiBrH,uBAAuBA;MAC7DgD,oBAAoBqE,iBAAiBrE,sBAAsBA;IAF5C;SAKZ7L,SAAS,KAAKwR,UAAU3F,mBAC3BV,SACA7c,QAAQmL,OACRnL,QAAQoL,MAHI;SAKTmJ,WAAW,KAAK4O,eAAenjB,OAApB;SACXojB,kBAAkB,IAAIvB,eAAe,KAAKtN,QAAxB;SAClB8O,gBAAAA;;;SA/DAC,OACLzG,SACA9c,WACAC,SAAqC;UAE/BujB,SAAS,IAAIR,aAAYlG,SAAS7c,OAAzB;AACfujB,WAAOC,aAAazjB,SAApB;WAEOwjB;;SAQFE,kBACL1jB,WACAC,UAA0C,CAAA,GAAE;UAEtC0jB,kBAAkB,MAAK;YACrB;QAAEN;QAAiBO;MAAnB,IAAuCZ;WACzCK,oBAAe,QAAfA,oBAAe,SAAf,SAAAA,gBAAiBR,kBAAiB7iB,aAAa4jB,oBAAoB3jB,SAAS;eACvEojB;;aAEF,IAAIvB,eAAe;QAAE,GAAGR;WAAmBrhB;MAAxB,CAAnB;IALc,GAAA;AAQvB+iB,iBAAYK,kBAAkBM;AAC9BX,iBAAYY,kBAAkB3jB;WACvB0jB,eAAeb,aAAa9iB,SAA5B;;SAGF6jB,oBAAoBzY,OAAeC,QAAgBF,UAAU,GAAC;UAC7D6I,aAAa,IAAI9I,WAAW;MAAEE;MAAOC;MAAQF;IAAjB,CAAf;WACZ;MACLjH,GAAG8P,WAAWpI;MACdzH,GAAG6P,WAAWnI;MACd7D,OAAOgM,WAAWhM;MAClB+X,WAAW9hB,KAAI;oBACD+V,WAAWpI,OAAAA,KAAYoI,WAAW3I,SAAS2I,WAAWnI,OAAAA;gBAC1DmI,WAAWhM,KAAAA,KAAU,KAAKgM,WAAWhM,KAAAA;OAFhC,EAGZ9I,QAAQ,QAAQ,GAHR;IAJN;;EA+BT6B,cACEd,UAGI,CAAA,GAAE;SAEDuU,SAASzT,gBAAgB;WACvB,KAAK+iB,UAAU,MAAA;;mCACpB,KAAKnT,kBAAAA,QAAAA,uBAAAA,SAAAA,SAALoT,mBACIriB,IACA2R,cACE,QACA,KAAKe,YACL,OAAOnU,QAAQ2P,aAAa,WACxB3P,QAAQ2P,WACR,KAAK4E,SAASG,kBALpB,CAFJ,EAUG3R,KAAMghB,SAAO;;+BACZ/jB,QAAQmW,gBAAAA,QAAAA,wBAAAA,SAAAA,SAAAA,oBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAZX,CAAA;IADK,CAAA;;EAkBThS,cACE/R,UAGI,CAAA,GAAE;SAEDuU,SAASzT,gBAAgB;WACvB,KAAK+iB,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAALsT,oBACIviB,IACA2R,cACE,QACA,KAAKe,YACL,OAAOnU,QAAQ2P,aAAa,WACxB3P,QAAQ2P,WACR,KAAK4E,SAASG,kBALpB,CAFJ,EAUG3R,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAZX,CAAA;IADK,CAAA;;EAkBTnR,iBACE5S,UAEI,CAAA,GAAE;SAEDikB,WAAAA;WAEE,KAAKJ,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAALwT,oBACIziB,IACA2R,iBACE,QACA,KAAKe,YACL,KAAKI,SAASG,oBACd,KAAKH,SAASmN,sBACd,KAAKnN,SAASzB,mBALhB,CAFJ,EAUG/P,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAZX,CAAA;IADK,CAAA;;EAkBTzR,cACE9I,WACAxJ,UAEI,CAAA,GAAE;SAEDikB,WAAAA;WACE,KAAKJ,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAALyT,oBACI1iB,IACA2R,oBACE,QACA,KAAKe,YACLnY,SAASwN,WAAW,KAAK2K,WAAYpT,QAAQhF,MAArC,GACR,KAAKwY,SAASmN,oBAJhB,CAFJ,EASG3e,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAXX,CAAA;IADK,CAAA;;EAiBT5R,gBACE3I,WACAxJ,UAEI,CAAA,GAAE;UAEA8iB,UAAU,MAAK;UACf,CAAC,KAAK3O,cAAc,CAAC,KAAKzD,cAAc;;;aAIrC,KAAKA,aACTjP,IACC2R,gBACElX,YAAY,KAAKiY,WAAWpT,SAASyI,SAA1B,GACX5L,kBAAkB,KAAK2W,SAAS5T,cAAf,GACjB,KAAK4T,SAASmB,oBAHhB,CAFG,EAQJ3S,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAVJ,CAAA;IALT;WAmBO,KAAKF,UAAUf,OAAf;;QAGHsB,yBAAsB;SACrBH,WAAAA;WACE,KAAKJ,UAAU,MACpB,KAAKnT,aAAcjP,IACjB2R,qBACE,QACA,KAAKe,YACL,KAAKI,SAASG,oBACd,KAAKH,SAASmN,sBACd,KAAKnN,SAASzB,qBACd,KAAKyB,SAAStB,iBANhB,GAQA;MAAE1Q,MAAM;IAAR,CATF,CADK;;EAeT8hB,iBAAc;WACL,KAAKR,UAAU,MAAA;;oCAAM,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAAL4T,oBAAmBrhB,SAAnB;IAArB,CAAA;;EAGTshB,kBAAe;WACN,KAAKV,UAAU,MAAA;;oCAAM,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAAL8T,oBAAmBnhB,UAAnB;IAArB,CAAA;;EAGTpC,YACEjB,UAGI,CAAA,GAAE;SAEDuU,SAAStT,cAAc;WACrB,KAAK4iB,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAAL+T,oBACIhjB,IACA2R,cACE,WACA,KAAKe,YACL,OAAOnU,QAAQ2P,aAAa,WACxB3P,QAAQ2P,WACR,KAAK4E,SAASG,kBALpB,CAFJ,EAUG3R,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAZX,CAAA;IADK,CAAA;;EAkBTW,YACE1kB,UAGI,CAAA,GAAE;SAEDuU,SAAStT,cAAc;WACrB,KAAK4iB,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAALiU,oBACIljB,IACA2R,cACE,WACA,KAAKe,YACL,OAAOnU,QAAQ2P,aAAa,WACxB3P,QAAQ2P,WACR,KAAK4E,SAASG,kBALpB,CAFJ,EAUG3R,KAAMghB,SAAO;;gCACZ/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAZX,CAAA;IADK,CAAA;;;EAmBTlI,iBAAiB;IAAE1Q;IAAOC;IAAQF;EAAjB,GAAqD;QAChEC,UAAU+E,OAAW,MAAKqE,SAASpJ,QAAQA;QAC3CC,WAAW8E,OAAW,MAAKqE,SAASnJ,SAASA;QAC7CF,YAAYgF,OAAW,MAAKqE,SAASrJ,UAAUA;SAC9CwG,OAAOmK,iBAAiB,KAAKtH,SAASpJ,OAAO,KAAKoJ,SAASnJ,MAAAA;QAG9D,KAAK+I,cACL,KAAKzD,gBACL,KAAKkU,wBACL,KAAKxQ,aACL;WACKwQ,qBAAqBtK,QAAAA;YACpBuK,sBAAsB,KAAKC,iCAAiC,KAAK3Q,UAA3C;WAEvBzD,aAAarP,uBAAwBG,eACxCqjB,oBAAoB1L,OAAO3X,SAA3B,CAAA;AAEFqjB,0BAAoB1L,OAAO,KAAKzI,aAAatQ,KAA7C;UAEI,KAAK2kB,OAAO;aACTA,MAAM/P,cAAc,KAAKZ,WAAAA;;;;EAKpCpC,YACEC,WACAC,UACAlS,UAGI,CAAA,GAAE;;QAEF0B,YAA+B,CAAA;UAE7BsjB,iBAAiB,MAAK;UAEtB/S,cAAc,kBAAkB,CAACC,UAAU;eACtC,KAAKqC,SAAS/T;;aAEhB0R;IALa,GAAA;UAQhB+S,cAAcrnB,kBAAkBonB,aAAD;SAEhCzQ,SAAStC,SAAAA,IAAaC;UAErBvC,YAAQ,oBAAG3P,QAAQ2P,cAAAA,QAAAA,sBAAAA,SAAAA,oBAAY,KAAK4E,SAASG;AAEnDhT,gBAAYA,UAAUqR,OACpBK,YAA6BnB,WAAWgT,aAAatV,QAArD,CADU;QAKRsC,cAAc,kBAAkB,CAACC,UAAU;AAC7CxQ,kBAAYA,UAAUqR,OAAOK,YAA6BnB,WAAW,MAAM,CAA9C,CAAjB;;WAGP,KAAK4R,UAAU,MAAA;;oCACpB,KAAKnT,kBAAAA,QAAAA,wBAAAA,SAAAA,SAALwU,oBAAmBzjB,IAAIC,SAAvB,EAAkCqB,KAAMghB,SAAO;;gCAC7C/jB,QAAQmW,gBAAAA,QAAAA,yBAAAA,SAAAA,SAAAA,qBAAAA,KAARnW,SAAqB+jB,GAAAA;eACdA;MAFT,CAAA;IADK,CAAA;;EAQToB,KAAKC,cAAoC,CAAA,GAAE;WAClC,KAAKvB,UAAU,YAAW;UAC3B,KAAK1P,cAAc,KAAKzD,gBAAgB,KAAK0D,aAAa;aACvD6P,WAAAA;aACAc,QAAQ,IAAIjR,KAAK,KAAKK,YAAY,KAAKzD,cAAc,KAAK0D,WAAlD;aACRG,WAAW;UACd,GAAG,KAAKA;aACL6Q;QAFW;aAIXL,MAAM7R,UAAU,KAAKqB,QAAAA;;IARvB,CAAA;;EAaT8Q,iBAAc;QACR,KAAKN,OAAO;WACTA,MAAM7O,WAAAA;;;EAIf+N,aAAU;QACJ,KAAKc,OAAO;WACTA,MAAMlhB,OAAAA;WACNkhB,QAAQ7U;;;EAIjBsT,aAAapD,MAAY;SAClB6D,WAAAA;SACAqB,QAAQlF;QACT,KAAKwE,sBAAsB;WACxBA,qBAAqBtK,QAAAA;;QAExB,KAAK5J,cAAc;WAChBA,aAAa9M,UAAAA;;SAEfghB,uBAAuB;SACvBW,mBAAmB,KAAKnC,gBAC1BP,aAAazC,IADQ,EAErBrd,KAAMyiB,iBAAe;UAEhB,CAACA,eAAe,KAAKpC,gBAAgBpB,eAAe;;;WAInD7N,aAAazJ,cAAc0V,MAAMoF,WAAP;WAC1B9U,eAAe,IAAI7Q,YAAY,KAAKsU,YAAY,KAAKI,UAAW/S,eACnEqjB,oBAAoB1L,OAAO3X,SAA3B,CADkB;YAIdqjB,sBAAsB,KAAKC,iCAC/B,KAAK3Q,UADqB;AAG5B0Q,0BAAoB1L,OAAO,KAAKzI,aAAatQ,KAA7C;IAhBoB,CAAA;WAkBjB,KAAKmlB;;EAGdT,iCAAiC/kB,WAAoB;UAC7C;MAAEoL;MAAOC;MAAQF;IAAjB,IAA6B,KAAKqJ;SACnCH,cAAc,IAAInJ,WAAW;MAAEE;MAAOC;MAAQF;IAAjB,CAAf;UACb2Z,sBAAsB,IAAI,KAAK3B,UAAU3I,oBAC7Cxa,WACA,KAAKqU,WAFqB;AAI5ByQ,wBAAoBtM,MAAM,KAAK7G,MAA/B;SACKkT,uBAAuBC;WACrBA;;QAGHY,mBAAgB;QAChB,CAAC,KAAKH,OAAO;YACT,IAAIvmB,MAAM,iEAAV;;UAEFgB,YAAY,MAAM,KAAK8jB,UAAU,MAAM,KAAK1P,UAA1B;WACjBpU;;EAGTojB,eAAenjB,SAAoC;UAC3C0lB,gBAAgB;MACpB,GAAGrE;SACArhB;IAFiB;QAMlBA,QAAQ2lB,2BAA2B,CAAC3lB,QAAQ0hB,sBAAsB;AACpEgE,oBAAchE,uBAAuB,MAAM1hB,QAAQ2lB;;QAEjD3lB,QAAQsW,2BAA2B,CAACtW,QAAQ0V,sBAAsB;AACpEgQ,oBAAchQ,uBAAuB,MAAMgQ,cAAcpP;;QAGvD,CAACtW,QAAQqW,wBAAwB;AACnCqP,oBAAcrP,yBAAyBqP,cAAc/kB;;WAGhD,KAAKilB,oBAAoBF,aAAzB;;;EAITE,oBAAoB5lB,SAA2B;UACvC6lB,aAAa;MAAE,GAAG7lB;IAAL;QACf6lB,WAAW1a,SAAS,CAAC0a,WAAWza,QAAQ;AAC1Cya,iBAAWza,SAASya,WAAW1a;IADjC,WAEW0a,WAAWza,UAAU,CAACya,WAAW1a,OAAO;AACjD0a,iBAAW1a,QAAQ0a,WAAWza;IADzB,WAEI,CAACya,WAAW1a,SAAS,CAAC0a,WAAWza,QAAQ;YAC5C;QAAED;QAAOC;MAAT,IAAoB,KAAKsG,OAAOkK,sBAAZ;YACpBkK,SAASzhB,KAAK8B,IAAIgF,OAAOC,MAAhB;AACfya,iBAAW1a,QAAQ2a;AACnBD,iBAAWza,SAAS0a;;WAEfD;;EAGThC,UAAakC,MAAa;QAEpB,KAAK3C,gBAAgBpB,eAAe;YAChCjjB,MAAM,iEAAD;;QAGT,KAAKwmB,kBAAkB;aAClB,KAAKA,iBAAiBxiB,KAAK,MAAK;YACjC,CAAC,KAAKqgB,gBAAgBpB,eAAe;iBAChC+D,KAAI;;MAFR,CAAA;;WAMFhkB,QAAQC,QAAR,EAAkBe,KAAKgjB,IAAvB;;EAGT1C,kBAAe;SACR3R,OAAO0J,wBAAyBE,SAAO;UACtC,KAAKyJ,OAAO;AACdzJ,YAAIY,eAAJ;aACK6I,MAAM1R,gBAAgBiI,IAAIU,SAAJ,CAAA;;IAH/B,CAAA;SAMKtK,OAAOgK,uBAAwBJ,SAAO;UACrC,KAAKyJ,OAAO;AACdzJ,YAAIY,eAAJ;aACK6I,MAAMjQ,mBAAmBwG,IAAIU,SAAJ,CAAA;;IAHlC,CAAA;SAMKtK,OAAOiK,sBAAsB,MAAK;;0BAChCoJ,WAAAA,QAAAA,gBAAAA,SAAAA,SAAAA,YAAOnQ,cAAAA;IADd,CAAA;;AAzf4B;AAqCvBmO,YAAAK,kBAAyC;AAEzCL,YAAAY,kBAAsD;;", "names": ["globalObj", "window", "global", "performanceNow", "performance", "now", "Date", "requestAnimationFrame", "bind", "callback", "setTimeout", "cancelAnimationFrame", "clearTimeout", "arrLast", "arr", "length", "fixIndex", "index", "selectIndex", "copyAndMergeDeep", "base", "override", "output", "key", "baseVal", "overrideVal", "Array", "isArray", "inflate", "scope", "obj", "parts", "split", "final", "current", "i", "cap", "count", "counter", "average", "sum", "reduce", "acc", "val", "colorStringToVals", "colorString", "normalizedColor", "toUpperCase", "trim", "test", "hexParts", "substring", "hexStr", "join", "r", "parseInt", "slice", "g", "b", "a", "rgbMatch", "match", "parseFloat", "Error", "string", "replace", "objRepeat", "item", "times", "objRepeatCb", "cb", "ua", "navigator", "userAgent", "isMs<PERSON><PERSON>er", "indexOf", "noop", "RenderState", "constructor", "character", "options", "onStateChange", "_<PERSON><PERSON><PERSON><PERSON>", "_onStateChange", "state", "drawingFadeDuration", "drawing<PERSON><PERSON><PERSON>", "drawingColor", "strokeColor", "outlineColor", "radicalColor", "highlightColor", "main", "opacity", "showCharacter", "strokes", "outline", "showOutline", "highlight", "userStrokes", "displayPortion", "overwriteOnStateChange", "updateState", "stateChanges", "nextState", "run", "mutations", "scopes", "map", "mut", "cancelMutations", "Promise", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "_isActive", "_index", "_resolve", "_mutations", "_loop", "loop", "_scopes", "push", "_run", "filter", "chain", "canceled", "activeMutation", "then", "_getActiveMutations", "pauseAll", "for<PERSON>ach", "mutation", "pause", "resumeAll", "resume", "scopesToCancel", "chainId", "scopeToCancel", "startsWith", "_cancelMutationChain", "cancelAll", "cancel", "subtract", "p1", "p2", "x", "y", "magnitude", "point", "Math", "sqrt", "pow", "distance", "point1", "point2", "equals", "round", "precision", "multiplier", "points", "lastPoint", "pointsSansFirst", "dist", "cosineSimilarity", "rawDotProduct", "_extendPointOnLine", "vect", "norm", "frechetDist", "curve1", "curve2", "longCurve", "shortCurve", "calcVal", "j", "prevResultsCol", "curResultsCol", "max", "lastResult", "min", "subdivideCurve", "curve", "maxLen", "newCurve", "prevPoint", "segLen", "numNewPoints", "ceil", "newSegLen", "outlineCurve", "numPoints", "curveLen", "segmentLen", "outlinePoints", "endPoint", "remainingCurvePoints", "remainingDist", "outlinePointFound", "nextPointDist", "shift", "nextPoint", "normalizeCurve", "outlinedCurve", "meanX", "meanY", "mean", "translatedCurve", "scale", "scaledCurve", "rotate", "theta", "cos", "sin", "_filterParallelPoints", "filteredPoints", "numFilteredPoints", "curVect", "prevVect", "isParallel", "pop", "getPathString", "close", "start", "remainingPoints", "pathString", "roundedPoint", "extendStart", "newStart", "extendedPoints", "unshift", "Stroke", "path", "strokeNum", "isInRadical", "getStartingPoint", "getEndingPoint", "<PERSON><PERSON><PERSON><PERSON>", "getVectors", "vector", "getDistance", "distances", "strokePoint", "getAverageDistance", "totalDist", "Character", "symbol", "generateStrokes", "radStrokes", "medians", "pointData", "parseCharData", "char<PERSON><PERSON>", "CHARACTER_BOUNDS", "from", "to", "preScaledWidth", "preScaledHeight", "Positioner", "padding", "width", "height", "effectiveWidth", "effectiveHeight", "scaleX", "scaleY", "xCenteringBuffer", "yCenteringBuffer", "xOffset", "yOffset", "convertExternalPoint", "COSINE_SIMILARITY_THRESHOLD", "START_AND_END_DIST_THRESHOLD", "FRECHET_THRESHOLD", "MIN_LEN_THRESHOLD", "stroke<PERSON><PERSON><PERSON>", "userStroke", "stripDuplicates", "isMatch", "meta", "isStrokeBackwards", "avgDist", "getMatchData", "laterStrokes", "closestMatchDist", "checkBackwards", "leniencyAdjustment", "leniency", "startAndEndMatches", "closestStroke", "startingDist", "endingDist", "getEdgeVectors", "vectors", "directionMatches", "stroke", "edgeVectors", "strokeVectors", "similarities", "edgeVector", "strokeSimilarities", "strokeVector", "avgSimilarity", "lengthMatches", "firstPoint", "rest", "dedupedPoints", "SHAPE_FIT_ROTATIONS", "PI", "shapeFit", "normCurve1", "normCurve2", "minDist", "Infinity", "isOutlineVisible", "averageDistanceThreshold", "distMod", "withinDistThresh", "startAndEndMatch", "directionMatch", "shapeMatch", "lengthMatch", "backwardsMatchData", "reverse", "UserStroke", "id", "startingPoint", "startingExternalPoint", "externalPoints", "appendPoint", "externalPoint", "Delay", "duration", "_duration", "_startTime", "_paused", "_runningPromise", "_timeout", "elapsed<PERSON><PERSON><PERSON>", "undefined", "Mutation", "valuesOrCallable", "_tick", "timing", "_startPauseTime", "progress", "_pausedDuration", "_renderState", "_values", "_frame<PERSON>andle", "easedProgress", "ease", "getPartialV<PERSON>ues", "_startState", "_valuesOrCallable", "_force", "force", "renderState", "_inflateValues", "isAlreadyAtEnd", "values", "startValues", "endValues", "target", "endValue", "startValue", "showStrokes", "char<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "updateColor", "colorName", "colorVal", "highlightStroke", "color", "speed", "animateStroke", "animateSingleStroke", "mutationStateFunc", "curCharState", "mutationState", "showStroke", "animateCharacter", "fadeDuration", "delayBetweenStrokes", "concat", "animateCharacterLoop", "delayBetweenLoops", "startQuiz", "startStrokeNum", "characterActions", "startUserStroke", "updateUserStroke", "userStrokeId", "hideUserStroke", "removeAllUserStrokes", "userStrokeIds", "highlightCompleteChar", "getDrawnPath", "geometry", "Quiz", "positioner", "_currentStrokeIndex", "_mistakesOnStroke", "_totalMistakes", "_character", "_positioner", "_userStrokesIds", "quizActions", "_options", "startIndex", "quizStartStrokeNum", "strokeFadeDuration", "_userStroke", "endUserStroke", "strokeId", "continueUserStroke", "nextPoints", "setPositioner", "acceptBackwardsStrokes", "markStrokeCorrectAfterMisses", "currentStroke", "_getCurrentStroke", "isForceAccepted", "isAccepted", "_handleSuccess", "_handleFailure", "showHintAfterMisses", "strokeHighlightSpeed", "_getStrokeData", "isCorrect", "mistakesOnStroke", "totalMistakes", "strokesRemaining", "<PERSON><PERSON><PERSON>", "isBackwards", "nextStroke", "onComplete", "highlightOnComplete", "highlightCompleteColor", "strokeHighlightDuration", "animation", "isComplete", "onCorrectStroke", "onMistake", "createElm", "elmType", "document", "createElementNS", "attr", "elm", "name", "value", "setAttributeNS", "attrs", "attrsMap", "Object", "keys", "attrName", "urlIdRef", "prefix", "location", "href", "removeElm", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "StrokeRendererBase", "_pathLength", "STROKE_WIDTH", "_getStrokeDashoffset", "_getColor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_oldProps", "mount", "_animationPath", "svg", "_clip", "_strokePath", "maskId", "style", "extendedMaskPoints", "toString", "fill", "append<PERSON><PERSON><PERSON>", "defs", "render", "props", "_this$_oldProps", "strokeDashoffset", "_this$_oldProps2", "<PERSON><PERSON><PERSON><PERSON>", "_strokeRenderers", "subTarget", "createSubRenderTarget", "_group", "<PERSON><PERSON><PERSON><PERSON>", "display", "removeProperty", "colorsChanged", "_this$_oldProps3", "_this$_oldProps4", "User<PERSON><PERSON><PERSON><PERSON><PERSON>", "_path", "strokeWidth", "destroy", "HanziWriter<PERSON><PERSON><PERSON>", "_main<PERSON><PERSON><PERSON><PERSON><PERSON>", "_outline<PERSON><PERSON><PERSON><PERSON><PERSON>", "_highlight<PERSON><PERSON><PERSON><PERSON><PERSON>", "_userStrokeRenderers", "<PERSON><PERSON><PERSON><PERSON>", "group", "_positioned<PERSON><PERSON><PERSON>", "userStrokeProps", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "RenderTargetBase", "node", "addPointerStartListener", "addEventListener", "evt", "_eventify", "_getMousePoint", "_getTouchPoint", "addPointerMoveListener", "addPointerEndListener", "getBoundingClientRect", "updateDimensions", "setAttribute", "pointFunc", "getPoint", "call", "preventDefault", "left", "top", "clientX", "clientY", "touches", "RenderTarget", "_pt", "createSVGPoint", "init", "elmOrId", "element", "getElementById", "nodeType", "nodeName", "localPt", "matrixTransform", "getScreenCTM", "_this$node$getScreenC", "inverse", "_this$node$getScreenC2", "createRenderTarget", "drawPath", "ctx", "beginPath", "moveTo", "lineTo", "pathStringToCanvas", "pathParts", "part", "commands", "cmd", "rawParams", "params", "param", "bezierCurveTo", "quadraticCurveTo", "usePath2D", "Path2D", "_path2D", "_pathCmd", "_extendedMaskPoints", "save", "clip", "globalAlpha", "dashOffset", "strokeStyle", "fillStyle", "lineWidth", "lineCap", "lineJoin", "setLineDash", "lineDashOffset", "restore", "renderUserStroke", "_target", "_animationFrame", "getContext", "clearRect", "translate", "transform", "draw", "canvas", "createElement", "VERSION", "getCharDataUrl", "char", "defaultCharDataLoader", "onLoad", "onError", "xhr", "XMLHttpRequest", "overrideMimeType", "open", "onerror", "event", "onreadystatechange", "readyState", "status", "JSON", "parse", "responseText", "send", "defaultOptions", "<PERSON>ar<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoadCharDataError", "onLoadCharDataSuccess", "renderer", "strokeAnimationSpeed", "outlineWidth", "rendererOverride", "LoadingManager", "_loadCounter", "_isLoading", "loadingFailed", "_debouncedLoad", "wrappedResolve", "data", "wrappedReject", "reason", "_reject", "returnedData", "catch", "_setupLoadingPromise", "reject", "err", "_loadingChar", "loadCharData", "promise", "HanziWriter", "canvas<PERSON><PERSON><PERSON>", "svg<PERSON><PERSON><PERSON>", "_renderer", "_assignOptions", "_loadingManager", "_setupListeners", "create", "writer", "<PERSON><PERSON><PERSON><PERSON>", "loadCharacterData", "loadingManager", "_loadingOptions", "getScalingTransform", "_withData", "_this$_renderState", "res", "_this$_renderState2", "cancelQuiz", "_this$_renderState3", "_this$_renderState4", "loopCharacterAnimation", "pauseAnimation", "_this$_renderState5", "resumeAnimation", "_this$_renderState6", "_this$_renderState7", "hideOutline", "_this$_renderState8", "_hanziWriter<PERSON><PERSON><PERSON>", "hanziWriter<PERSON><PERSON><PERSON>", "_initAndMountHanziWriterRenderer", "_quiz", "fixedColorVal", "mappedColor", "_this$_renderState9", "quiz", "quizOptions", "skipQuizStroke", "_char", "_withDataPromise", "pathStrings", "getCharacterData", "mergedOptions", "strokeAnimationDuration", "_fillWidthAndHeight", "filledOpts", "minDim", "func"]}